/**
 * 虚拟键盘组件
 * 专为SSH终端优化的移动端虚拟键盘
 */

import React, { useState, useCallback, useMemo } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Vibration,
  Platform
} from 'react-native'
import { VirtualKeyboardConfig, CustomKey } from '../types'

interface VirtualKeyboardProps {
  config: VirtualKeyboardConfig
  onKeyPress: (key: string) => void
  onSpecialKey: (action: string) => void
  visible: boolean
  theme: 'dark' | 'light'
}

// 预定义键盘布局
const QWERTY_LAYOUT = [
  ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
  ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
  ['shift', 'z', 'x', 'c', 'v', 'b', 'n', 'm', 'backspace'],
  ['123', 'space', 'enter']
]

const COMPACT_LAYOUT = [
  ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
  ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
  ['z', 'x', 'c', 'v', 'b', 'n', 'm'],
  ['123', 'space', 'enter']
]

const NUMBER_LAYOUT = [
  ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
  ['-', '/', ':', ';', '(', ')', '$', '&', '@', '"'],
  ['#+=', '.', ',', '?', '!', "'", 'backspace'],
  ['ABC', 'space', 'enter']
]

const SYMBOL_LAYOUT = [
  ['[', ']', '{', '}', '#', '%', '^', '*', '+', '='],
  ['_', '\\', '|', '~', '<', '>', '€', '£', '¥', '•'],
  ['123', '.', ',', '?', '!', "'", 'backspace'],
  ['ABC', 'space', 'enter']
]

// SSH特殊键
const SSH_FUNCTION_KEYS = [
  { key: 'ctrl', label: 'Ctrl', width: 1 },
  { key: 'alt', label: 'Alt', width: 1 },
  { key: 'esc', label: 'Esc', width: 1 },
  { key: 'tab', label: 'Tab', width: 1.5 },
  { key: 'up', label: '↑', width: 1 },
  { key: 'down', label: '↓', width: 1 },
  { key: 'left', label: '←', width: 1 },
  { key: 'right', label: '→', width: 1 }
]

export const VirtualKeyboard: React.FC<VirtualKeyboardProps> = ({
  config,
  onKeyPress,
  onSpecialKey,
  visible,
  theme
}) => {
  const [currentLayout, setCurrentLayout] = useState<'qwerty' | 'compact' | 'number' | 'symbol'>('qwerty')
  const [isShiftPressed, setIsShiftPressed] = useState(false)
  const [isCtrlPressed, setIsCtrlPressed] = useState(false)
  const [isAltPressed, setIsAltPressed] = useState(false)
  const [showFunctionKeys, setShowFunctionKeys] = useState(config.showFunctionKeys)

  const screenWidth = Dimensions.get('window').width
  const keyboardHeight = config.keyHeight * 5 + config.keySpacing * 6

  // 获取当前键盘布局
  const getLayout = useMemo(() => {
    switch (currentLayout) {
      case 'compact':
        return COMPACT_LAYOUT
      case 'number':
        return NUMBER_LAYOUT
      case 'symbol':
        return SYMBOL_LAYOUT
      default:
        return config.layout === 'compact' ? COMPACT_LAYOUT : QWERTY_LAYOUT
    }
  }, [currentLayout, config.layout])

  // 处理按键点击
  const handleKeyPress = useCallback((key: string) => {
    // 触觉反馈
    if (Platform.OS === 'ios') {
      Vibration.vibrate(10)
    }

    // 处理特殊键
    switch (key) {
      case 'shift':
        setIsShiftPressed(!isShiftPressed)
        return
      
      case 'ctrl':
        setIsCtrlPressed(!isCtrlPressed)
        return
      
      case 'alt':
        setIsAltPressed(!isAltPressed)
        return
      
      case 'backspace':
        onSpecialKey('backspace')
        return
      
      case 'enter':
        onKeyPress('\n')
        resetModifiers()
        return
      
      case 'space':
        onKeyPress(' ')
        return
      
      case 'tab':
        onKeyPress('\t')
        return
      
      case 'esc':
        onKeyPress('\x1b')
        return
      
      case '123':
        setCurrentLayout('number')
        return
      
      case 'ABC':
        setCurrentLayout('qwerty')
        return
      
      case '#+=':
        setCurrentLayout('symbol')
        return
      
      case 'up':
        onKeyPress('\x1b[A')
        return
      
      case 'down':
        onKeyPress('\x1b[B')
        return
      
      case 'right':
        onKeyPress('\x1b[C')
        return
      
      case 'left':
        onKeyPress('\x1b[D')
        return
      
      default:
        // 处理普通字符
        let char = key
        
        // 应用修饰键
        if (isShiftPressed && char.length === 1) {
          char = char.toUpperCase()
        }
        
        if (isCtrlPressed) {
          // Ctrl+字符组合
          const ctrlChar = String.fromCharCode(char.toLowerCase().charCodeAt(0) - 96)
          onKeyPress(ctrlChar)
        } else if (isAltPressed) {
          // Alt+字符组合
          onKeyPress('\x1b' + char)
        } else {
          onKeyPress(char)
        }
        
        resetModifiers()
    }
  }, [isShiftPressed, isCtrlPressed, isAltPressed, onKeyPress, onSpecialKey])

  // 重置修饰键状态
  const resetModifiers = useCallback(() => {
    setIsShiftPressed(false)
    // Ctrl和Alt保持状态，方便连续使用
  }, [])

  // 渲染按键
  const renderKey = useCallback((key: string, index: number, rowIndex: number) => {
    const isModifier = ['shift', 'ctrl', 'alt'].includes(key)
    const isSpecial = ['backspace', 'enter', 'space', 'tab', 'esc'].includes(key)
    const isLayoutSwitch = ['123', 'ABC', '#+='].includes(key)
    const isArrow = ['up', 'down', 'left', 'right'].includes(key)
    
    // 计算按键宽度
    let keyWidth = (screenWidth - config.keySpacing * 11) / 10
    if (key === 'space') {
      keyWidth = keyWidth * 5
    } else if (key === 'shift' || key === 'backspace') {
      keyWidth = keyWidth * 1.5
    } else if (key === 'tab') {
      keyWidth = keyWidth * 1.5
    }

    // 按键状态
    const isPressed = (key === 'shift' && isShiftPressed) ||
                     (key === 'ctrl' && isCtrlPressed) ||
                     (key === 'alt' && isAltPressed)

    // 按键样式
    const keyStyle = [
      styles.key,
      {
        width: keyWidth,
        height: config.keyHeight,
        backgroundColor: isPressed ? theme === 'dark' ? '#4a90e2' : '#007aff' : 
                        isModifier ? theme === 'dark' ? '#3a3a3c' : '#e5e5ea' :
                        isSpecial ? theme === 'dark' ? '#2c2c2e' : '#d1d1d6' :
                        theme === 'dark' ? '#1c1c1e' : '#ffffff'
      }
    ]

    // 文字样式
    const textStyle = [
      styles.keyText,
      {
        color: isPressed ? '#ffffff' :
               theme === 'dark' ? '#ffffff' : '#000000',
        fontSize: isSpecial || isLayoutSwitch ? 12 : 16
      }
    ]

    // 按键标签
    let label = key
    if (key === 'backspace') label = '⌫'
    else if (key === 'enter') label = '↵'
    else if (key === 'space') label = 'Space'
    else if (key === 'shift') label = '⇧'
    else if (isShiftPressed && key.length === 1) label = key.toUpperCase()

    return (
      <TouchableOpacity
        key={`${rowIndex}-${index}`}
        style={keyStyle}
        onPress={() => handleKeyPress(key)}
        activeOpacity={0.7}
      >
        <Text style={textStyle}>{label}</Text>
      </TouchableOpacity>
    )
  }, [config, theme, isShiftPressed, isCtrlPressed, isAltPressed, handleKeyPress, screenWidth])

  // 渲染功能键行
  const renderFunctionKeys = useCallback(() => {
    if (!showFunctionKeys) return null

    return (
      <View style={styles.functionKeyRow}>
        {SSH_FUNCTION_KEYS.map((funcKey, index) => {
          const keyWidth = (screenWidth - config.keySpacing * 9) / 8 * funcKey.width
          const isPressed = (funcKey.key === 'ctrl' && isCtrlPressed) ||
                           (funcKey.key === 'alt' && isAltPressed)

          return (
            <TouchableOpacity
              key={index}
              style={[
                styles.functionKey,
                {
                  width: keyWidth,
                  height: config.keyHeight * 0.8,
                  backgroundColor: isPressed ? 
                    theme === 'dark' ? '#4a90e2' : '#007aff' :
                    theme === 'dark' ? '#2c2c2e' : '#d1d1d6'
                }
              ]}
              onPress={() => handleKeyPress(funcKey.key)}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.functionKeyText,
                {
                  color: isPressed ? '#ffffff' :
                         theme === 'dark' ? '#ffffff' : '#000000'
                }
              ]}>
                {funcKey.label}
              </Text>
            </TouchableOpacity>
          )
        })}
      </View>
    )
  }, [showFunctionKeys, config, theme, isCtrlPressed, isAltPressed, handleKeyPress, screenWidth])

  // 渲染自定义键
  const renderCustomKeys = useCallback(() => {
    if (!config.customKeys.length) return null

    return (
      <View style={styles.customKeyRow}>
        {config.customKeys.map((customKey, index) => {
          const keyWidth = customKey.width ? 
            (screenWidth - config.keySpacing * 11) / 10 * customKey.width :
            (screenWidth - config.keySpacing * 11) / 10

          return (
            <TouchableOpacity
              key={customKey.id}
              style={[
                styles.customKey,
                {
                  width: keyWidth,
                  height: config.keyHeight * 0.8,
                  backgroundColor: customKey.type === 'command' ?
                    theme === 'dark' ? '#ff9500' : '#ff9500' :
                    theme === 'dark' ? '#2c2c2e' : '#d1d1d6'
                }
              ]}
              onPress={() => {
                if (customKey.type === 'command') {
                  onSpecialKey(`custom:${customKey.value}`)
                } else {
                  onKeyPress(customKey.value)
                }
              }}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.customKeyText,
                {
                  color: customKey.type === 'command' ? '#ffffff' :
                         theme === 'dark' ? '#ffffff' : '#000000'
                }
              ]}>
                {customKey.label}
              </Text>
            </TouchableOpacity>
          )
        })}
      </View>
    )
  }, [config.customKeys, theme, onKeyPress, onSpecialKey, screenWidth])

  if (!visible) return null

  return (
    <View style={[
      styles.container,
      {
        height: keyboardHeight + (showFunctionKeys ? config.keyHeight * 0.8 + config.keySpacing : 0),
        backgroundColor: theme === 'dark' ? '#000000' : '#f2f2f7'
      }
    ]}>
      {renderFunctionKeys()}
      {renderCustomKeys()}
      
      {getLayout.map((row, rowIndex) => (
        <View key={rowIndex} style={styles.keyRow}>
          {row.map((key, keyIndex) => renderKey(key, keyIndex, rowIndex))}
        </View>
      ))}
      
      {/* 键盘控制按钮 */}
      <View style={styles.controlRow}>
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme === 'dark' ? '#2c2c2e' : '#d1d1d6' }]}
          onPress={() => setShowFunctionKeys(!showFunctionKeys)}
        >
          <Text style={[styles.controlButtonText, { color: theme === 'dark' ? '#ffffff' : '#000000' }]}>
            {showFunctionKeys ? '隐藏功能键' : '显示功能键'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme === 'dark' ? '#2c2c2e' : '#d1d1d6' }]}
          onPress={() => onSpecialKey('hideKeyboard')}
        >
          <Text style={[styles.controlButtonText, { color: theme === 'dark' ? '#ffffff' : '#000000' }]}>
            隐藏键盘
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 5,
    paddingVertical: 10
  },
  
  keyRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 5
  },
  
  functionKeyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingHorizontal: 5
  },
  
  customKeyRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 5,
    paddingHorizontal: 5
  },
  
  controlRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 5
  },
  
  key: {
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
    borderRadius: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2
  },
  
  functionKey: {
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 1,
    borderRadius: 4
  },
  
  customKey: {
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 1,
    borderRadius: 4
  },
  
  controlButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 15
  },
  
  keyText: {
    fontWeight: '500'
  },
  
  functionKeyText: {
    fontSize: 10,
    fontWeight: '500'
  },
  
  customKeyText: {
    fontSize: 10,
    fontWeight: '500'
  },
  
  controlButtonText: {
    fontSize: 12,
    fontWeight: '500'
  }
})

export default VirtualKeyboard
