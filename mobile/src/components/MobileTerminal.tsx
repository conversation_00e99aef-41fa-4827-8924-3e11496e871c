/**
 * 移动端终端组件
 * 优化的SSH终端界面，支持触摸操作和手势
 */

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { View, Text, ScrollView, TouchableOpacity, Dimensions, StyleSheet, SafeAreaView, StatusBar } from 'react-native'
import { PanGestureHandler, PinchGestureHandler, State } from 'react-native-gesture-handler'
import { TerminalSession, TerminalOutput, GestureConfig } from '../types'
import VirtualKeyboard from './VirtualKeyboard'

interface MobileTerminalProps {
  session: TerminalSession
  onInput: (data: string) => void
  onCommand: (command: string) => void
  gestureConfig: GestureConfig
  theme: 'dark' | 'light'
  fontSize: number
  fontFamily: string
}

export const MobileTerminal: React.FC<MobileTerminalProps> = ({ session, onInput, onCommand, gestureConfig, theme, fontSize, fontFamily }) => {
  const [showKeyboard, setShowKeyboard] = useState(false)
  const [scale, setScale] = useState(1)
  const [translateX, setTranslateX] = useState(0)
  const [translateY, setTranslateY] = useState(0)
  const [selectionStart, setSelectionStart] = useState<number | null>(null)
  const [selectionEnd, setSelectionEnd] = useState<number | null>(null)
  const [currentInput, setCurrentInput] = useState('')

  const scrollViewRef = useRef<ScrollView>(null)
  const terminalRef = useRef<View>(null)
  const lastTapRef = useRef<number>(0)

  const screenWidth = Dimensions.get('window').width
  const screenHeight = Dimensions.get('window').height

  // 自动滚动到底部
  useEffect(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true })
    }
  }, [session.output])

  // 处理键盘输入
  const handleKeyPress = useCallback(
    (key: string) => {
      if (key === '\n') {
        // 回车键 - 执行命令
        onCommand(currentInput)
        setCurrentInput('')
      } else {
        // 普通字符
        const newInput = currentInput + key
        setCurrentInput(newInput)
        onInput(key)
      }
    },
    [currentInput, onInput, onCommand]
  )

  // 处理特殊键
  const handleSpecialKey = useCallback(
    (action: string) => {
      switch (action) {
        case 'backspace':
          if (currentInput.length > 0) {
            const newInput = currentInput.slice(0, -1)
            setCurrentInput(newInput)
            onInput('\b')
          }
          break

        case 'hideKeyboard':
          setShowKeyboard(false)
          break

        case 'copy':
          if (selectionStart !== null && selectionEnd !== null) {
            // 复制选中文本
            const selectedText = getSelectedText()
            // 这里应该调用剪贴板API
            console.log('复制文本:', selectedText)
          }
          break

        case 'paste':
          // 粘贴剪贴板内容
          // 这里应该从剪贴板获取文本
          break

        case 'clear':
          onCommand('clear')
          break

        default:
          if (action.startsWith('custom:')) {
            const command = action.replace('custom:', '')
            onCommand(command)
          }
      }
    },
    [currentInput, selectionStart, selectionEnd, onInput, onCommand]
  )

  // 获取选中文本
  const getSelectedText = useCallback((): string => {
    if (selectionStart === null || selectionEnd === null) return ''

    const allText = session.output.map((output) => output.content).join('')
    return allText.slice(selectionStart, selectionEnd)
  }, [session.output, selectionStart, selectionEnd])

  // 处理单击
  const handleSingleTap = useCallback(() => {
    const now = Date.now()
    const timeDiff = now - lastTapRef.current

    if (timeDiff < 300) {
      // 双击
      handleDoubleTap()
    } else {
      // 单击 - 显示/隐藏键盘
      if (gestureConfig.doubleTap === 'showKeyboard') {
        setShowKeyboard(!showKeyboard)
      }
    }

    lastTapRef.current = now
  }, [showKeyboard, gestureConfig])

  // 处理双击
  const handleDoubleTap = useCallback(() => {
    switch (gestureConfig.doubleTap) {
      case 'showKeyboard':
        setShowKeyboard(!showKeyboard)
        break
      case 'copy':
        // 选择当前行
        break
      case 'paste':
        handleSpecialKey('paste')
        break
    }
  }, [showKeyboard, gestureConfig, handleSpecialKey])

  // 处理长按
  const handleLongPress = useCallback(() => {
    switch (gestureConfig.longPress) {
      case 'showShortcuts':
        // 显示快捷操作菜单
        break
      case 'copy':
        handleSpecialKey('copy')
        break
      case 'paste':
        handleSpecialKey('paste')
        break
    }
  }, [gestureConfig, handleSpecialKey])

  // 处理滑动手势
  const handleSwipe = useCallback(
    (direction: 'left' | 'right' | 'up' | 'down') => {
      const action = gestureConfig[`swipe${direction.charAt(0).toUpperCase() + direction.slice(1)}` as keyof GestureConfig]

      switch (action) {
        case 'showKeyboard':
          setShowKeyboard(true)
          break
        case 'hideKeyboard':
          setShowKeyboard(false)
          break
        case 'switchSession':
          // 切换会话
          break
        case 'newSession':
          // 新建会话
          break
        case 'closeSession':
          // 关闭会话
          break
      }
    },
    [gestureConfig]
  )

  // 处理缩放手势
  const handlePinchGesture = useCallback(
    (event: any) => {
      if (gestureConfig.pinchZoom) {
        const newScale = Math.max(0.5, Math.min(3, event.nativeEvent.scale))
        setScale(newScale)
      }
    },
    [gestureConfig.pinchZoom]
  )

  // 处理平移手势
  const handlePanGesture = useCallback(
    (event: any) => {
      if (scale > 1) {
        setTranslateX(event.nativeEvent.translationX)
        setTranslateY(event.nativeEvent.translationY)
      }
    },
    [scale]
  )

  // 渲染终端输出
  const renderOutput = useCallback(() => {
    return session.output.map((output, index) => {
      const isSelected = selectionStart !== null && selectionEnd !== null && index >= selectionStart && index <= selectionEnd

      return (
        <View
          key={output.id}
          style={styles.outputLine}
        >
          <Text
            style={[
              styles.outputText,
              {
                color:
                  output.type === 'error'
                    ? '#ff6b6b'
                    : output.type === 'input'
                      ? theme === 'dark'
                        ? '#4ecdc4'
                        : '#007aff'
                      : theme === 'dark'
                        ? '#ffffff'
                        : '#000000',
                fontSize: fontSize * scale,
                fontFamily,
                backgroundColor: isSelected ? (theme === 'dark' ? '#4a90e2' : '#007aff') : 'transparent'
              },
              output.formatting && {
                color: output.formatting.color,
                backgroundColor: output.formatting.backgroundColor,
                fontWeight: output.formatting.bold ? 'bold' : 'normal',
                fontStyle: output.formatting.italic ? 'italic' : 'normal'
              }
            ]}
            selectable={true}
            onSelectionChange={(event) => {
              setSelectionStart(event.nativeEvent.selection.start)
              setSelectionEnd(event.nativeEvent.selection.end)
            }}
          >
            {output.content}
          </Text>
        </View>
      )
    })
  }, [session.output, selectionStart, selectionEnd, theme, fontSize, scale, fontFamily])

  // 渲染当前输入行
  const renderCurrentInput = useCallback(() => {
    if (!currentInput) return null

    return (
      <View style={styles.inputLine}>
        <Text
          style={[
            styles.promptText,
            {
              color: theme === 'dark' ? '#4ecdc4' : '#007aff',
              fontSize: fontSize * scale,
              fontFamily
            }
          ]}
        >
          $
        </Text>
        <Text
          style={[
            styles.inputText,
            {
              color: theme === 'dark' ? '#ffffff' : '#000000',
              fontSize: fontSize * scale,
              fontFamily
            }
          ]}
        >
          {currentInput}
        </Text>
        <View
          style={[
            styles.cursor,
            {
              backgroundColor: theme === 'dark' ? '#ffffff' : '#000000'
            }
          ]}
        />
      </View>
    )
  }, [currentInput, theme, fontSize, scale, fontFamily])

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme === 'dark' ? '#000000' : '#ffffff' }]}>
      <StatusBar
        barStyle={theme === 'dark' ? 'light-content' : 'dark-content'}
        backgroundColor={theme === 'dark' ? '#000000' : '#ffffff'}
      />

      {/* 终端头部 */}
      <View style={[styles.header, { backgroundColor: theme === 'dark' ? '#1c1c1e' : '#f2f2f7' }]}>
        <Text style={[styles.headerTitle, { color: theme === 'dark' ? '#ffffff' : '#000000' }]}>{session.title}</Text>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowKeyboard(!showKeyboard)}
          >
            <Text style={[styles.headerButtonText, { color: theme === 'dark' ? '#ffffff' : '#000000' }]}>键盘</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => handleSpecialKey('clear')}
          >
            <Text style={[styles.headerButtonText, { color: theme === 'dark' ? '#ffffff' : '#000000' }]}>清屏</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* 终端内容区域 */}
      <PinchGestureHandler
        onGestureEvent={handlePinchGesture}
        onHandlerStateChange={(event) => {
          if (event.nativeEvent.state === State.END) {
            // 缩放结束，可以做一些清理工作
          }
        }}
      >
        <PanGestureHandler
          onGestureEvent={handlePanGesture}
          onHandlerStateChange={(event) => {
            if (event.nativeEvent.state === State.END) {
              // 检测滑动方向
              const { translationX, translationY, velocityX, velocityY } = event.nativeEvent

              if (Math.abs(velocityX) > Math.abs(velocityY)) {
                // 水平滑动
                if (velocityX > 500) {
                  handleSwipe('right')
                } else if (velocityX < -500) {
                  handleSwipe('left')
                }
              } else {
                // 垂直滑动
                if (velocityY > 500) {
                  handleSwipe('down')
                } else if (velocityY < -500) {
                  handleSwipe('up')
                }
              }

              // 重置平移
              setTranslateX(0)
              setTranslateY(0)
            }
          }}
        >
          <View style={styles.terminalContainer}>
            <ScrollView
              ref={scrollViewRef}
              style={styles.scrollView}
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
              onContentSizeChange={() => {
                scrollViewRef.current?.scrollToEnd({ animated: true })
              }}
            >
              <TouchableOpacity
                activeOpacity={1}
                onPress={handleSingleTap}
                onLongPress={handleLongPress}
                style={[
                  styles.terminalContent,
                  {
                    transform: [{ scale }, { translateX }, { translateY }]
                  }
                ]}
              >
                <View ref={terminalRef}>
                  {renderOutput()}
                  {renderCurrentInput()}
                </View>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </PanGestureHandler>
      </PinchGestureHandler>

      {/* 虚拟键盘 */}
      <VirtualKeyboard
        config={{
          layout: 'qwerty',
          showFunctionKeys: true,
          showArrowKeys: true,
          showNumberRow: true,
          customKeys: [
            { id: 'sudo', label: 'sudo', value: 'sudo ', type: 'command' },
            { id: 'ls', label: 'ls', value: 'ls -la', type: 'command' },
            { id: 'cd', label: 'cd', value: 'cd ', type: 'command' },
            { id: 'vim', label: 'vim', value: 'vim ', type: 'command' }
          ],
          keyHeight: 40,
          keySpacing: 5,
          backgroundColor: theme === 'dark' ? '#000000' : '#f2f2f7',
          keyColor: theme === 'dark' ? '#1c1c1e' : '#ffffff',
          textColor: theme === 'dark' ? '#ffffff' : '#000000'
        }}
        onKeyPress={handleKeyPress}
        onSpecialKey={handleSpecialKey}
        visible={showKeyboard}
        theme={theme}
      />
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0'
  },

  headerTitle: {
    fontSize: 16,
    fontWeight: '600'
  },

  headerActions: {
    flexDirection: 'row',
    gap: 12
  },

  headerButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(0, 0, 0, 0.1)'
  },

  headerButtonText: {
    fontSize: 12,
    fontWeight: '500'
  },

  terminalContainer: {
    flex: 1
  },

  scrollView: {
    flex: 1
  },

  scrollContent: {
    flexGrow: 1,
    padding: 12
  },

  terminalContent: {
    flex: 1
  },

  outputLine: {
    marginBottom: 2
  },

  outputText: {
    fontFamily: 'Menlo',
    lineHeight: 20
  },

  inputLine: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4
  },

  promptText: {
    fontFamily: 'Menlo',
    fontWeight: 'bold'
  },

  inputText: {
    fontFamily: 'Menlo',
    flex: 1
  },

  cursor: {
    width: 2,
    height: 16,
    marginLeft: 2
  }
})

export default MobileTerminal
