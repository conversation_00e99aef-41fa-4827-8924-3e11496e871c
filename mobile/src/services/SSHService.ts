/**
 * 移动端SSH服务
 * 提供SSH连接管理、终端操作和文件传输功能
 */

import { EventEmitter } from 'events'
import { MobileSSHConfig, ConnectionState, TerminalOutput, FileTransfer } from '../types'

export interface SSHConnection {
  id: string
  config: MobileSSHConfig
  isConnected: boolean
  lastActivity: Date
  
  // 连接方法
  connect(): Promise<void>
  disconnect(): Promise<void>
  
  // 终端操作
  executeCommand(command: string): Promise<string>
  sendInput(data: string): void
  
  // 文件操作
  uploadFile(localPath: string, remotePath: string): Promise<FileTransfer>
  downloadFile(remotePath: string, localPath: string): Promise<FileTransfer>
  
  // 事件监听
  on(event: string, listener: Function): void
  off(event: string, listener: Function): void
}

class MobileSSHConnection extends EventEmitter implements SSHConnection {
  public id: string
  public config: MobileSSHConfig
  public isConnected: boolean = false
  public lastActivity: Date = new Date()
  
  private connection: any // SSH2连接实例
  private keepAliveInterval?: NodeJS.Timeout
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 5
  
  constructor(config: MobileSSHConfig) {
    super()
    this.id = config.id
    this.config = config
  }
  
  /**
   * 建立SSH连接
   */
  async connect(): Promise<void> {
    try {
      this.emit('connecting')
      
      // 创建SSH连接
      const SSH2 = require('react-native-ssh2')
      this.connection = new SSH2()
      
      // 配置连接参数
      const connectionConfig = {
        host: this.config.host,
        port: this.config.port,
        username: this.config.username,
        password: this.config.password,
        privateKey: this.config.privateKey,
        passphrase: this.config.passphrase,
        keepaliveInterval: this.config.keepAlive ? 30000 : 0,
        readyTimeout: 20000,
        algorithms: {
          kex: ['diffie-hellman-group14-sha256', 'ecdh-sha2-nistp256'],
          cipher: ['aes128-ctr', 'aes192-ctr', 'aes256-ctr'],
          hmac: ['hmac-sha2-256', 'hmac-sha2-512'],
          compress: ['none']
        }
      }
      
      // 建立连接
      await new Promise<void>((resolve, reject) => {
        this.connection.connect(connectionConfig)
        
        this.connection.on('ready', () => {
          this.isConnected = true
          this.reconnectAttempts = 0
          this.lastActivity = new Date()
          
          // 启动保活机制
          if (this.config.keepAlive) {
            this.startKeepAlive()
          }
          
          this.emit('connected')
          resolve()
        })
        
        this.connection.on('error', (error: Error) => {
          this.isConnected = false
          this.emit('error', error)
          reject(error)
        })
        
        this.connection.on('close', () => {
          this.isConnected = false
          this.stopKeepAlive()
          this.emit('disconnected')
          
          // 自动重连
          if (this.config.autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        })
      })
      
    } catch (error) {
      this.isConnected = false
      this.emit('error', error)
      throw error
    }
  }
  
  /**
   * 断开SSH连接
   */
  async disconnect(): Promise<void> {
    try {
      this.stopKeepAlive()
      
      if (this.connection && this.isConnected) {
        this.connection.end()
      }
      
      this.isConnected = false
      this.emit('disconnected')
    } catch (error) {
      this.emit('error', error)
      throw error
    }
  }
  
  /**
   * 执行命令
   */
  async executeCommand(command: string): Promise<string> {
    if (!this.isConnected) {
      throw new Error('SSH连接未建立')
    }
    
    return new Promise((resolve, reject) => {
      this.connection.exec(command, (err: Error, stream: any) => {
        if (err) {
          reject(err)
          return
        }
        
        let output = ''
        let errorOutput = ''
        
        stream.on('data', (data: Buffer) => {
          const text = data.toString()
          output += text
          this.emit('output', {
            type: 'output',
            content: text,
            timestamp: new Date()
          })
        })
        
        stream.stderr.on('data', (data: Buffer) => {
          const text = data.toString()
          errorOutput += text
          this.emit('output', {
            type: 'error',
            content: text,
            timestamp: new Date()
          })
        })
        
        stream.on('close', (code: number) => {
          this.lastActivity = new Date()
          
          if (code === 0) {
            resolve(output)
          } else {
            reject(new Error(errorOutput || `命令执行失败，退出码: ${code}`))
          }
        })
      })
    })
  }
  
  /**
   * 发送输入数据
   */
  sendInput(data: string): void {
    if (!this.isConnected) {
      throw new Error('SSH连接未建立')
    }
    
    // 通过shell发送数据
    if (this.connection.shell) {
      this.connection.shell.write(data)
      this.lastActivity = new Date()
      
      this.emit('input', {
        type: 'input',
        content: data,
        timestamp: new Date()
      })
    }
  }
  
  /**
   * 上传文件
   */
  async uploadFile(localPath: string, remotePath: string): Promise<FileTransfer> {
    if (!this.isConnected) {
      throw new Error('SSH连接未建立')
    }
    
    const transfer: FileTransfer = {
      id: `upload_${Date.now()}`,
      connectionId: this.id,
      type: 'upload',
      localPath,
      remotePath,
      fileName: localPath.split('/').pop() || '',
      fileSize: 0,
      status: 'pending',
      progress: 0,
      speed: 0,
      startTime: new Date()
    }
    
    try {
      // 获取文件信息
      const fs = require('react-native-fs')
      const fileStats = await fs.stat(localPath)
      transfer.fileSize = fileStats.size
      
      // 开始传输
      transfer.status = 'transferring'
      this.emit('fileTransferStart', transfer)
      
      return new Promise((resolve, reject) => {
        this.connection.sftp((err: Error, sftp: any) => {
          if (err) {
            transfer.status = 'error'
            transfer.error = err.message
            reject(err)
            return
          }
          
          const readStream = fs.createReadStream(localPath)
          const writeStream = sftp.createWriteStream(remotePath)
          
          let transferredBytes = 0
          const startTime = Date.now()
          
          readStream.on('data', (chunk: Buffer) => {
            transferredBytes += chunk.length
            transfer.progress = (transferredBytes / transfer.fileSize) * 100
            transfer.speed = transferredBytes / ((Date.now() - startTime) / 1000)
            
            this.emit('fileTransferProgress', transfer)
          })
          
          writeStream.on('close', () => {
            transfer.status = 'completed'
            transfer.endTime = new Date()
            transfer.progress = 100
            
            this.emit('fileTransferComplete', transfer)
            resolve(transfer)
          })
          
          writeStream.on('error', (error: Error) => {
            transfer.status = 'error'
            transfer.error = error.message
            
            this.emit('fileTransferError', transfer)
            reject(error)
          })
          
          readStream.pipe(writeStream)
        })
      })
      
    } catch (error) {
      transfer.status = 'error'
      transfer.error = (error as Error).message
      this.emit('fileTransferError', transfer)
      throw error
    }
  }
  
  /**
   * 下载文件
   */
  async downloadFile(remotePath: string, localPath: string): Promise<FileTransfer> {
    if (!this.isConnected) {
      throw new Error('SSH连接未建立')
    }
    
    const transfer: FileTransfer = {
      id: `download_${Date.now()}`,
      connectionId: this.id,
      type: 'download',
      localPath,
      remotePath,
      fileName: remotePath.split('/').pop() || '',
      fileSize: 0,
      status: 'pending',
      progress: 0,
      speed: 0,
      startTime: new Date()
    }
    
    try {
      transfer.status = 'transferring'
      this.emit('fileTransferStart', transfer)
      
      return new Promise((resolve, reject) => {
        this.connection.sftp((err: Error, sftp: any) => {
          if (err) {
            transfer.status = 'error'
            transfer.error = err.message
            reject(err)
            return
          }
          
          // 获取远程文件信息
          sftp.stat(remotePath, (statErr: Error, stats: any) => {
            if (statErr) {
              transfer.status = 'error'
              transfer.error = statErr.message
              reject(statErr)
              return
            }
            
            transfer.fileSize = stats.size
            
            const readStream = sftp.createReadStream(remotePath)
            const fs = require('react-native-fs')
            const writeStream = fs.createWriteStream(localPath)
            
            let transferredBytes = 0
            const startTime = Date.now()
            
            readStream.on('data', (chunk: Buffer) => {
              transferredBytes += chunk.length
              transfer.progress = (transferredBytes / transfer.fileSize) * 100
              transfer.speed = transferredBytes / ((Date.now() - startTime) / 1000)
              
              this.emit('fileTransferProgress', transfer)
            })
            
            writeStream.on('close', () => {
              transfer.status = 'completed'
              transfer.endTime = new Date()
              transfer.progress = 100
              
              this.emit('fileTransferComplete', transfer)
              resolve(transfer)
            })
            
            writeStream.on('error', (error: Error) => {
              transfer.status = 'error'
              transfer.error = error.message
              
              this.emit('fileTransferError', transfer)
              reject(error)
            })
            
            readStream.pipe(writeStream)
          })
        })
      })
      
    } catch (error) {
      transfer.status = 'error'
      transfer.error = (error as Error).message
      this.emit('fileTransferError', transfer)
      throw error
    }
  }
  
  /**
   * 启动保活机制
   */
  private startKeepAlive(): void {
    this.keepAliveInterval = setInterval(() => {
      if (this.isConnected) {
        // 发送保活包
        this.executeCommand('echo "keepalive"').catch(() => {
          // 保活失败，可能连接已断开
          this.emit('keepAliveError')
        })
      }
    }, 30000)
  }
  
  /**
   * 停止保活机制
   */
  private stopKeepAlive(): void {
    if (this.keepAliveInterval) {
      clearInterval(this.keepAliveInterval)
      this.keepAliveInterval = undefined
    }
  }
  
  /**
   * 计划重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000)
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.connect().catch((error) => {
          this.emit('reconnectFailed', error)
        })
      }
    }, delay)
  }
}

/**
 * SSH服务管理器
 */
export class SSHService {
  private connections = new Map<string, MobileSSHConnection>()
  private eventEmitter = new EventEmitter()
  
  /**
   * 创建SSH连接
   */
  createConnection(config: MobileSSHConfig): SSHConnection {
    const connection = new MobileSSHConnection(config)
    this.connections.set(config.id, connection)
    
    // 转发连接事件
    connection.on('connected', () => {
      this.eventEmitter.emit('connectionStateChanged', {
        connectionId: config.id,
        state: 'connected'
      })
    })
    
    connection.on('disconnected', () => {
      this.eventEmitter.emit('connectionStateChanged', {
        connectionId: config.id,
        state: 'disconnected'
      })
    })
    
    connection.on('error', (error) => {
      this.eventEmitter.emit('connectionError', {
        connectionId: config.id,
        error
      })
    })
    
    return connection
  }
  
  /**
   * 获取连接
   */
  getConnection(connectionId: string): SSHConnection | undefined {
    return this.connections.get(connectionId)
  }
  
  /**
   * 移除连接
   */
  async removeConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId)
    if (connection) {
      await connection.disconnect()
      this.connections.delete(connectionId)
    }
  }
  
  /**
   * 获取所有连接
   */
  getAllConnections(): SSHConnection[] {
    return Array.from(this.connections.values())
  }
  
  /**
   * 监听事件
   */
  on(event: string, listener: Function): void {
    this.eventEmitter.on(event, listener)
  }
  
  /**
   * 移除事件监听
   */
  off(event: string, listener: Function): void {
    this.eventEmitter.off(event, listener)
  }
  
  /**
   * 清理所有连接
   */
  async cleanup(): Promise<void> {
    const disconnectPromises = Array.from(this.connections.values()).map(
      connection => connection.disconnect()
    )
    
    await Promise.all(disconnectPromises)
    this.connections.clear()
  }
}

}

// 单例实例
export const sshService = new SSHService()
