{"name": "chaterm-mobile", "version": "1.0.0", "description": "Chaterm Mobile - Cross-platform SSH Terminal Manager", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace ChartermMobile.xcworkspace -scheme ChartermMobile -configuration Release archive", "clean": "react-native clean", "pod-install": "cd ios && pod install"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.0", "@react-native-community/netinfo": "^9.4.0", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "@react-navigation/bottom-tabs": "^6.5.0", "react": "18.2.0", "react-native": "0.72.0", "react-native-background-job": "^1.2.0", "react-native-gesture-handler": "^2.12.0", "react-native-keychain": "^8.1.0", "react-native-orientation-locker": "^1.5.0", "react-native-reanimated": "^3.3.0", "react-native-safe-area-context": "^4.7.0", "react-native-screens": "^3.22.0", "react-native-ssh2": "^1.0.0", "react-native-super-grid": "^4.9.0", "react-native-vector-icons": "^10.0.0", "react-native-webview": "^13.2.0", "react-redux": "^8.1.0", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.72.0", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.5", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["ssh", "terminal", "mobile", "react-native", "cross-platform"], "author": "Chaterm Team", "license": "MIT"}