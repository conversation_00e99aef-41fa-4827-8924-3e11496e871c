appId: ai.chaterm.app
productName: RS-Chaterm
compression: maximum
directories:
  buildResources: build
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
  - '!node_modules/**/*.ts'
  - '!node_modules/**/*.map'
  - '!node_modules/**/test/**'
  - '!node_modules/**/tests/**'
  - '!node_modules/**/docs/**'
  - '!node_modules/**/documentation/**'
  - '!node_modules/**/examples/**'
  - '!node_modules/**/.github/**'
  - '!node_modules/**/README.md'
  - '!node_modules/**/readme.md'
  - '!node_modules/**/CHANGELOG.md'
  - '!node_modules/**/changelog.md'
  - '!node_modules/**/LICENSE*'
  - '!node_modules/**/license*'
  - '!node_modules/**/*.d.ts'
  - '!node_modules/**/*.flow'
  - '!node_modules/**/.DS_Store'
  - '!node_modules/**/Thumbs.db'
  - '!resources/demo.mp4'
  - '!resources/demo.jpg'
  - '!resources/deploy.jpg'
  - '!node_modules/**/webpack/**'
  - '!node_modules/**/rollup/**'
  - '!node_modules/**/vite/**'
  - '!node_modules/**/@types/**'
  - '!node_modules/monaco-editor/esm/vs/language/!(typescript|javascript|json|css|html)/**'
  - '!node_modules/monaco-editor/min/vs/language/!(typescript|javascript|json|css|html)/**'
extraResources:
  - from: 'src/renderer/src/assets/db/'
    to: 'db/'
    filter: '**/*.db'
asarUnpack:
  - resources/**
icon: resources/icon.ico
win:
  executableName: RS-Chaterm
nsis:
  artifactName: ${name}-${version}-setup-${arch}.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
mac:
  icon: resources/icon.icns
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
dmg:
  artifactName: ${name}-${version}-macos-${arch}.${ext}
linux:
  target:
    - AppImage
    - deb
  maintainer: chaterm.ai
  category: Utility
  artifactName: ${name}-${version}-linux-${arch}.${ext}
npmRebuild: true
publish:
  provider: generic
  url: https://chaterm-static.intsig.net/download/
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
