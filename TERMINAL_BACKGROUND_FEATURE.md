# 终端背景设置功能

## 功能概述

我们为 Chaterm 终端应用添加了全新的终端背景设置功能，用户现在可以自定义终端的背景样式，包括：

- **默认背景**：使用系统默认的终端背景
- **纯色背景**：选择自定义的纯色作为终端背景
- **图片背景**：使用自定义图片或预设图片作为终端背景
- **透明度调节**：调整背景的透明度

## 功能特性

### 1. 背景类型选择
- **默认**：保持原有的终端背景样式
- **纯色背景**：提供颜色选择器，用户可以选择任意颜色
- **图片背景**：支持URL输入和本地图片上传

### 2. 预设背景
为了方便用户快速选择，我们提供了几种预设背景：
- **星空**：深色星空主题背景
- **渐变蓝**：蓝色渐变背景
- **深色网格**：深色网格图案背景

### 3. 透明度控制
- 支持 0.1 到 1.0 的透明度调节
- 实时预览透明度效果
- 百分比显示当前透明度值

### 4. 图片上传
- 支持本地图片文件上传
- 自动转换为 Base64 格式存储
- 支持常见图片格式（JPG、PNG、GIF等）

## 使用方法

### 访问设置
1. 打开 Chaterm 应用
2. 点击左侧菜单中的"设置"图标
3. 选择"终端设置"选项卡
4. 找到"终端背景"设置项

### 设置纯色背景
1. 在"背景类型"中选择"纯色背景"
2. 点击颜色选择器选择喜欢的颜色
3. 调整透明度滑块设置透明度
4. 设置会自动保存并应用到终端

### 设置图片背景
1. 在"背景类型"中选择"图片背景"
2. 可以选择以下方式之一：
   - 在URL输入框中输入图片链接
   - 点击"上传图片"按钮选择本地图片
   - 点击预设背景中的任一选项
3. 调整透明度滑块设置透明度
4. 设置会自动保存并应用到终端

## 技术实现

### 前端组件
- 在 `terminal.vue` 中添加了背景设置的UI组件
- 使用 Ant Design Vue 的表单组件构建设置界面
- 支持颜色选择器、文件上传、滑块等交互组件

### 数据存储
- 扩展了 `UserConfig` 接口，添加 `terminalBackground` 配置项
- 配置包含类型、颜色、图片URL和透明度信息
- 使用 IndexedDB 进行本地持久化存储

### 样式应用
- 在 `Term/index.vue` 中实现背景样式的动态应用
- 使用 Vue 的计算属性动态生成CSS样式
- 支持背景颜色、背景图片和透明度的实时切换

### 事件通信
- 使用 EventBus 在设置组件和终端组件间通信
- 设置变更时实时更新终端背景
- 确保多个终端实例同步更新

## 国际化支持

添加了中英文双语支持：

### 中文
- 终端背景
- 背景类型
- 默认/纯色背景/图片背景
- 背景颜色/背景图片URL/背景透明度
- 上传图片

### 英文
- Terminal Background
- Background Type
- Default/Solid Color/Image Background
- Background Color/Background Image URL/Background Opacity
- Upload Image

## 文件变更清单

### 新增功能文件
- 无新增文件，所有功能集成到现有文件中

### 修改的文件
1. `src/renderer/src/services/userConfigStoreService.ts`
   - 添加 `TerminalBackgroundConfig` 接口
   - 扩展 `UserConfig` 接口
   - 更新默认配置

2. `src/renderer/src/views/components/LeftTab/components/terminal.vue`
   - 添加背景设置UI组件
   - 实现图片上传功能
   - 添加预设背景选择
   - 新增相关CSS样式

3. `src/renderer/src/views/components/Term/index.vue`
   - 添加背景样式计算逻辑
   - 实现背景配置的动态应用
   - 添加事件监听器

4. `src/renderer/src/locales/lang/zh-CN.ts`
   - 添加中文翻译文本

5. `src/renderer/src/locales/lang/en-US.ts`
   - 添加英文翻译文本

## 测试建议

1. **基本功能测试**
   - 测试三种背景类型的切换
   - 验证颜色选择器功能
   - 测试图片URL输入和本地上传
   - 验证透明度调节功能

2. **预设背景测试**
   - 测试所有预设背景的选择
   - 验证预设背景的显示效果

3. **持久化测试**
   - 重启应用后验证设置是否保持
   - 测试多个终端标签页的背景同步

4. **兼容性测试**
   - 测试不同主题模式下的显示效果
   - 验证在不同屏幕尺寸下的适配

## 后续优化建议

1. **更多预设背景**：可以添加更多精美的预设背景选项
2. **背景动画**：考虑添加动态背景效果
3. **背景管理**：添加背景收藏和管理功能
4. **性能优化**：对大尺寸图片进行压缩处理
5. **云端同步**：支持背景设置的云端同步功能
