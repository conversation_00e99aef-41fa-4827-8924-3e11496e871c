# 多存储后端数据同步技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[用户界面层] --> B[同步管理器]
    B --> C[存储适配器工厂]
    C --> D[OneDrive 适配器]
    C --> E[GitHub 适配器]
    C --> F[MinIO 适配器]
    C --> G[SMB 适配器]
    C --> H[SFTP 适配器]
    
    B --> I[冲突解决器]
    B --> J[加密服务]
    B --> K[配置管理器]
    
    D --> L[OneDrive API]
    E --> M[GitHub API]
    F --> N[MinIO S3 API]
    G --> O[SMB 协议]
    H --> P[SFTP 协议]
    
    subgraph "应用层"
        A
        B
        I
        J
        K
    end
    
    subgraph "适配器层"
        C
        D
        E
        F
        G
        H
    end
    
    subgraph "外部服务"
        L
        M
        N
        O
        P
    end
```

## 2. 技术描述

* **前端**: React\@18 + TypeScript + Ant Design + Vite

* **后端**: Electron Main Process + Node.js

* **存储**: 多种存储后端适配器

* **加密**: AES-256 + RSA 混合加密

* **依赖**: axios、minio、node-smb2、ssh2-sftp-client、@microsoft/microsoft-graph-client

## 3. 路由定义

| 路由              | 用途                   |
| --------------- | -------------------- |
| /sync/config    | 存储配置页面，管理多个存储后端的连接配置 |
| /sync/manage    | 同步管理页面，监控和控制同步操作     |
| /sync/monitor   | 存储监控页面，查看使用统计和性能数据   |
| /sync/conflicts | 冲突解决页面，处理数据同步冲突      |

## 4. API 定义

### 4.1 核心 API

**存储配置管理**

```
POST /api/storage/config
```

请求参数:

| 参数名    | 参数类型   | 是否必需 | 描述                                    |
| ------ | ------ | ---- | ------------------------------------- |
| type   | string | true | 存储类型 (onedrive/github/minio/smb/sftp) |
| config | object | true | 存储配置对象                                |
| name   | string | true | 配置名称                                  |

响应参数:

| 参数名      | 参数类型    | 描述     |
| -------- | ------- | ------ |
| success  | boolean | 配置是否成功 |
| configId | string  | 配置ID   |

**同步操作**

```
POST /api/sync/start
```

请求参数:

| 参数名      | 参数类型   | 是否必需 | 描述                      |
| -------- | ------ | ---- | ----------------------- |
| configId | string | true | 存储配置ID                  |
| syncType | string | true | 同步类型 (full/incremental) |

**冲突解决**

```
POST /api/sync/resolve-conflict
```

请求参数:

| 参数名        | 参数类型   | 是否必需 | 描述                        |
| ---------- | ------ | ---- | ------------------------- |
| conflictId | string | true | 冲突ID                      |
| resolution | string | true | 解决策略 (local/remote/merge) |

## 5. 服务器架构图

```mermaid
graph TD
    A[Electron Main Process] --> B[同步控制器]
    B --> C[存储适配器管理器]
    C --> D[配置服务]
    C --> E[加密服务]
    C --> F[冲突解决服务]
    
    D --> G[配置存储]
    E --> H[密钥管理]
    F --> I[冲突数据库]
    
    subgraph "主进程服务层"
        B
        C
        D
        E
        F
    end
    
    subgraph "数据层"
        G
        H
        I
    end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    STORAGE_CONFIG ||--o{ SYNC_SESSION : has
    STORAGE_CONFIG ||--o{ SYNC_CONFLICT : generates
    SYNC_SESSION ||--o{ SYNC_LOG : produces
    
    STORAGE_CONFIG {
        string id PK
        string name
        string type
        json config
        boolean enabled
        datetime created_at
        datetime updated_at
    }
    
    SYNC_SESSION {
        string id PK
        string config_id FK
        string type
        string status
        int files_synced
        int files_failed
        datetime started_at
        datetime completed_at
    }
    
    SYNC_CONFLICT {
        string id PK
        string config_id FK
        string file_path
        json local_data
        json remote_data
        string status
        datetime created_at
    }
    
    SYNC_LOG {
        string id PK
        string session_id FK
        string level
        string message
        json metadata
        datetime created_at
    }
```

### 6.2 数据定义语言

**存储配置表 (storage\_configs)**

```sql
-- 创建存储配置表
CREATE TABLE storage_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('onedrive', 'github', 'minio', 'smb', 'sftp')),
    config JSONB NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_storage_configs_type ON storage_configs(type);
CREATE INDEX idx_storage_configs_enabled ON storage_configs(enabled);

-- 同步会话表
CREATE TABLE sync_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_id UUID REFERENCES storage_configs(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('full', 'incremental')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    files_synced INTEGER DEFAULT 0,
    files_failed INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 同步冲突表
CREATE TABLE sync_conflicts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_id UUID REFERENCES storage_configs(id) ON DELETE CASCADE,
    file_path VARCHAR(500) NOT NULL,
    local_data JSONB,
    remote_data JSONB,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'resolved')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 同步日志表
CREATE TABLE sync_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES sync_sessions(id) ON DELETE CASCADE,
    level VARCHAR(10) NOT NULL CHECK (level IN ('debug', 'info', 'warn', 'error')),
    message TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 初始化数据
INSERT INTO storage_configs (name, type, config, enabled) VALUES
('默认 OneDrive', 'onedrive', '{"clientId": "", "tenantId": "common"}', false),
('默认 GitHub', 'github', '{"owner": "", "repo": "", "token": ""}', false);
```

## 7. 存储适配器接口设计

### 7.1 基础适配器接口

```typescript
interface IStorageAdapter {
  // 连接和认证
  connect(config: StorageConfig): Promise<boolean>;
  disconnect(): Promise<void>;
  testConnection(): Promise<boolean>;
  
  // 文件操作
  uploadFile(localPath: string, remotePath: string): Promise<void>;
  downloadFile(remotePath: string, localPath: string): Promise<void>;
  deleteFile(remotePath: string): Promise<void>;
  listFiles(remotePath?: string): Promise<FileInfo[]>;
  
  // 元数据操作
  getFileMetadata(remotePath: string): Promise<FileMetadata>;
  
  // 同步操作
  sync(localPath: string, remotePath: string): Promise<SyncResult>;
}
```

### 7.2 配置类型定义

```typescript
interface OneDriveConfig {
  clientId: string;
  tenantId: string;
  redirectUri: string;
}

interface GitHubConfig {
  owner: string;
  repo: string;
  token: string;
  branch?: string;
}

interface MinIOConfig {
  endpoint: string;
  accessKey: string;
  secretKey: string;
  bucket: string;
  region?: string;
}

interface SMBConfig {
  host: string;
  port?: number;
  username: string;
  password: string;
  share: string;
  domain?: string;
}

interface SFTPConfig {
  host: string;
  port?: number;
  username: string;
  password?: string;
  privateKey?: string;
  passphrase?: string;
}
```

## 8. 实现要点

### 8.1 错误处理策略

* 网络错误：自动重试机制，指数退避

* 认证错误：提示用户重新认证

* 存储空间不足：警告并建议清理

* 冲突错误：记录冲突并提供解决选项

### 8.2 性能优化

* 增量同步：只同步变更的文件

* 并发控制：限制同时进行的传输数量

* 压缩传输：对大文件进行压缩

* 缓存机制：缓存文件元数据减少API调用

### 8.3 安全考虑

* 端到端加密：所有数据传输前加密

* 凭据安全：使用系统密钥链存储敏感信息

* 访问控制：基于角色的权限管理

* 审计日志：记录所有同步操作

