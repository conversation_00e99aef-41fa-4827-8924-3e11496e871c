// 调试自动补全功能的脚本
// 在浏览器开发者工具的控制台中运行

console.log('=== 自动补全功能调试 ===');

// 1. 检查配置
async function checkConfig() {
  console.log('\n1. 检查配置:');
  try {
    const config = await window.api.getUserConfig();
    console.log('用户配置:', config);
    console.log('autoCompleteStatus:', config.autoCompleteStatus);
    console.log('自动补全是否启用:', config.autoCompleteStatus === 1);
  } catch (error) {
    console.error('获取配置失败:', error);
  }
}

// 2. 测试 queryCommand API
async function testQueryCommand() {
  console.log('\n2. 测试 queryCommand API:');
  try {
    const testCommand = 'ls';
    const testIp = '127.0.0.1';
    console.log(`测试命令: ${testCommand}, IP: ${testIp}`);
    
    const result = await window.api.queryCommand({
      command: testCommand,
      ip: testIp
    });
    
    console.log('API 响应:', result);
    
    if (result && result.success) {
      console.log('✅ API 调用成功');
      console.log('建议数量:', result.data ? result.data.length : 0);
      console.log('建议内容:', result.data);
    } else {
      console.log('❌ API 调用失败:', result.error || '未知错误');
    }
  } catch (error) {
    console.error('❌ API 调用异常:', error);
  }
}

// 3. 检查 DOM 元素
function checkDOMElements() {
  console.log('\n3. 检查 DOM 元素:');
  
  // 查找建议组件
  const suggestionElements = document.querySelectorAll('.suggestions');
  console.log('找到的建议组件数量:', suggestionElements.length);
  
  suggestionElements.forEach((el, index) => {
    console.log(`建议组件 ${index + 1}:`, {
      id: el.id,
      visible: el.style.display !== 'none',
      children: el.children.length,
      innerHTML: el.innerHTML.substring(0, 200) + '...'
    });
  });
  
  // 查找终端元素
  const terminalElements = document.querySelectorAll('.xterm');
  console.log('找到的终端元素数量:', terminalElements.length);
  
  // 查找 SSH 连接组件
  const sshComponents = document.querySelectorAll('[data-connection-id]');
  console.log('找到的 SSH 组件数量:', sshComponents.length);
}

// 4. 检查 Vue 组件状态
function checkVueState() {
  console.log('\n4. 检查 Vue 组件状态:');
  
  // 尝试获取 Vue 应用实例
  const app = document.querySelector('#app').__vue_app__;
  if (app) {
    console.log('✅ Vue 应用实例找到');
    
    // 查找 SSH 连接组件的实例
    const sshComponents = document.querySelectorAll('[data-v-]');
    console.log('Vue 组件数量:', sshComponents.length);
  } else {
    console.log('❌ Vue 应用实例未找到');
  }
}

// 5. 模拟输入测试
function simulateInput() {
  console.log('\n5. 模拟输入测试:');
  
  const terminalElements = document.querySelectorAll('.xterm-helper-textarea');
  console.log('找到的终端输入元素数量:', terminalElements.length);
  
  if (terminalElements.length > 0) {
    const textarea = terminalElements[0];
    console.log('模拟输入 "ls" 到终端...');
    
    // 模拟输入事件
    textarea.value = 'ls';
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    
    setTimeout(() => {
      checkDOMElements();
    }, 1000);
  } else {
    console.log('❌ 未找到终端输入元素');
  }
}

// 运行所有检查
async function runAllChecks() {
  await checkConfig();
  await testQueryCommand();
  checkDOMElements();
  checkVueState();
  
  console.log('\n=== 调试完成 ===');
  console.log('如果要测试输入，请运行: simulateInput()');
}

// 导出函数供手动调用
window.debugAutoComplete = {
  runAllChecks,
  checkConfig,
  testQueryCommand,
  checkDOMElements,
  checkVueState,
  simulateInput
};

console.log('调试工具已加载，运行 debugAutoComplete.runAllChecks() 开始调试');
