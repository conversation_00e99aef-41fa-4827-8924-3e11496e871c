// 登录认证功能已屏蔽 - Login authentication functionality disabled
// import { getUserInfo, setUserInfo } from '@/utils/permission'
// import { dataSyncService } from '@/services/dataSyncService'

export const beforeEach = async (to, from, next) => {
  // 登录认证已屏蔽，直接允许访问所有页面 - Authentication disabled, allow access to all pages
  console.log('路由守卫：登录认证已屏蔽，直接允许访问')

  // 如果访问登录页面，直接跳转到主页
  if (to.path === '/login') {
    next('/')
    return
  }

  // 初始化数据库以支持自动补全等功能
  try {
    const api = window.api as any
    if (api && api.initUserDatabase) {
      // 使用默认的访客用户ID来初始化数据库
      const dbResult = await api.initUserDatabase({ uid: 999999999 })
      console.log('[Router] 数据库初始化结果:', dbResult)

      if (!dbResult.success) {
        console.warn('[Router] 数据库初始化失败，但继续访问:', dbResult.error)
      }
    }
  } catch (error) {
    console.warn('[Router] 数据库初始化异常:', error)
  }

  // 直接允许访问
  next()

  // 原有的认证逻辑已屏蔽 - Original authentication logic disabled
  // const token = localStorage.getItem('ctm-token')
  // const isDev = import.meta.env.MODE === 'development'
  //
  // // 如果有有效的登录token
  // if (token && token !== 'guest_token') {
  //   try {
  //     const userInfo = getUserInfo()
  //     if (userInfo && userInfo.uid) {
  //       if (typeof window !== 'undefined' && window.api && window.api.initUserDatabase) {
  //         const dbResult = await window.api.initUserDatabase({ uid: userInfo.uid })
  //
  //         if (dbResult.success) {
  //           // 数据库初始化成功后，异步初始化数据同步服务（不阻塞界面显示）
  //           dataSyncService.initialize().catch((error) => {
  //             console.error('数据同步服务初始化失败:', error)
  //           })
  //           next()
  //         } else {
  //           console.error('数据库初始化失败，设置默认用户模式')
  //           await setupDefaultUserMode()
  //           next()
  //         }
  //       } else {
  //         console.log('非 Electron 环境，直接进入应用')
  //         next()
  //       }
  //     } else {
  //       // 用户信息无效，设置默认用户模式
  //       await setupDefaultUserMode()
  //       next()
  //     }
  //   } catch (error) {
  //     console.error('处理失败:', error)
  //     // In the development environment, bypass the relevant errors (usually caused by hot updates)
  //     if (isDev && (error.message.includes('nextSibling') || error.message.includes('getUserInfo'))) {
  //       next()
  //       return
  //     }
  //     // 出错时设置默认用户模式
  //     await setupDefaultUserMode()
  //     next()
  //   }
  // } else {
  //   // 没有登录状态，设置默认用户模式
  //   await setupDefaultUserMode()
  //   next()
  // }
}

export const afterEach = () => {}
