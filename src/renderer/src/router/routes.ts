// 登录认证功能已屏蔽 - Login authentication functionality disabled
export const AppRoutes = [
  {
    path: '/',
    name: 'Home',
    meta: {
      requiresAuth: false // 已屏蔽认证要求 - Authentication requirement disabled
    },
    component: () => import('@/views/index.vue')
  },
  {
    path: '/optimization-demo',
    name: 'OptimizationDemo',
    meta: {
      requiresAuth: false
    },
    component: () => import('@/views/OptimizationDemo.vue')
  }
  // 登录路由已移除 - Login route removed
  // {
  //   path: '/login',
  //   name: 'Login',
  //   meta: {
  //     requiresAuth: false
  //   },
  //   component: () => import('@/views/auth/login.vue')
  // }
]
