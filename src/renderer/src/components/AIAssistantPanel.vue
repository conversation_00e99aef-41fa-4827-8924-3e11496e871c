<template>
  <div
    class="ai-assistant-panel"
    v-if="showPanel"
  >
    <div class="panel-header">
      <div class="header-title">
        <span class="ai-icon">🤖</span>
        <h3>AI助手</h3>
      </div>
      <div class="header-actions">
        <button
          @click="toggleAutoAnalysis"
          :class="{ active: autoAnalysis }"
          class="toggle-btn"
        >
          自动分析
        </button>
        <button
          @click="closePanel"
          class="close-btn"
          >×</button
        >
      </div>
    </div>

    <div class="panel-content">
      <!-- 错误诊断结果 -->
      <div
        v-if="currentDiagnosis"
        class="diagnosis-section"
      >
        <div class="section-header">
          <span class="icon">🔍</span>
          <h4>错误诊断</h4>
          <span class="confidence">置信度: {{ (currentDiagnosis.confidence * 100).toFixed(0) }}%</span>
        </div>

        <div class="diagnosis-content">
          <div class="error-info">
            <div
              class="error-type"
              :class="currentDiagnosis.errorType"
            >
              {{ getErrorTypeLabel(currentDiagnosis.errorType) }}
            </div>
            <div
              class="severity"
              :class="currentDiagnosis.severity"
            >
              {{ getSeverityLabel(currentDiagnosis.severity) }}
            </div>
          </div>

          <p class="description">{{ currentDiagnosis.description }}</p>

          <div class="possible-causes">
            <h5>可能原因：</h5>
            <ul>
              <li
                v-for="cause in currentDiagnosis.possibleCauses"
                :key="cause"
              >
                {{ cause }}
              </li>
            </ul>
          </div>

          <div class="solutions">
            <h5>解决方案：</h5>
            <div
              v-for="(solution, index) in currentDiagnosis.solutions"
              :key="index"
              class="solution-item"
            >
              <div class="solution-header">
                <span class="solution-title">{{ solution.title }}</span>
                <span
                  class="difficulty"
                  :class="solution.difficulty"
                  >{{ solution.difficulty }}</span
                >
                <span
                  class="risk"
                  :class="solution.riskLevel"
                  >{{ solution.riskLevel }} 风险</span
                >
              </div>
              <p class="solution-desc">{{ solution.description }}</p>
              <div
                v-if="solution.commands"
                class="solution-commands"
              >
                <div
                  v-for="cmd in solution.commands"
                  :key="cmd"
                  class="command-item"
                >
                  <code>{{ cmd }}</code>
                  <button
                    @click="copyCommand(cmd)"
                    class="copy-btn"
                    >复制</button
                  >
                  <button
                    @click="executeCommand(cmd)"
                    class="execute-btn"
                    >执行</button
                  >
                </div>
              </div>
              <div
                v-if="solution.steps"
                class="solution-steps"
              >
                <ol>
                  <li
                    v-for="step in solution.steps"
                    :key="step"
                    >{{ step }}</li
                  >
                </ol>
              </div>
              <div class="solution-meta">
                <span>预计时间: {{ solution.estimatedTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 命令优化建议 -->
      <div
        v-if="currentOptimization"
        class="optimization-section"
      >
        <div class="section-header">
          <span class="icon">⚡</span>
          <h4>命令优化</h4>
        </div>

        <div class="optimization-content">
          <div class="command-comparison">
            <div class="original-command">
              <label>原命令:</label>
              <code>{{ currentOptimization.originalCommand }}</code>
            </div>
            <div class="optimized-command">
              <label>优化后:</label>
              <code>{{ currentOptimization.optimizedCommand }}</code>
              <button
                @click="copyCommand(currentOptimization.optimizedCommand)"
                class="copy-btn"
                >复制</button
              >
              <button
                @click="executeCommand(currentOptimization.optimizedCommand)"
                class="execute-btn"
                >执行</button
              >
            </div>
          </div>

          <div class="improvements">
            <h5>改进点:</h5>
            <ul>
              <li
                v-for="improvement in currentOptimization.improvements"
                :key="improvement"
              >
                {{ improvement }}
              </li>
            </ul>
          </div>

          <p class="explanation">{{ currentOptimization.explanation }}</p>
          <p class="performance-gain">性能提升: {{ currentOptimization.performanceGain }}</p>
        </div>
      </div>

      <!-- AI洞察 -->
      <div
        v-if="insights.length > 0"
        class="insights-section"
      >
        <div class="section-header">
          <span class="icon">💡</span>
          <h4>AI洞察</h4>
        </div>

        <div class="insights-content">
          <div
            v-for="(insight, index) in insights"
            :key="index"
            class="insight-item"
            :class="insight.type"
          >
            <div class="insight-header">
              <span class="insight-icon">{{ getInsightIcon(insight.type) }}</span>
              <span class="insight-title">{{ insight.title }}</span>
            </div>
            <p class="insight-content">{{ insight.content }}</p>
            <div
              v-if="insight.commands && insight.actionable"
              class="insight-actions"
            >
              <div
                v-for="cmd in insight.commands"
                :key="cmd"
                class="command-item"
              >
                <code>{{ cmd }}</code>
                <button
                  @click="copyCommand(cmd)"
                  class="copy-btn"
                  >复制</button
                >
                <button
                  @click="executeCommand(cmd)"
                  class="execute-btn"
                  >执行</button
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 学习统计 -->
      <div
        v-if="showStats"
        class="stats-section"
      >
        <div class="section-header">
          <span class="icon">📊</span>
          <h4>学习统计</h4>
          <button
            @click="toggleStats"
            class="toggle-stats"
            >{{ showStats ? '隐藏' : '显示' }}</button
          >
        </div>

        <div class="stats-content">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">总错误数</span>
              <span class="stat-value">{{ learningStats.totalErrors }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">常用命令</span>
              <span class="stat-value">{{ learningStats.commonCommands.slice(0, 3).join(', ') }}</span>
            </div>
          </div>

          <div class="error-types">
            <h5>错误类型分布:</h5>
            <div
              v-for="(count, type) in learningStats.errorTypes"
              :key="type"
              class="error-type-item"
            >
              <span class="type-name">{{ type }}</span>
              <span class="type-count">{{ count }}</span>
            </div>
          </div>

          <div class="suggestions">
            <h5>改进建议:</h5>
            <ul>
              <li
                v-for="suggestion in learningStats.improvementSuggestions"
                :key="suggestion"
              >
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-footer">
      <button
        @click="analyzeLastCommand"
        class="action-btn"
        >分析上一条命令</button
      >
      <button
        @click="clearHistory"
        class="action-btn secondary"
        >清空历史</button
      >
      <button
        @click="toggleStats"
        class="action-btn"
        >{{ showStats ? '隐藏' : '显示' }}统计</button
      >
    </div>
  </div>

  <!-- 浮动AI按钮 -->
  <div
    class="ai-toggle"
    v-if="!showPanel"
    @click="togglePanel"
  >
    <span class="ai-icon">🤖</span>
    <span class="ai-text">AI助手</span>
    <span
      v-if="hasNewInsights"
      class="notification-dot"
    ></span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { aiAssistant, DiagnosisResult, CommandOptimization, AIInsight } from '@/utils/aiAssistant'

interface Props {
  lastCommand?: string
  lastOutput?: string
  commandHistory?: string[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  executeCommand: [command: string]
  copyToClipboard: [text: string]
  close: []
}>()

const showPanel = ref(true) // 默认显示，因为是通过工具栏打开的
const autoAnalysis = ref(true)
const showStats = ref(false)
const hasNewInsights = ref(false)

const currentDiagnosis = ref<DiagnosisResult | null>(null)
const currentOptimization = ref<CommandOptimization | null>(null)
const insights = ref<AIInsight[]>([])
const learningStats = ref<any>({})

onMounted(() => {
  updateLearningStats()

  // 如果启用自动分析，分析最后的命令
  if (autoAnalysis.value && props.lastCommand && props.lastOutput) {
    analyzeCommand(props.lastCommand, props.lastOutput)
  }
})

/**
 * 分析命令
 */
const analyzeCommand = (command: string, output: string) => {
  // 错误诊断
  if (output && (output.includes('error') || output.includes('failed'))) {
    const context = {
      command,
      output,
      workingDirectory: '/',
      environment: {},
      timestamp: Date.now()
    }

    currentDiagnosis.value = aiAssistant.diagnoseError(context)
  }

  // 命令优化
  currentOptimization.value = aiAssistant.optimizeCommand(command)

  // AI洞察
  insights.value = aiAssistant.getAIInsights({
    command,
    output,
    history: props.commandHistory
  })

  // 如果有新的洞察，显示通知
  if (insights.value.length > 0 || currentDiagnosis.value || currentOptimization.value) {
    hasNewInsights.value = true
  }
}

/**
 * 分析上一条命令
 */
const analyzeLastCommand = () => {
  if (props.lastCommand && props.lastOutput) {
    analyzeCommand(props.lastCommand, props.lastOutput)
  }
}

/**
 * 更新学习统计
 */
const updateLearningStats = () => {
  learningStats.value = aiAssistant.getLearningStats()
}

/**
 * 切换面板显示
 */
const togglePanel = () => {
  if (showPanel.value) {
    // 如果当前显示，则关闭并emit close事件
    emit('close')
  } else {
    showPanel.value = true
    hasNewInsights.value = false
  }
}

/**
 * 关闭面板
 */
const closePanel = () => {
  console.log('AIAssistantPanel closePanel clicked')
  emit('close')
}

/**
 * 切换自动分析
 */
const toggleAutoAnalysis = () => {
  autoAnalysis.value = !autoAnalysis.value
}

/**
 * 切换统计显示
 */
const toggleStats = () => {
  showStats.value = !showStats.value
  if (showStats.value) {
    updateLearningStats()
  }
}

/**
 * 复制命令
 */
const copyCommand = (command: string) => {
  emit('copyToClipboard', command)
}

/**
 * 执行命令
 */
const executeCommand = (command: string) => {
  emit('executeCommand', command)
}

/**
 * 清空历史
 */
const clearHistory = () => {
  currentDiagnosis.value = null
  currentOptimization.value = null
  insights.value = []
  updateLearningStats()
}

/**
 * 获取错误类型标签
 */
const getErrorTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    syntax: '语法错误',
    permission: '权限错误',
    network: '网络错误',
    file: '文件系统错误',
    dependency: '依赖错误',
    system: '系统错误',
    unknown: '未知错误'
  }
  return labels[type] || type
}

/**
 * 获取严重程度标签
 */
const getSeverityLabel = (severity: string): string => {
  const labels: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return labels[severity] || severity
}

/**
 * 获取洞察图标
 */
const getInsightIcon = (type: string): string => {
  const icons: Record<string, string> = {
    tip: '💡',
    warning: '⚠️',
    optimization: '⚡',
    learning: '📚'
  }
  return icons[type] || '💡'
}
</script>

<style scoped lang="less">
.ai-assistant-panel {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 13px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .header-title {
    display: flex;
    align-items: center;
    gap: 8px;

    .ai-icon {
      font-size: 20px;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .header-actions {
    display: flex;
    gap: 8px;

    .toggle-btn {
      padding: 4px 8px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      background: transparent;
      color: white;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover,
      &.active {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    .close-btn {
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
      }
    }
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;

  .icon {
    font-size: 16px;
  }

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    flex: 1;
  }

  .confidence {
    font-size: 11px;
    color: #666;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 10px;
  }
}

.diagnosis-section,
.optimization-section,
.insights-section,
.stats-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.error-info {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;

  .error-type,
  .severity {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
  }

  .error-type {
    background: #e6f7ff;
    color: #1890ff;

    &.permission {
      background: #fff2e8;
      color: #fa8c16;
    }
    &.network {
      background: #f6ffed;
      color: #52c41a;
    }
    &.file {
      background: #fff1f0;
      color: #ff4d4f;
    }
  }

  .severity {
    &.low {
      background: #f6ffed;
      color: #52c41a;
    }
    &.medium {
      background: #fff7e6;
      color: #fa8c16;
    }
    &.high {
      background: #fff2e8;
      color: #fa541c;
    }
    &.critical {
      background: #fff1f0;
      color: #ff4d4f;
    }
  }
}

.description {
  margin: 8px 0;
  color: #333;
  line-height: 1.5;
}

.possible-causes,
.solutions,
.improvements {
  margin: 12px 0;

  h5 {
    margin: 0 0 6px 0;
    font-size: 12px;
    font-weight: 600;
    color: #666;
  }

  ul,
  ol {
    margin: 0;
    padding-left: 16px;

    li {
      margin-bottom: 4px;
      line-height: 1.4;
    }
  }
}

.solution-item {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;

  .solution-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;

    .solution-title {
      font-weight: 600;
      flex: 1;
    }

    .difficulty,
    .risk {
      padding: 1px 6px;
      border-radius: 8px;
      font-size: 10px;

      &.easy {
        background: #f6ffed;
        color: #52c41a;
      }
      &.medium {
        background: #fff7e6;
        color: #fa8c16;
      }
      &.hard {
        background: #fff2e8;
        color: #fa541c;
      }
      &.low {
        background: #f6ffed;
        color: #52c41a;
      }
      &.high {
        background: #fff1f0;
        color: #ff4d4f;
      }
    }
  }

  .solution-desc {
    margin: 6px 0;
    color: #666;
    font-size: 12px;
  }

  .solution-meta {
    margin-top: 8px;
    font-size: 11px;
    color: #999;
  }
}

.command-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 6px 0;
  padding: 6px 8px;
  background: #f5f5f5;
  border-radius: 4px;

  code {
    flex: 1;
    background: none;
    padding: 0;
    font-size: 11px;
    color: #333;
  }

  .copy-btn,
  .execute-btn {
    padding: 2px 6px;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    background: white;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }

  .execute-btn {
    background: #1890ff;
    border-color: #1890ff;
    color: white;

    &:hover {
      background: #40a9ff;
    }
  }
}

.ai-toggle {
  position: fixed;
  bottom: 90px; // 避免与性能监控重叠
  right: 20px;
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transition: all 0.3s;
  position: relative;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .ai-icon {
    font-size: 24px;
    margin-bottom: 2px;
  }

  .ai-text {
    font-size: 10px;
    color: white;
    font-weight: 500;
  }

  .notification-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    background: #ff4d4f;
    border-radius: 50%;
    border: 2px solid white;
  }
}

.panel-footer {
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  gap: 8px;

  .action-btn {
    flex: 1;
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: white;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }

    &.secondary {
      color: #666;
    }
  }
}
</style>
