<template>
  <div
    class="plugin-manager"
    v-if="showManager"
  >
    <div class="manager-header">
      <div class="header-title">
        <span class="plugin-icon">🔌</span>
        <h3>插件管理器</h3>
      </div>
      <div class="header-actions">
        <button
          @click="refreshPlugins"
          class="action-btn"
          >刷新</button
        >
        <button
          @click="installPlugin"
          class="action-btn primary"
          >安装插件</button
        >
        <button
          @click="closeManager"
          class="close-btn"
          >×</button
        >
      </div>
    </div>

    <div class="manager-content">
      <!-- 搜索和过滤 -->
      <div class="search-section">
        <div class="search-bar">
          <input
            v-model="searchQuery"
            placeholder="搜索插件..."
            class="search-input"
            @input="handleSearch"
          />
          <button
            @click="clearSearch"
            class="clear-search"
            >清空</button
          >
        </div>

        <div class="filter-tabs">
          <button
            v-for="tab in filterTabs"
            :key="tab.key"
            @click="activeFilter = tab.key"
            :class="{ active: activeFilter === tab.key }"
            class="filter-tab"
          >
            {{ tab.label }} ({{ tab.count }})
          </button>
        </div>
      </div>

      <!-- 插件统计 -->
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-label">总计</span>
          <span class="stat-value">{{ pluginStats.total }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">已启用</span>
          <span class="stat-value enabled">{{ pluginStats.enabled }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">已禁用</span>
          <span class="stat-value disabled">{{ pluginStats.disabled }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">错误</span>
          <span class="stat-value error">{{ pluginStats.errors }}</span>
        </div>
      </div>

      <!-- 插件列表 -->
      <div class="plugins-section">
        <div
          v-if="filteredPlugins.length === 0"
          class="empty-state"
        >
          <span class="empty-icon">📦</span>
          <p>{{ getEmptyMessage() }}</p>
          <button
            @click="installPlugin"
            class="install-first-btn"
            >安装第一个插件</button
          >
        </div>

        <div
          v-else
          class="plugins-list"
        >
          <div
            v-for="plugin in filteredPlugins"
            :key="plugin.manifest.id"
            class="plugin-item"
            :class="{
              enabled: plugin.enabled,
              disabled: !plugin.enabled,
              error: plugin.error
            }"
          >
            <div class="plugin-header">
              <div class="plugin-info">
                <h4 class="plugin-name">{{ plugin.manifest.name }}</h4>
                <span class="plugin-version">v{{ plugin.manifest.version }}</span>
                <span class="plugin-author">by {{ plugin.manifest.author }}</span>
              </div>
              <div class="plugin-controls">
                <button
                  @click="togglePlugin(plugin)"
                  :class="{ enabled: plugin.enabled }"
                  class="toggle-btn"
                >
                  {{ plugin.enabled ? '禁用' : '启用' }}
                </button>
                <button
                  @click="configurePlugin(plugin)"
                  class="config-btn"
                  >设置</button
                >
                <button
                  @click="uninstallPlugin(plugin)"
                  class="uninstall-btn"
                  >卸载</button
                >
              </div>
            </div>

            <p class="plugin-description">{{ plugin.manifest.description }}</p>

            <div class="plugin-meta">
              <div class="plugin-tags">
                <span
                  v-for="keyword in plugin.manifest.keywords"
                  :key="keyword"
                  class="plugin-tag"
                >
                  {{ keyword }}
                </span>
              </div>

              <div class="plugin-links">
                <a
                  v-if="plugin.manifest.homepage"
                  :href="plugin.manifest.homepage"
                  target="_blank"
                  class="plugin-link"
                >
                  主页
                </a>
                <a
                  v-if="plugin.manifest.repository"
                  :href="plugin.manifest.repository"
                  target="_blank"
                  class="plugin-link"
                >
                  源码
                </a>
              </div>
            </div>

            <div
              v-if="plugin.error"
              class="plugin-error"
            >
              <span class="error-icon">⚠️</span>
              <span class="error-message">{{ plugin.error }}</span>
            </div>

            <div
              v-if="plugin.manifest.permissions.length > 0"
              class="plugin-permissions"
            >
              <h5>权限要求:</h5>
              <div class="permissions-list">
                <span
                  v-for="permission in plugin.manifest.permissions"
                  :key="permission.type"
                  class="permission-item"
                  :title="permission.description"
                >
                  {{ getPermissionLabel(permission) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 浮动插件按钮 -->
  <div
    class="plugin-toggle"
    v-if="!showManager"
    @click="toggleManager"
  >
    <span class="plugin-icon">🔌</span>
    <span class="plugin-text">插件</span>
    <span
      v-if="hasUpdates"
      class="update-dot"
    ></span>
  </div>

  <!-- 插件配置对话框 -->
  <div
    v-if="showConfigDialog"
    class="config-dialog-overlay"
    @click="closeConfigDialog"
  >
    <div
      class="config-dialog"
      @click.stop
    >
      <div class="dialog-header">
        <h4>{{ currentPlugin?.manifest.name }} - 设置</h4>
        <button
          @click="closeConfigDialog"
          class="close-btn"
          >×</button
        >
      </div>

      <div class="dialog-content">
        <div
          v-if="currentPlugin?.manifest.settings"
          class="settings-form"
        >
          <div
            v-for="setting in currentPlugin.manifest.settings"
            :key="setting.key"
            class="setting-item"
          >
            <label class="setting-label">
              {{ setting.title }}
              <span
                v-if="setting.description"
                class="setting-description"
              >
                {{ setting.description }}
              </span>
            </label>

            <input
              v-if="setting.type === 'string'"
              v-model="pluginSettings[setting.key]"
              :placeholder="setting.default"
              class="setting-input"
            />

            <input
              v-else-if="setting.type === 'number'"
              v-model.number="pluginSettings[setting.key]"
              type="number"
              :min="setting.validation?.min"
              :max="setting.validation?.max"
              class="setting-input"
            />

            <input
              v-else-if="setting.type === 'boolean'"
              v-model="pluginSettings[setting.key]"
              type="checkbox"
              class="setting-checkbox"
            />

            <select
              v-else-if="setting.type === 'select'"
              v-model="pluginSettings[setting.key]"
              class="setting-select"
            >
              <option
                v-for="option in setting.options"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </select>
          </div>
        </div>

        <div
          v-else
          class="no-settings"
        >
          <p>此插件没有可配置的设置</p>
        </div>
      </div>

      <div class="dialog-actions">
        <button
          @click="closeConfigDialog"
          class="btn-cancel"
          >取消</button
        >
        <button
          @click="savePluginSettings"
          class="btn-save"
          >保存</button
        >
      </div>
    </div>
  </div>

  <!-- 安装插件对话框 -->
  <div
    v-if="showInstallDialog"
    class="install-dialog-overlay"
    @click="closeInstallDialog"
  >
    <div
      class="install-dialog"
      @click.stop
    >
      <div class="dialog-header">
        <h4>安装插件</h4>
        <button
          @click="closeInstallDialog"
          class="close-btn"
          >×</button
        >
      </div>

      <div class="dialog-content">
        <div class="install-methods">
          <div class="method-item">
            <h5>从文件安装</h5>
            <input
              type="file"
              accept=".zip,.tar.gz"
              @change="handleFileInstall"
              class="file-input"
            />
          </div>

          <div class="method-item">
            <h5>从URL安装</h5>
            <input
              v-model="installUrl"
              placeholder="插件包URL"
              class="url-input"
            />
            <button
              @click="installFromUrl"
              class="install-btn"
              >安装</button
            >
          </div>

          <div class="method-item">
            <h5>从插件市场</h5>
            <button
              @click="openMarketplace"
              class="marketplace-btn"
              >打开插件市场</button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { pluginSystem, Plugin } from '@/utils/pluginSystem'

const emit = defineEmits<{
  close: []
}>()

const showManager = ref(true) // 默认显示，因为是通过工具栏打开的
const showConfigDialog = ref(false)
const showInstallDialog = ref(false)
const hasUpdates = ref(false)

const searchQuery = ref('')
const activeFilter = ref('all')
const plugins = ref<Plugin[]>([])
const currentPlugin = ref<Plugin | null>(null)
const pluginSettings = ref<Record<string, any>>({})
const installUrl = ref('')

const filterTabs = computed(() => {
  const stats = pluginStats.value
  return [
    { key: 'all', label: '全部', count: stats.total },
    { key: 'enabled', label: '已启用', count: stats.enabled },
    { key: 'disabled', label: '已禁用', count: stats.disabled },
    { key: 'errors', label: '错误', count: stats.errors }
  ]
})

const pluginStats = computed(() => {
  return pluginSystem.getPluginStats()
})

const filteredPlugins = computed(() => {
  let filtered = plugins.value

  // 按状态过滤
  if (activeFilter.value === 'enabled') {
    filtered = filtered.filter((p) => p.enabled)
  } else if (activeFilter.value === 'disabled') {
    filtered = filtered.filter((p) => !p.enabled)
  } else if (activeFilter.value === 'errors') {
    filtered = filtered.filter((p) => p.error)
  }

  // 按搜索查询过滤
  if (searchQuery.value) {
    filtered = pluginSystem.searchPlugins(searchQuery.value)
  }

  return filtered
})

onMounted(() => {
  loadPlugins()
  setupPluginListeners()
})

onUnmounted(() => {
  removePluginListeners()
})

/**
 * 加载插件列表
 */
const loadPlugins = () => {
  plugins.value = pluginSystem.getAllPlugins()
}

/**
 * 设置插件事件监听
 */
const setupPluginListeners = () => {
  pluginSystem.on('pluginLoaded', loadPlugins)
  pluginSystem.on('pluginUnloaded', loadPlugins)
  pluginSystem.on('pluginEnabled', loadPlugins)
  pluginSystem.on('pluginDisabled', loadPlugins)
}

/**
 * 移除插件事件监听
 */
const removePluginListeners = () => {
  // 移除事件监听器
}

/**
 * 切换插件状态
 */
const togglePlugin = async (plugin: Plugin) => {
  try {
    if (plugin.enabled) {
      await pluginSystem.disablePlugin(plugin.manifest.id)
    } else {
      await pluginSystem.enablePlugin(plugin.manifest.id)
    }
  } catch (error) {
    alert('操作失败: ' + (error as Error).message)
  }
}

/**
 * 配置插件
 */
const configurePlugin = (plugin: Plugin) => {
  currentPlugin.value = plugin
  pluginSettings.value = { ...plugin.settings }
  showConfigDialog.value = true
}

/**
 * 卸载插件
 */
const uninstallPlugin = async (plugin: Plugin) => {
  if (confirm(`确定要卸载插件 "${plugin.manifest.name}" 吗？`)) {
    try {
      await pluginSystem.unloadPlugin(plugin.manifest.id)
    } catch (error) {
      alert('卸载失败: ' + (error as Error).message)
    }
  }
}

/**
 * 保存插件设置
 */
const savePluginSettings = async () => {
  if (currentPlugin.value) {
    try {
      await pluginSystem.updatePluginSettings(currentPlugin.value.manifest.id, pluginSettings.value)
      closeConfigDialog()
    } catch (error) {
      alert('保存设置失败: ' + (error as Error).message)
    }
  }
}

/**
 * 安装插件
 */
const installPlugin = () => {
  showInstallDialog.value = true
}

/**
 * 从文件安装
 */
const handleFileInstall = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // 实现文件安装逻辑
    console.log('安装插件文件:', file.name)
  }
}

/**
 * 从URL安装
 */
const installFromUrl = () => {
  if (installUrl.value) {
    // 实现URL安装逻辑
    console.log('从URL安装:', installUrl.value)
  }
}

/**
 * 打开插件市场
 */
const openMarketplace = () => {
  // 打开插件市场
  window.open('https://chaterm-plugins.com', '_blank')
}

/**
 * 刷新插件
 */
const refreshPlugins = () => {
  loadPlugins()
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

/**
 * 清空搜索
 */
const clearSearch = () => {
  searchQuery.value = ''
}

/**
 * 切换管理器显示
 */
const toggleManager = () => {
  showManager.value = !showManager.value
}

/**
 * 关闭管理器
 */
const closeManager = () => {
  emit('close')
}

/**
 * 关闭配置对话框
 */
const closeConfigDialog = () => {
  showConfigDialog.value = false
  currentPlugin.value = null
  pluginSettings.value = {}
}

/**
 * 关闭安装对话框
 */
const closeInstallDialog = () => {
  showInstallDialog.value = false
  installUrl.value = ''
}

/**
 * 获取权限标签
 */
const getPermissionLabel = (permission: any): string => {
  const labels: Record<string, string> = {
    terminal: '终端',
    filesystem: '文件系统',
    network: '网络',
    system: '系统',
    ui: '界面',
    storage: '存储'
  }
  return labels[permission.type] || permission.type
}

/**
 * 获取空状态消息
 */
const getEmptyMessage = (): string => {
  if (searchQuery.value) {
    return `没有找到匹配 "${searchQuery.value}" 的插件`
  }

  switch (activeFilter.value) {
    case 'enabled':
      return '没有已启用的插件'
    case 'disabled':
      return '没有已禁用的插件'
    case 'errors':
      return '没有错误的插件'
    default:
      return '还没有安装任何插件'
  }
}
</script>

<style scoped lang="less">
.plugin-manager {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 13px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  color: white;

  .header-title {
    display: flex;
    align-items: center;
    gap: 8px;

    .plugin-icon {
      font-size: 20px;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .header-actions {
    display: flex;
    gap: 8px;

    .action-btn {
      padding: 4px 8px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      background: transparent;
      color: white;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      &.primary {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    .close-btn {
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
      }
    }
  }
}

.manager-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.search-section {
  margin-bottom: 16px;

  .search-bar {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;

    .search-input {
      flex: 1;
      padding: 6px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 12px;

      &:focus {
        outline: none;
        border-color: #40a9ff;
      }
    }

    .clear-search {
      padding: 6px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: white;
      font-size: 11px;
      cursor: pointer;

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }
  }

  .filter-tabs {
    display: flex;
    gap: 4px;

    .filter-tab {
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: white;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }

      &.active {
        background: #1890ff;
        border-color: #1890ff;
        color: white;
      }
    }
  }
}

.stats-section {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;

    .stat-label {
      font-size: 10px;
      color: #666;
      margin-bottom: 2px;
    }

    .stat-value {
      font-size: 16px;
      font-weight: 600;

      &.enabled {
        color: #52c41a;
      }
      &.disabled {
        color: #999;
      }
      &.error {
        color: #ff4d4f;
      }
    }
  }
}

.plugins-section {
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 0 0 16px 0;
      font-size: 14px;
    }

    .install-first-btn {
      padding: 8px 16px;
      background: #1890ff;
      border: none;
      border-radius: 4px;
      color: white;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        background: #40a9ff;
      }
    }
  }

  .plugins-list {
    .plugin-item {
      margin-bottom: 16px;
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      transition: all 0.2s;

      &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &.enabled {
        border-left: 4px solid #52c41a;
      }

      &.disabled {
        border-left: 4px solid #d9d9d9;
        opacity: 0.7;
      }

      &.error {
        border-left: 4px solid #ff4d4f;
      }

      .plugin-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;

        .plugin-info {
          .plugin-name {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
          }

          .plugin-version,
          .plugin-author {
            font-size: 11px;
            color: #666;
            margin-right: 8px;
          }
        }

        .plugin-controls {
          display: flex;
          gap: 4px;

          .toggle-btn,
          .config-btn,
          .uninstall-btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            background: white;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              border-color: #40a9ff;
              color: #40a9ff;
            }
          }

          .toggle-btn.enabled {
            background: #52c41a;
            border-color: #52c41a;
            color: white;

            &:hover {
              background: #73d13d;
            }
          }

          .uninstall-btn {
            color: #ff4d4f;
            border-color: #ff4d4f;

            &:hover {
              background: #ff4d4f;
              color: white;
            }
          }
        }
      }

      .plugin-description {
        margin: 8px 0;
        color: #666;
        font-size: 12px;
        line-height: 1.4;
      }

      .plugin-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 12px;

        .plugin-tags {
          display: flex;
          gap: 4px;
          flex-wrap: wrap;

          .plugin-tag {
            padding: 2px 6px;
            background: #f0f0f0;
            border-radius: 10px;
            font-size: 10px;
            color: #666;
          }
        }

        .plugin-links {
          display: flex;
          gap: 8px;

          .plugin-link {
            font-size: 10px;
            color: #1890ff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .plugin-error {
        margin-top: 8px;
        padding: 6px 8px;
        background: #fff2f0;
        border-radius: 4px;
        color: #ff4d4f;
        font-size: 11px;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .plugin-permissions {
        margin-top: 12px;
        padding-top: 8px;
        border-top: 1px solid #f0f0f0;

        h5 {
          margin: 0 0 6px 0;
          font-size: 11px;
          color: #666;
        }

        .permissions-list {
          display: flex;
          gap: 4px;
          flex-wrap: wrap;

          .permission-item {
            padding: 2px 6px;
            background: #fff7e6;
            border-radius: 10px;
            font-size: 10px;
            color: #fa8c16;
          }
        }
      }
    }
  }
}

.plugin-toggle {
  position: fixed;
  bottom: 230px; // 避免与其他按钮重叠
  right: 20px;
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transition: all 0.3s;
  position: relative;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .plugin-icon {
    font-size: 24px;
    margin-bottom: 2px;
  }

  .plugin-text {
    font-size: 10px;
    color: white;
    font-weight: 500;
  }

  .update-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    background: #ff4d4f;
    border-radius: 50%;
    border: 2px solid white;
  }
}

.config-dialog-overlay,
.install-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;

  .config-dialog,
  .install-dialog {
    background: white;
    border-radius: 8px;
    width: 400px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #f0f0f0;
          border-radius: 4px;
        }
      }
    }

    .dialog-content {
      padding: 20px;
      max-height: 50vh;
      overflow-y: auto;

      .settings-form {
        .setting-item {
          margin-bottom: 16px;

          .setting-label {
            display: block;
            margin-bottom: 6px;
            font-size: 12px;
            font-weight: 500;

            .setting-description {
              display: block;
              font-size: 11px;
              color: #666;
              font-weight: normal;
              margin-top: 2px;
            }
          }

          .setting-input,
          .setting-select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;

            &:focus {
              outline: none;
              border-color: #40a9ff;
            }
          }

          .setting-checkbox {
            margin-right: 6px;
          }
        }
      }

      .no-settings {
        text-align: center;
        color: #666;
        font-size: 12px;
        padding: 20px;
      }

      .install-methods {
        .method-item {
          margin-bottom: 20px;
          padding-bottom: 16px;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          h5 {
            margin: 0 0 8px 0;
            font-size: 13px;
            font-weight: 600;
          }

          .file-input,
          .url-input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 8px;

            &:focus {
              outline: none;
              border-color: #40a9ff;
            }
          }

          .install-btn,
          .marketplace-btn {
            padding: 6px 12px;
            background: #1890ff;
            border: none;
            border-radius: 4px;
            color: white;
            font-size: 11px;
            cursor: pointer;

            &:hover {
              background: #40a9ff;
            }
          }

          .marketplace-btn {
            background: #52c41a;

            &:hover {
              background: #73d13d;
            }
          }
        }
      }
    }

    .dialog-actions {
      display: flex;
      gap: 8px;
      padding: 16px 20px;
      background: #fafafa;
      border-top: 1px solid #f0f0f0;

      .btn-cancel,
      .btn-save {
        flex: 1;
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .btn-cancel {
        background: white;
        color: #666;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
        }
      }

      .btn-save {
        background: #1890ff;
        border-color: #1890ff;
        color: white;

        &:hover {
          background: #40a9ff;
        }
      }
    }
  }
}
</style>
