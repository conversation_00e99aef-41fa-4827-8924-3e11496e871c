<template>
  <div class="cloud-integration-wrapper">
    <div
      class="cloud-panel"
      v-if="showPanel"
    >
      <div class="panel-header">
        <h3>云端集成</h3>
        <button
          @click="closePanel"
          class="close-btn"
          >×</button
        >
      </div>

      <div class="panel-content">
        <!-- 云服务提供商管理 -->
        <div class="providers-section">
          <div class="section-header">
            <h4>云服务提供商</h4>
            <button
              @click="showAddProvider = true"
              class="add-btn"
              >+ 添加</button
            >
          </div>

          <div class="providers-list">
            <div
              v-for="provider in providers"
              :key="provider.id"
              class="provider-item"
              :class="{ active: selectedProvider?.id === provider.id }"
              @click="selectProvider(provider)"
            >
              <div class="provider-info">
                <span class="provider-name">{{ provider.name }}</span>
                <span class="provider-type">{{ getProviderTypeName(provider.type) }}</span>
                <span class="provider-region">{{ provider.region }}</span>
              </div>
              <div class="provider-actions">
                <button
                  @click.stop="refreshProvider(provider)"
                  class="action-btn"
                  >刷新</button
                >
                <button
                  @click.stop="removeProvider(provider)"
                  class="action-btn danger"
                  >删除</button
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 云实例管理 -->
        <div
          class="instances-section"
          v-if="selectedProvider"
        >
          <div class="section-header">
            <h4>云实例 ({{ selectedProvider.name }})</h4>
            <button
              @click="showCreateInstance = true"
              class="add-btn"
              >+ 创建实例</button
            >
          </div>

          <div class="instances-list">
            <div
              v-for="instance in filteredInstances"
              :key="instance.id"
              class="instance-item"
              :class="instance.status"
            >
              <div class="instance-info">
                <div class="instance-name">{{ instance.name }}</div>
                <div class="instance-details">
                  <span class="instance-type">{{ instance.type }}</span>
                  <span
                    class="instance-status"
                    :class="instance.status"
                    >{{ getStatusText(instance.status) }}</span
                  >
                  <span
                    class="instance-ip"
                    v-if="instance.publicIp"
                    >{{ instance.publicIp }}</span
                  >
                </div>
                <div
                  class="instance-cost"
                  v-if="instance.cost"
                >
                  ${{ instance.cost.hourly }}/小时 | ${{ instance.cost.monthly }}/月
                </div>
              </div>
              <div class="instance-actions">
                <button
                  @click="connectToInstance(instance)"
                  class="action-btn primary"
                  v-if="instance.status === 'running'"
                  >连接</button
                >
                <button
                  @click="startInstance(instance)"
                  class="action-btn"
                  v-if="instance.status === 'stopped'"
                  >启动</button
                >
                <button
                  @click="stopInstance(instance)"
                  class="action-btn"
                  v-if="instance.status === 'running'"
                  >停止</button
                >
                <button
                  @click="deleteInstance(instance)"
                  class="action-btn danger"
                  >删除</button
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 云服务监控 -->
        <div
          class="monitoring-section"
          v-if="selectedProvider"
        >
          <h4>服务监控</h4>
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-title">总实例数</div>
              <div class="metric-value">{{ filteredInstances.length }}</div>
            </div>
            <div class="metric-card">
              <div class="metric-title">运行中</div>
              <div class="metric-value">{{ runningInstances }}</div>
            </div>
            <div class="metric-card">
              <div class="metric-title">月度成本</div>
              <div class="metric-value">${{ totalMonthlyCost.toFixed(2) }}</div>
            </div>
            <div class="metric-card">
              <div class="metric-title">区域</div>
              <div class="metric-value">{{ uniqueRegions }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加提供商对话框 -->
    <div
      class="modal-overlay"
      v-if="showAddProvider"
      @click="showAddProvider = false"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <h3>添加云服务提供商</h3>
        <form @submit.prevent="addProvider">
          <div class="form-group">
            <label>提供商类型</label>
            <select
              v-model="newProvider.type"
              required
            >
              <option value="aws">Amazon Web Services</option>
              <option value="azure">Microsoft Azure</option>
              <option value="gcp">Google Cloud Platform</option>
              <option value="alibaba">阿里云</option>
              <option value="tencent">腾讯云</option>
            </select>
          </div>
          <div class="form-group">
            <label>名称</label>
            <input
              v-model="newProvider.name"
              type="text"
              required
            />
          </div>
          <div class="form-group">
            <label>区域</label>
            <input
              v-model="newProvider.region"
              type="text"
              required
            />
          </div>
          <div class="form-group">
            <label>Access Key</label>
            <input
              v-model="newProvider.credentials.accessKey"
              type="text"
              required
            />
          </div>
          <div class="form-group">
            <label>Secret Key</label>
            <input
              v-model="newProvider.credentials.secretKey"
              type="password"
              required
            />
          </div>
          <div class="form-actions">
            <button
              type="button"
              @click="showAddProvider = false"
              >取消</button
            >
            <button
              type="submit"
              class="primary"
              >添加</button
            >
          </div>
        </form>
      </div>
    </div>

    <!-- 创建实例对话框 -->
    <div
      class="modal-overlay"
      v-if="showCreateInstance"
      @click="showCreateInstance = false"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <h3>创建云实例</h3>
        <form @submit.prevent="createInstance">
          <div class="form-group">
            <label>实例名称</label>
            <input
              v-model="newInstance.name"
              type="text"
              required
            />
          </div>
          <div class="form-group">
            <label>实例类型</label>
            <select
              v-model="newInstance.instanceType"
              required
            >
              <option value="t3.micro">t3.micro (1 vCPU, 1GB RAM)</option>
              <option value="t3.small">t3.small (2 vCPU, 2GB RAM)</option>
              <option value="t3.medium">t3.medium (2 vCPU, 4GB RAM)</option>
              <option value="t3.large">t3.large (2 vCPU, 8GB RAM)</option>
            </select>
          </div>
          <div class="form-group">
            <label>镜像</label>
            <select
              v-model="newInstance.image"
              required
            >
              <option value="ubuntu-20.04">Ubuntu 20.04 LTS</option>
              <option value="ubuntu-22.04">Ubuntu 22.04 LTS</option>
              <option value="centos-7">CentOS 7</option>
              <option value="amazon-linux-2">Amazon Linux 2</option>
            </select>
          </div>
          <div class="form-actions">
            <button
              type="button"
              @click="showCreateInstance = false"
              >取消</button
            >
            <button
              type="submit"
              class="primary"
              >创建</button
            >
          </div>
        </form>
      </div>
    </div>

    <!-- 浮动按钮 -->
    <div
      class="cloud-toggle"
      v-if="!showPanel"
      @click="togglePanel"
    >
      <span class="cloud-icon">☁️</span>
      <span class="cloud-text">云端</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { cloudIntegration, type CloudProvider, type CloudInstance, type DeploymentConfig } from '@/utils/cloudIntegration'

const emit = defineEmits<{
  close: []
}>()

const showPanel = ref(true) // 默认显示，因为是通过工具栏打开的
const showAddProvider = ref(false)
const showCreateInstance = ref(false)
const providers = ref<CloudProvider[]>([])
const instances = ref<CloudInstance[]>([])
const selectedProvider = ref<CloudProvider | null>(null)

// 新提供商表单
const newProvider = ref({
  type: 'aws' as const,
  name: '',
  region: '',
  credentials: {
    accessKey: '',
    secretKey: ''
  }
})

// 新实例表单
const newInstance = ref({
  name: '',
  instanceType: 't3.micro',
  image: 'ubuntu-20.04'
})

// 计算属性
const filteredInstances = computed(() => {
  if (!selectedProvider.value) return []
  return instances.value.filter((instance) => instance.provider === selectedProvider.value!.id)
})

const runningInstances = computed(() => {
  return filteredInstances.value.filter((instance) => instance.status === 'running').length
})

const totalMonthlyCost = computed(() => {
  return filteredInstances.value.reduce((total, instance) => {
    return total + (instance.cost?.monthly || 0)
  }, 0)
})

const uniqueRegions = computed(() => {
  const regions = new Set(filteredInstances.value.map((instance) => instance.region))
  return regions.size
})

onMounted(() => {
  loadProviders()
  setupEventListeners()
})

const loadProviders = () => {
  providers.value = cloudIntegration.getProviders()
  instances.value = cloudIntegration.getAllInstances()
}

const setupEventListeners = () => {
  cloudIntegration.on('providerAdded', (provider: CloudProvider) => {
    providers.value.push(provider)
  })

  cloudIntegration.on('instanceCreated', (instance: CloudInstance) => {
    instances.value.push(instance)
  })

  cloudIntegration.on('instanceStatusChanged', (instance: CloudInstance) => {
    const index = instances.value.findIndex((i) => i.id === instance.id)
    if (index !== -1) {
      instances.value[index] = instance
    }
  })

  cloudIntegration.on('instanceDeleted', (instance: CloudInstance) => {
    const index = instances.value.findIndex((i) => i.id === instance.id)
    if (index !== -1) {
      instances.value.splice(index, 1)
    }
  })
}

const togglePanel = () => {
  if (showPanel.value) {
    emit('close')
  } else {
    showPanel.value = true
  }
}

const closePanel = () => {
  emit('close')
}

const selectProvider = (provider: CloudProvider) => {
  selectedProvider.value = provider
  refreshProvider(provider)
}

const refreshProvider = async (provider: CloudProvider) => {
  try {
    const providerInstances = await cloudIntegration.getInstances(provider.id)
    // 更新实例列表
    instances.value = instances.value.filter((i) => i.provider !== provider.id).concat(providerInstances)
  } catch (error) {
    console.error('刷新提供商失败:', error)
  }
}

const addProvider = async () => {
  try {
    const provider: CloudProvider = {
      id: `${newProvider.value.type}-${Date.now()}`,
      name: newProvider.value.name,
      type: newProvider.value.type,
      region: newProvider.value.region,
      credentials: { ...newProvider.value.credentials }
    }

    await cloudIntegration.addProvider(provider)
    showAddProvider.value = false

    // 重置表单
    newProvider.value = {
      type: 'aws',
      name: '',
      region: '',
      credentials: { accessKey: '', secretKey: '' }
    }
  } catch (error) {
    console.error('添加提供商失败:', error)
    alert('添加提供商失败: ' + error)
  }
}

const createInstance = async () => {
  if (!selectedProvider.value) return

  try {
    const config: DeploymentConfig = {
      name: newInstance.value.name,
      provider: selectedProvider.value.id,
      region: selectedProvider.value.region,
      instanceType: newInstance.value.instanceType,
      image: newInstance.value.image,
      securityGroups: ['default'],
      keyPair: 'default',
      tags: { CreatedBy: 'Chaterm', Environment: 'development' }
    }

    await cloudIntegration.createInstance(config)
    showCreateInstance.value = false

    // 重置表单
    newInstance.value = {
      name: '',
      instanceType: 't3.micro',
      image: 'ubuntu-20.04'
    }
  } catch (error) {
    console.error('创建实例失败:', error)
    alert('创建实例失败: ' + error)
  }
}

const connectToInstance = (instance: CloudInstance) => {
  // 这里应该集成到SSH连接功能
  console.log('连接到实例:', instance)
  // 可以触发一个事件，让主应用创建新的SSH连接
}

const startInstance = (instance: CloudInstance) => {
  console.log('启动实例:', instance.name)
  // 实际实现中应该调用云服务API
}

const stopInstance = (instance: CloudInstance) => {
  console.log('停止实例:', instance.name)
  // 实际实现中应该调用云服务API
}

const deleteInstance = async (instance: CloudInstance) => {
  if (confirm(`确定要删除实例 "${instance.name}" 吗？`)) {
    try {
      await cloudIntegration.deleteInstance(instance.id)
    } catch (error) {
      console.error('删除实例失败:', error)
      alert('删除实例失败: ' + error)
    }
  }
}

const removeProvider = (provider: CloudProvider) => {
  if (confirm(`确定要删除提供商 "${provider.name}" 吗？`)) {
    const index = providers.value.findIndex((p) => p.id === provider.id)
    if (index !== -1) {
      providers.value.splice(index, 1)
      if (selectedProvider.value?.id === provider.id) {
        selectedProvider.value = null
      }
    }
  }
}

const getProviderTypeName = (type: string): string => {
  const names = {
    aws: 'AWS',
    azure: 'Azure',
    gcp: 'GCP',
    alibaba: '阿里云',
    tencent: '腾讯云'
  }
  return names[type] || type
}

const getStatusText = (status: string): string => {
  const texts = {
    running: '运行中',
    stopped: '已停止',
    pending: '启动中',
    terminated: '已终止'
  }
  return texts[status] || status
}
</script>

<style scoped lang="less">
.cloud-integration-wrapper {
  position: relative;
}

.cloud-panel {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px 8px 0 0;

  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;

    &:hover {
      color: #666;
    }
  }
}

.panel-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.providers-section,
.instances-section,
.monitoring-section {
  margin-bottom: 20px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 13px;
    font-weight: 600;
    color: #333;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .add-btn {
    padding: 4px 8px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;

    &:hover {
      background: #40a9ff;
    }
  }
}

.providers-list,
.instances-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.provider-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #d9d9d9;
    background: #fafafa;
  }

  &.active {
    border-color: #1890ff;
    background: #f6ffed;
  }
}

.provider-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .provider-name {
    font-weight: 600;
    font-size: 13px;
  }

  .provider-type,
  .provider-region {
    font-size: 11px;
    color: #666;
  }
}

.provider-actions,
.instance-actions {
  display: flex;
  gap: 6px;
}

.action-btn {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s;

  &:hover {
    border-color: #40a9ff;
    color: #40a9ff;
  }

  &.primary {
    background: #1890ff;
    border-color: #1890ff;
    color: white;

    &:hover {
      background: #40a9ff;
    }
  }

  &.danger {
    &:hover {
      border-color: #ff4d4f;
      color: #ff4d4f;
    }
  }
}

.instance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s;

  &.running {
    border-left: 4px solid #52c41a;
  }

  &.stopped {
    border-left: 4px solid #faad14;
  }

  &.pending {
    border-left: 4px solid #1890ff;
  }

  &.terminated {
    border-left: 4px solid #ff4d4f;
  }
}

.instance-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .instance-name {
    font-weight: 600;
    font-size: 13px;
  }

  .instance-details {
    display: flex;
    gap: 8px;
    font-size: 11px;
    color: #666;

    .instance-status {
      &.running {
        color: #52c41a;
      }
      &.stopped {
        color: #faad14;
      }
      &.pending {
        color: #1890ff;
      }
      &.terminated {
        color: #ff4d4f;
      }
    }
  }

  .instance-cost {
    font-size: 10px;
    color: #999;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.metric-card {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  text-align: center;

  .metric-title {
    font-size: 11px;
    color: #666;
    margin-bottom: 4px;
  }

  .metric-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  width: 400px;
  max-width: 90vw;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
  }
}

.form-group {
  margin-bottom: 12px;

  label {
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
    font-weight: 600;
    color: #333;
  }

  input,
  select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;

    &:focus {
      outline: none;
      border-color: #40a9ff;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;

  button {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 12px;

    &.primary {
      background: #1890ff;
      border-color: #1890ff;
      color: white;
    }
  }
}

.cloud-toggle {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transition: all 0.3s;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .cloud-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }

  .cloud-text {
    font-size: 10px;
    color: white;
    font-weight: 500;
  }
}
</style>
