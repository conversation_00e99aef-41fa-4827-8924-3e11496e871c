<template>
  <div
    class="collaboration-panel"
    v-if="showPanel"
  >
    <div class="panel-header">
      <div class="header-title">
        <span class="collab-icon">👥</span>
        <h3>协作会话</h3>
      </div>
      <div class="header-actions">
        <button
          @click="toggleChat"
          :class="{ active: showChat }"
          class="toggle-btn"
        >
          聊天
        </button>
        <button
          @click="closePanel"
          class="close-btn"
          >×</button
        >
      </div>
    </div>

    <div class="panel-content">
      <!-- 会话状态 -->
      <div
        v-if="currentSession"
        class="session-info"
      >
        <div class="session-header">
          <h4>{{ currentSession.name }}</h4>
          <span
            class="session-status"
            :class="currentSession.status"
          >
            {{ getStatusLabel(currentSession.status) }}
          </span>
        </div>
        <p class="session-description">{{ currentSession.description }}</p>

        <!-- 参与者列表 -->
        <div class="participants-section">
          <h5>参与者 ({{ currentSession.participants.length }})</h5>
          <div class="participants-list">
            <div
              v-for="participant in currentSession.participants"
              :key="participant.id"
              class="participant-item"
              :class="participant.status"
            >
              <div class="participant-info">
                <span class="participant-name">{{ participant.name }}</span>
                <span
                  class="participant-role"
                  :class="participant.role"
                >
                  {{ getRoleLabel(participant.role) }}
                </span>
              </div>
              <div class="participant-status">
                <span
                  class="status-dot"
                  :class="participant.status"
                ></span>
                <span class="status-text">{{ getParticipantStatusLabel(participant.status) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 会话控制 -->
        <div class="session-controls">
          <button
            @click="inviteUser"
            class="control-btn primary"
            >邀请用户</button
          >
          <button
            @click="shareSession"
            class="control-btn"
            >分享会话</button
          >
          <button
            @click="leaveSession"
            class="control-btn danger"
            >离开会话</button
          >
        </div>
      </div>

      <!-- 创建/加入会话 -->
      <div
        v-else
        class="session-actions"
      >
        <div class="action-section">
          <h4>创建新会话</h4>
          <form
            @submit.prevent="createSession"
            class="create-form"
          >
            <input
              v-model="newSession.name"
              placeholder="会话名称"
              required
              class="form-input"
            />
            <textarea
              v-model="newSession.description"
              placeholder="会话描述（可选）"
              class="form-textarea"
            ></textarea>
            <div class="form-options">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  v-model="newSession.allowViewers"
                />
                允许观察者
              </label>
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  v-model="newSession.recordSession"
                />
                录制会话
              </label>
            </div>
            <button
              type="submit"
              class="form-submit"
              >创建会话</button
            >
          </form>
        </div>

        <div class="divider">或</div>

        <div class="action-section">
          <h4>加入现有会话</h4>
          <form
            @submit.prevent="joinSession"
            class="join-form"
          >
            <input
              v-model="joinSessionId"
              placeholder="会话ID"
              required
              class="form-input"
            />
            <input
              v-model="joinPassword"
              type="password"
              placeholder="密码（如需要）"
              class="form-input"
            />
            <button
              type="submit"
              class="form-submit"
              >加入会话</button
            >
          </form>
        </div>
      </div>

      <!-- 聊天区域 -->
      <div
        v-if="showChat && currentSession"
        class="chat-section"
      >
        <div class="chat-header">
          <h5>团队聊天</h5>
          <button
            @click="clearChat"
            class="clear-chat"
            >清空</button
          >
        </div>

        <div
          class="chat-messages"
          ref="chatMessages"
        >
          <div
            v-for="message in chatMessages"
            :key="message.id"
            class="chat-message"
            :class="{ own: message.userId === currentUserId }"
          >
            <div class="message-header">
              <span class="message-author">{{ message.userName }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
            <div
              class="message-content"
              :class="message.type"
            >
              <span
                v-if="message.type === 'command'"
                class="command-prefix"
                >$</span
              >
              {{ message.content }}
            </div>
          </div>
        </div>

        <div class="chat-input">
          <input
            v-model="chatInput"
            @keyup.enter="sendChatMessage"
            placeholder="输入消息..."
            class="chat-input-field"
          />
          <button
            @click="sendChatMessage"
            class="send-btn"
            >发送</button
          >
        </div>
      </div>
    </div>
  </div>

  <!-- 浮动协作按钮 -->
  <div
    class="collab-toggle"
    v-if="!showPanel"
    @click="togglePanel"
  >
    <span class="collab-icon">👥</span>
    <span class="collab-text">协作</span>
    <span
      v-if="hasNotifications"
      class="notification-dot"
    ></span>
  </div>

  <!-- 邀请用户对话框 -->
  <div
    v-if="showInviteDialog"
    class="invite-dialog-overlay"
    @click="closeInviteDialog"
  >
    <div
      class="invite-dialog"
      @click.stop
    >
      <h4>邀请用户</h4>
      <form @submit.prevent="sendInvitation">
        <input
          v-model="inviteEmail"
          type="email"
          placeholder="用户邮箱"
          required
          class="form-input"
        />
        <select
          v-model="inviteRole"
          class="form-select"
        >
          <option value="viewer">观察者</option>
          <option value="editor">编辑者</option>
          <option value="admin">管理员</option>
        </select>
        <div class="dialog-actions">
          <button
            type="button"
            @click="closeInviteDialog"
            class="btn-cancel"
            >取消</button
          >
          <button
            type="submit"
            class="btn-confirm"
            >发送邀请</button
          >
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { collaborationManager, CollaborationSession, ChatMessage } from '@/utils/collaborationManager'

interface Props {
  connectionInfo?: {
    host: string
    port: number
    username: string
    protocol: string
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  sessionCreated: [session: CollaborationSession]
  sessionJoined: [session: CollaborationSession]
  sessionLeft: []
  close: []
}>()

const showPanel = ref(true) // 默认显示，因为是通过工具栏打开的
const showChat = ref(false)
const showInviteDialog = ref(false)
const hasNotifications = ref(false)

const currentSession = ref<CollaborationSession | null>(null)
const chatMessages = ref<ChatMessage[]>([])
const chatInput = ref('')
const chatMessages_ref = ref<HTMLElement>()

// 创建会话表单
const newSession = ref({
  name: '',
  description: '',
  allowViewers: true,
  recordSession: true
})

// 加入会话表单
const joinSessionId = ref('')
const joinPassword = ref('')

// 邀请用户表单
const inviteEmail = ref('')
const inviteRole = ref<'viewer' | 'editor' | 'admin'>('viewer')

const currentUserId = ref('current-user-id') // 应该从用户状态获取

onMounted(() => {
  setupCollaborationListeners()
  currentSession.value = collaborationManager.getCurrentSession()
})

onUnmounted(() => {
  removeCollaborationListeners()
})

/**
 * 设置协作事件监听
 */
const setupCollaborationListeners = () => {
  collaborationManager.on('sessionCreated', handleSessionCreated)
  collaborationManager.on('sessionJoined', handleSessionJoined)
  collaborationManager.on('sessionLeft', handleSessionLeft)
  collaborationManager.on('participantJoined', handleParticipantJoined)
  collaborationManager.on('participantLeft', handleParticipantLeft)
  collaborationManager.on('chatMessage', handleChatMessage)
  collaborationManager.on('terminalInput', handleTerminalInput)
  collaborationManager.on('terminalOutput', handleTerminalOutput)
}

/**
 * 移除协作事件监听
 */
const removeCollaborationListeners = () => {
  // 这里应该移除所有事件监听器
  // collaborationManager.off('sessionCreated', handleSessionCreated)
  // ... 其他事件
}

/**
 * 创建会话
 */
const createSession = async () => {
  if (!props.connectionInfo) {
    alert('需要连接信息才能创建协作会话')
    return
  }

  try {
    const session = await collaborationManager.createSession(
      newSession.value.name,
      newSession.value.description,
      {
        host: props.connectionInfo.host,
        port: props.connectionInfo.port,
        username: props.connectionInfo.username,
        protocol: props.connectionInfo.protocol as any,
        encrypted: true
      },
      {
        allowViewers: newSession.value.allowViewers,
        recordSession: newSession.value.recordSession
      }
    )

    currentSession.value = session
    emit('sessionCreated', session)

    // 重置表单
    newSession.value = {
      name: '',
      description: '',
      allowViewers: true,
      recordSession: true
    }
  } catch (error) {
    console.error('创建会话失败:', error)
    alert('创建会话失败: ' + (error as Error).message)
  }
}

/**
 * 加入会话
 */
const joinSession = async () => {
  try {
    const session = await collaborationManager.joinSession(joinSessionId.value, joinPassword.value || undefined)

    currentSession.value = session
    emit('sessionJoined', session)

    // 重置表单
    joinSessionId.value = ''
    joinPassword.value = ''
  } catch (error) {
    console.error('加入会话失败:', error)
    alert('加入会话失败: ' + (error as Error).message)
  }
}

/**
 * 离开会话
 */
const leaveSession = async () => {
  if (confirm('确定要离开当前会话吗？')) {
    await collaborationManager.leaveCurrentSession()
    currentSession.value = null
    chatMessages.value = []
    emit('sessionLeft')
  }
}

/**
 * 邀请用户
 */
const inviteUser = () => {
  showInviteDialog.value = true
}

/**
 * 发送邀请
 */
const sendInvitation = async () => {
  try {
    await collaborationManager.inviteUser(inviteEmail.value, inviteRole.value)
    alert('邀请已发送')
    closeInviteDialog()
  } catch (error) {
    console.error('发送邀请失败:', error)
    alert('发送邀请失败: ' + (error as Error).message)
  }
}

/**
 * 关闭邀请对话框
 */
const closeInviteDialog = () => {
  showInviteDialog.value = false
  inviteEmail.value = ''
  inviteRole.value = 'viewer'
}

/**
 * 分享会话
 */
const shareSession = () => {
  if (currentSession.value) {
    const shareUrl = `${window.location.origin}/collaboration/join/${currentSession.value.id}`
    navigator.clipboard.writeText(shareUrl).then(() => {
      alert('会话链接已复制到剪贴板')
    })
  }
}

/**
 * 发送聊天消息
 */
const sendChatMessage = () => {
  if (chatInput.value.trim()) {
    const isCommand = chatInput.value.startsWith('$')
    const content = isCommand ? chatInput.value.substring(1).trim() : chatInput.value.trim()
    const type = isCommand ? 'command' : 'text'

    collaborationManager.sendChatMessage(content, type)
    chatInput.value = ''
  }
}

/**
 * 清空聊天
 */
const clearChat = () => {
  chatMessages.value = []
}

/**
 * 切换面板显示
 */
const togglePanel = () => {
  showPanel.value = !showPanel.value
  if (showPanel.value) {
    hasNotifications.value = false
  }
}

/**
 * 关闭面板
 */
const closePanel = () => {
  emit('close')
}

/**
 * 切换聊天显示
 */
const toggleChat = () => {
  showChat.value = !showChat.value
  if (showChat.value) {
    nextTick(() => {
      scrollChatToBottom()
    })
  }
}

/**
 * 滚动聊天到底部
 */
const scrollChatToBottom = () => {
  if (chatMessages_ref.value) {
    chatMessages_ref.value.scrollTop = chatMessages_ref.value.scrollHeight
  }
}

/**
 * 事件处理器
 */
const handleSessionCreated = (session: CollaborationSession) => {
  console.log('会话已创建:', session)
}

const handleSessionJoined = (session: CollaborationSession) => {
  console.log('已加入会话:', session)
}

const handleSessionLeft = () => {
  console.log('已离开会话')
}

const handleParticipantJoined = (participant: any) => {
  console.log('参与者加入:', participant)
  hasNotifications.value = true
}

const handleParticipantLeft = (participantId: string) => {
  console.log('参与者离开:', participantId)
}

const handleChatMessage = (message: ChatMessage) => {
  chatMessages.value.push(message)
  hasNotifications.value = true
  nextTick(() => {
    scrollChatToBottom()
  })
}

const handleTerminalInput = (data: any) => {
  console.log('终端输入:', data)
}

const handleTerminalOutput = (data: any) => {
  console.log('终端输出:', data)
}

/**
 * 工具函数
 */
const getStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    active: '活跃',
    paused: '暂停',
    ended: '已结束'
  }
  return labels[status] || status
}

const getRoleLabel = (role: string): string => {
  const labels: Record<string, string> = {
    owner: '所有者',
    admin: '管理员',
    editor: '编辑者',
    viewer: '观察者'
  }
  return labels[role] || role
}

const getParticipantStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    away: '离开'
  }
  return labels[status] || status
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString()
}
</script>

<style scoped lang="less">
.collaboration-panel {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 13px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;

  .header-title {
    display: flex;
    align-items: center;
    gap: 8px;

    .collab-icon {
      font-size: 20px;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .header-actions {
    display: flex;
    gap: 8px;

    .toggle-btn {
      padding: 4px 8px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      background: transparent;
      color: white;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover,
      &.active {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    .close-btn {
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
      }
    }
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.session-info {
  .session-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
    }

    .session-status {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;

      &.active {
        background: #f6ffed;
        color: #52c41a;
      }
      &.paused {
        background: #fff7e6;
        color: #fa8c16;
      }
      &.ended {
        background: #fff1f0;
        color: #ff4d4f;
      }
    }
  }

  .session-description {
    margin: 8px 0 16px 0;
    color: #666;
    font-size: 12px;
    line-height: 1.4;
  }
}

.participants-section {
  margin: 16px 0;

  h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #666;
  }

  .participants-list {
    .participant-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 4px;
      background: #fafafa;
      border-radius: 6px;

      .participant-info {
        .participant-name {
          font-weight: 500;
          margin-right: 8px;
        }

        .participant-role {
          padding: 1px 6px;
          border-radius: 8px;
          font-size: 10px;

          &.owner {
            background: #fff2e8;
            color: #fa541c;
          }
          &.admin {
            background: #fff1f0;
            color: #ff4d4f;
          }
          &.editor {
            background: #e6f7ff;
            color: #1890ff;
          }
          &.viewer {
            background: #f6ffed;
            color: #52c41a;
          }
        }
      }

      .participant-status {
        display: flex;
        align-items: center;
        gap: 4px;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.online {
            background: #52c41a;
          }
          &.offline {
            background: #d9d9d9;
          }
          &.away {
            background: #faad14;
          }
        }

        .status-text {
          font-size: 10px;
          color: #999;
        }
      }
    }
  }
}

.session-controls {
  display: flex;
  gap: 8px;
  margin-top: 16px;

  .control-btn {
    flex: 1;
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: white;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }

    &.primary {
      background: #1890ff;
      border-color: #1890ff;
      color: white;

      &:hover {
        background: #40a9ff;
      }
    }

    &.danger {
      color: #ff4d4f;
      border-color: #ff4d4f;

      &:hover {
        background: #ff4d4f;
        color: white;
      }
    }
  }
}

.session-actions {
  .action-section {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .divider {
    text-align: center;
    margin: 16px 0;
    color: #999;
    font-size: 12px;
  }

  .form-input,
  .form-textarea,
  .form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 8px;

    &:focus {
      outline: none;
      border-color: #40a9ff;
    }
  }

  .form-textarea {
    resize: vertical;
    min-height: 60px;
  }

  .form-options {
    margin: 8px 0;

    .checkbox-label {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 4px;
      font-size: 11px;
      cursor: pointer;
    }
  }

  .form-submit {
    width: 100%;
    padding: 8px 16px;
    background: #1890ff;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: #40a9ff;
    }
  }
}

.chat-section {
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
  padding-top: 16px;

  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h5 {
      margin: 0;
      font-size: 12px;
      font-weight: 600;
    }

    .clear-chat {
      background: none;
      border: none;
      color: #999;
      font-size: 10px;
      cursor: pointer;

      &:hover {
        color: #666;
      }
    }
  }

  .chat-messages {
    height: 200px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;

    .chat-message {
      margin-bottom: 8px;

      &.own {
        .message-content {
          background: #e6f7ff;
          margin-left: 20px;
        }
      }

      .message-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2px;

        .message-author {
          font-size: 10px;
          font-weight: 500;
          color: #666;
        }

        .message-time {
          font-size: 10px;
          color: #999;
        }
      }

      .message-content {
        padding: 4px 8px;
        background: #f5f5f5;
        border-radius: 4px;
        font-size: 11px;
        line-height: 1.4;

        &.command {
          background: #f6ffed;
          color: #52c41a;
          font-family: monospace;

          .command-prefix {
            color: #999;
            margin-right: 4px;
          }
        }
      }
    }
  }

  .chat-input {
    display: flex;
    gap: 8px;

    .chat-input-field {
      flex: 1;
      padding: 6px 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 11px;

      &:focus {
        outline: none;
        border-color: #40a9ff;
      }
    }

    .send-btn {
      padding: 6px 12px;
      background: #1890ff;
      border: none;
      border-radius: 4px;
      color: white;
      font-size: 11px;
      cursor: pointer;

      &:hover {
        background: #40a9ff;
      }
    }
  }
}

.collab-toggle {
  position: fixed;
  bottom: 160px; // 避免与其他按钮重叠
  right: 20px;
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transition: all 0.3s;
  position: relative;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .collab-icon {
    font-size: 24px;
    margin-bottom: 2px;
  }

  .collab-text {
    font-size: 10px;
    color: white;
    font-weight: 500;
  }

  .notification-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    background: #ff4d4f;
    border-radius: 50%;
    border: 2px solid white;
  }
}

.invite-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;

  .invite-dialog {
    background: white;
    border-radius: 8px;
    padding: 20px;
    width: 300px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
    }

    .dialog-actions {
      display: flex;
      gap: 8px;
      margin-top: 16px;

      .btn-cancel,
      .btn-confirm {
        flex: 1;
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .btn-cancel {
        background: white;
        color: #666;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
        }
      }

      .btn-confirm {
        background: #1890ff;
        border-color: #1890ff;
        color: white;

        &:hover {
          background: #40a9ff;
        }
      }
    }
  }
}
</style>
