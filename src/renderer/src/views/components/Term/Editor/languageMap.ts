export const LanguageMap: Record<string, string> = {
  '.abap': 'abap',
  '.apex': 'apex',
  '.azcli': 'azcli',
  '.bat': 'bat',
  '.bicep': 'bicep',
  '.cameligo': 'cameligo',
  '.clojure': 'clojure',
  '.coffee': 'coffee',
  '.cpp': 'cpp',
  '.csharp': 'csharp',
  '.csp': 'csp',
  '.css': 'css',
  '.dart': 'dart',
  '.dockerfile': 'dockerfile',
  '.ecl': 'ecl',
  '.elixir': 'elixir',
  '.flow9': 'flow9',
  '.freemarker2': 'freemarker2',
  '.fsharp': 'fsharp',
  '.go': 'go',
  '.graphql': 'graphql',
  '.handlebars': 'handlebars',
  '.hcl': 'hcl',
  '.html': 'html',
  '.ini': 'ini',
  '.java': 'java',
  '.javascript': 'javascript',
  '.json': 'json',
  '.julia': 'julia',
  '.kotlin': 'kotlin',
  '.less': 'less',
  '.lexon': 'lexon',
  '.liquid': 'liquid',
  '.lua': 'lua',
  '.m3': 'm3',
  '.markdown': 'markdown',
  '.mips': 'mips',
  '.msdax': 'msdax',
  '.mysql': 'mysql',
  '.objective': 'objective',
  '.c': 'c',
  '.pascal': 'pascal',
  '.pascaligo': 'pascaligo',
  '.perl': 'perl',
  '.pgsql': 'pgsql',
  '.php': 'php',
  '.pla': 'pla',
  '.postiats': 'postiats',
  '.powerquery': 'powerquery',
  '.powershell': 'powershell',
  '.protobuf': 'protobuf',
  '.pug': 'pug',
  '.python': 'python',
  '.py': 'python',
  '.qsharp': 'qsharp',
  '.r': 'r',
  '.razor': 'razor',
  '.redis': 'redis',
  '.redshift': 'redshift',
  '.restructuredtext': 'restructuredtext',
  '.ruby': 'ruby',
  '.rust': 'rust',
  '.sb': 'sb',
  '.scala': 'scala',
  '.scheme': 'scheme',
  '.scss': 'scss',
  '.shell': 'shell',
  '.solidity': 'solidity',
  '.sophia': 'sophia',
  '.sparql': 'sparql',
  '.sql': 'sql',
  '.st': 'st',
  '.swift': 'swift',
  '.systemverilog': 'systemverilog',
  '.tcl': 'tcl',
  '.twig': 'twig',
  '.typescript': 'typescript',
  '.vb': 'vb',
  '.xml': 'xml',
  '.yaml': 'yaml'
}
