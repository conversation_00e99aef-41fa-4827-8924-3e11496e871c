<template>
  <div
    class="user-config"
    style="background: #1a1a1a !important; color: #ffffff !important; padding: 20px !important; min-height: 500px !important"
  >
    <div
      class="user-config-title"
      style="
        background: yellow !important;
        color: black !important;
        padding: 10px !important;
        font-size: 18px !important;
        font-weight: bold !important;
      "
      >设置页面测试 - {{ $t('common.userConfig') }}</div
    >
    <a-divider style="border-color: #666666; margin: 10px 0" />
    <div
      class="tabs-container"
      style="background: #2a2a2a !important; padding: 10px !important; min-height: 400px !important"
    >
      <!-- 临时简化版本，只显示基本内容 -->
      <div style="color: white !important; padding: 20px !important">
        <h2 style="color: yellow !important; margin-bottom: 20px !important">设置页面内容区域</h2>
        <p style="color: white !important; font-size: 16px !important">这里应该显示设置选项卡</p>
        <div style="background: #333 !important; padding: 15px !important; margin: 10px 0 !important; border-radius: 5px !important">
          <h3 style="color: #4caf50 !important">测试内容 1</h3>
          <p style="color: #ccc !important">如果你能看到这个内容，说明组件基本结构是正常的</p>
        </div>
        <div style="background: #333 !important; padding: 15px !important; margin: 10px 0 !important; border-radius: 5px !important">
          <h3 style="color: #2196f3 !important">测试内容 2</h3>
          <p style="color: #ccc !important">接下来我们会逐步添加真正的设置组件</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

// 暂时移除所有组件导入，先测试基本结构

// 添加调试信息
onMounted(() => {
  console.log('=== UserConfig component mounted successfully ===')
  console.log('Current theme class:', document.documentElement.className)
  console.log('Document body style:', {
    backgroundColor: getComputedStyle(document.body).backgroundColor,
    color: getComputedStyle(document.body).color,
    display: getComputedStyle(document.body).display
  })
  console.log('CSS variables:', {
    bgColor: getComputedStyle(document.documentElement).getPropertyValue('--bg-color'),
    textColor: getComputedStyle(document.documentElement).getPropertyValue('--text-color'),
    borderColor: getComputedStyle(document.documentElement).getPropertyValue('--border-color')
  })

  // 检查组件是否正确渲染
  setTimeout(() => {
    const userConfigElement = document.querySelector('.user-config')
    console.log('UserConfig element found:', userConfigElement)
    if (userConfigElement) {
      console.log('UserConfig element styles:', {
        display: getComputedStyle(userConfigElement).display,
        visibility: getComputedStyle(userConfigElement).visibility,
        opacity: getComputedStyle(userConfigElement).opacity,
        backgroundColor: getComputedStyle(userConfigElement).backgroundColor,
        color: getComputedStyle(userConfigElement).color
      })
    }
  }, 1000)
})
</script>

<style lang="less" scoped>
.user-config {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  /* 临时调试样式 */
  border: 2px solid red !important;
  color: #000000 !important;
}

.user-config-title {
  line-height: 30px;
  font-size: 16px;
  font-weight: 600;
  margin-left: 10px;
  flex-shrink: 0;
  color: var(--text-color);
  /* 临时调试样式 */
  background-color: yellow !important;
  color: #000000 !important;
  border: 1px solid blue !important;
}

.tabs-container {
  flex: 1;
  overflow: hidden;
  /* 临时调试样式 */
  background-color: lightgreen !important;
  border: 2px solid purple !important;
}

.user-config-tab {
  color: var(--text-color);
  height: 100%;
  /* 临时调试样式 */
  background-color: lightblue !important;
  border: 2px solid orange !important;

  :deep(.ant-tabs) {
    height: 100%;
    /* 临时调试样式 */
    background-color: pink !important;
  }

  :deep(.ant-tabs-content) {
    height: 100%;
  }

  :deep(.ant-tabs-nav) {
    height: 100%;
    width: 120px;
    background-color: var(--bg-color);

    &::before {
      display: none;
    }
  }

  :deep(.ant-tabs-content-holder) {
    height: 100%;
    overflow: auto;
    background-color: var(--bg-color);

    &::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  :deep(.ant-tabs-tabpane) {
    padding-left: 0 !important;
    height: 100%;
    overflow: auto;
    background-color: var(--bg-color);

    &::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  :deep(.ant-tabs-nav-list) {
    border-right: 1px solid var(--bg-color);
    height: 100%;
  }

  :deep(.ant-tabs-tab) {
    padding: 8px 16px !important;
    margin: 0 !important;
    min-height: 40px;
    font-size: 14px;
    color: var(--text-color-secondary);
  }

  :deep(.ant-tabs-tab-active) {
    background-color: var(--hover-bg-color);
    .ant-tabs-tab-btn {
      color: #1890ff !important;
    }
  }

  :deep(.ant-tabs-content-holder) {
    height: 100%;
    overflow: auto;
  }

  :deep(.ant-tabs-tabpane) {
    height: 100%;
  }
}
</style>
