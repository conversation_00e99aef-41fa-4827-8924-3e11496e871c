<template>
  <div>
    <div class="section-header">
      <h3>{{ $t('user.modelNames') }}</h3>
    </div>
    <a-card
      class="settings-section"
      :bordered="false"
    >
      <div class="model-list">
        <div
          v-for="model in modelOptions"
          :key="model.id"
          class="model-item"
        >
          <a-checkbox
            v-model:checked="model.checked"
            @change="handleModelChange(model)"
          >
            <span class="model-label">
              <img
                v-if="model.name.endsWith('-Thinking')"
                src="@/assets/icons/thinking.svg"
                alt="Thinking"
                class="thinking-icon"
              />
              {{ model.name.replace(/-Thinking$/, '') }}
            </span>
          </a-checkbox>
          <a-button
            v-if="model.checked && model.type === 'custom'"
            type="text"
            class="remove-button"
            @click="removeModel(model)"
          >
            <span class="remove-icon">×</span>
          </a-button>
        </div>
      </div>
    </a-card>
    <div>
      <div class="section-header">
        <h3>{{ $t('user.apiConfiguration') }}</h3>
      </div>

      <!-- DeepSeek Configuration -->
      <a-card
        class="settings-section"
        :bordered="false"
      >
        <div class="api-provider-header">
          <h4>DeepSeek</h4>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.deepSeekApiKey')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input-password
              v-model:value="deepSeekApiKey"
              :placeholder="$t('user.deepSeekApiKeyPh')"
            />
            <p class="setting-description-no-padding">
              {{ $t('user.deepSeekApiKeyDescribe') }}
            </p>
            <!-- API密钥检测和保存按钮 -->
            <div class="api-action-buttons">
              <a-button
                class="check-btn"
                size="small"
                :loading="checkLoadingDeepSeek"
                @click="handleCheck('deepseek')"
              >
                检测连接
              </a-button>
              <a-button
                class="save-btn"
                size="small"
                @click="saveDeepSeekConfig"
              >
                保存配置
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 已添加的模型列表 -->
        <div
          v-if="deepSeekModels.length > 0"
          class="setting-item"
        >
          <a-form-item
            label="已添加的模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-list">
              <div
                v-for="model in deepSeekModels"
                :key="model.id"
                class="model-item"
              >
                {{ model.name }}
                <a-button
                  type="text"
                  class="remove-button"
                  @click="removeDeepSeekModel(model.id)"
                >
                  <span class="remove-icon">×</span>
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 预设模型快速添加 -->
        <div class="setting-item">
          <a-form-item
            label="预设模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="preset-models">
              <a-button
                v-for="preset in deepSeekPresetModels"
                :key="preset.id"
                size="small"
                class="preset-model-btn"
                :disabled="deepSeekModels.some((m) => m.name === preset.name)"
                @click="addDeepSeekPresetModel(preset)"
              >
                {{ preset.name }}
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 添加新模型 -->
        <div class="setting-item">
          <a-form-item
            label="添加新模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-input-container">
              <a-input
                v-model:value="deepSeekNewModelInput"
                size="small"
                class="model-input"
                placeholder="输入模型名称"
                @keydown.enter="addDeepSeekNewModel"
              />
              <div class="button-group">
                <a-button
                  class="save-btn"
                  size="small"
                  @click="addDeepSeekNewModel"
                >
                  添加
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-card>

      <!-- LiteLLM Configuration -->
      <a-card
        class="settings-section"
        :bordered="false"
      >
        <div class="api-provider-header">
          <h4>LiteLLM</h4>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.liteLlmBaseUrl')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              v-model:value="liteLlmBaseUrl"
              :placeholder="$t('user.liteLlmBaseUrlPh')"
            />
          </a-form-item>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.liteLlmApiKey')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input-password
              v-model:value="liteLlmApiKey"
              :placeholder="$t('user.liteLlmApiKeyPh')"
            />
            <p class="setting-description-no-padding">
              {{ $t('user.liteLlmApiKeyDescribe') }}
            </p>
            <!-- API密钥检测和保存按钮 -->
            <div class="api-action-buttons">
              <a-button
                class="check-btn"
                size="small"
                :loading="checkLoadingLiteLLM"
                @click="handleCheck('litellm')"
              >
                检测连接
              </a-button>
              <a-button
                class="save-btn"
                size="small"
                @click="saveLiteLLMConfig"
              >
                保存配置
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 已添加的模型列表 -->
        <div
          v-if="liteLlmModels.length > 0"
          class="setting-item"
        >
          <a-form-item
            label="已添加的模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-list">
              <div
                v-for="model in liteLlmModels"
                :key="model.id"
                class="model-item"
              >
                {{ model.name }}
                <a-button
                  type="text"
                  class="remove-button"
                  @click="removeLiteLlmModel(model.id)"
                >
                  <span class="remove-icon">×</span>
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 预设模型快速添加 -->
        <div class="setting-item">
          <a-form-item
            label="预设模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="preset-models">
              <a-button
                v-for="preset in liteLlmPresetModels"
                :key="preset.id"
                size="small"
                class="preset-model-btn"
                :disabled="liteLlmModels.some((m) => m.name === preset.name)"
                @click="addLiteLlmPresetModel(preset)"
              >
                {{ preset.name }}
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 添加新模型 -->
        <div class="setting-item">
          <a-form-item
            label="添加新模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-input-container">
              <a-input
                v-model:value="liteLlmNewModelInput"
                size="small"
                class="model-input"
                placeholder="输入模型名称"
                @keydown.enter="addLiteLlmNewModel"
              />
              <div class="button-group">
                <a-button
                  class="save-btn"
                  size="small"
                  @click="addLiteLlmNewModel"
                >
                  添加
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-card>

      <!-- OpenAI Compatible Configuration -->
      <a-card
        class="settings-section"
        :bordered="false"
      >
        <div class="api-provider-header">
          <h4>OpenAI Compatible</h4>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.openAiBaseUrl')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              v-model:value="openAiBaseUrl"
              :placeholder="$t('user.openAiBaseUrlPh')"
            />
          </a-form-item>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.openAiApiKey')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input-password
              v-model:value="openAiApiKey"
              :placeholder="$t('user.openAiApiKeyPh')"
            />
            <p class="setting-description-no-padding">
              {{ $t('user.openAiApiKeyDescribe') }}
            </p>
            <!-- API密钥检测和保存按钮 -->
            <div class="api-action-buttons">
              <a-button
                class="check-btn"
                size="small"
                :loading="checkLoadingOpenAI"
                @click="handleCheck('openai')"
              >
                检测连接
              </a-button>
              <a-button
                class="save-btn"
                size="small"
                @click="saveOpenAiConfig"
              >
                保存配置
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 已添加的模型列表 -->
        <div
          v-if="openAiModels.length > 0"
          class="setting-item"
        >
          <a-form-item
            label="已添加的模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-list">
              <div
                v-for="model in openAiModels"
                :key="model.id"
                class="model-item"
              >
                {{ model.name }}
                <a-button
                  type="text"
                  class="remove-button"
                  @click="removeOpenAiModel(model.id)"
                >
                  <span class="remove-icon">×</span>
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 预设模型快速添加 -->
        <div class="setting-item">
          <a-form-item
            label="预设模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="preset-models">
              <a-button
                v-for="preset in openAiPresetModels"
                :key="preset.id"
                size="small"
                class="preset-model-btn"
                :disabled="openAiModels.some((m) => m.name === preset.name)"
                @click="addOpenAiPresetModel(preset)"
              >
                {{ preset.name }}
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 添加新模型 -->
        <div class="setting-item">
          <a-form-item
            label="添加新模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-input-container">
              <a-input
                v-model:value="openAiNewModelInput"
                size="small"
                class="model-input"
                placeholder="输入模型名称"
                @keydown.enter="addOpenAiNewModel"
              />
              <div class="button-group">
                <a-button
                  class="save-btn"
                  size="small"
                  @click="addOpenAiNewModel"
                >
                  添加
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-card>

      <!-- Ollama Configuration -->
      <a-card
        class="settings-section"
        :bordered="false"
      >
        <div class="api-provider-header">
          <h4>Ollama</h4>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.ollamaBaseUrl')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              v-model:value="ollamaBaseUrl"
              :placeholder="$t('user.ollamaBaseUrlPh')"
            />
            <p class="setting-description-no-padding">
              {{ $t('user.ollamaBaseUrlDescribe') }}
            </p>
          </a-form-item>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.ollamaApiKey')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input-password
              v-model:value="ollamaApiKey"
              :placeholder="$t('user.ollamaApiKeyPh')"
            />
            <p class="setting-description-no-padding">
              {{ $t('user.ollamaApiKeyDescribe') }}
            </p>
            <!-- API密钥检测和保存按钮 -->
            <div class="api-action-buttons">
              <a-button
                class="check-btn"
                size="small"
                :loading="checkLoadingOllama"
                @click="handleCheck('ollama')"
              >
                检测连接
              </a-button>
              <a-button
                class="save-btn"
                size="small"
                @click="saveOllamaConfig"
              >
                保存配置
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 已添加的模型列表 -->
        <div
          v-if="ollamaModels.length > 0"
          class="setting-item"
        >
          <a-form-item
            label="已添加的模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-list">
              <div
                v-for="model in ollamaModels"
                :key="model.id"
                class="model-item"
              >
                {{ model.name }}
                <a-button
                  type="text"
                  class="remove-button"
                  @click="removeOllamaModel(model.id)"
                >
                  <span class="remove-icon">×</span>
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 预设模型快速添加 -->
        <div class="setting-item">
          <a-form-item
            label="预设模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="preset-models">
              <a-button
                v-for="preset in ollamaPresetModels"
                :key="preset.id"
                size="small"
                class="preset-model-btn"
                :disabled="ollamaModels.some((m) => m.name === preset.name)"
                @click="addOllamaPresetModel(preset)"
              >
                {{ preset.name }}
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 添加新模型 -->
        <div class="setting-item">
          <a-form-item
            label="添加新模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-input-container">
              <a-input
                v-model:value="ollamaNewModelInput"
                size="small"
                class="model-input"
                placeholder="输入模型名称"
                @keydown.enter="addOllamaNewModel"
              />
              <div class="button-group">
                <a-button
                  class="save-btn"
                  size="small"
                  @click="addOllamaNewModel"
                >
                  添加
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-card>

      <!-- 火山引擎 Configuration -->
      <a-card
        class="settings-section"
        :bordered="false"
      >
        <div class="api-provider-header">
          <h4>火山引擎 (Volcengine)</h4>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.volcengineBaseUrl')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              v-model:value="volcengineBaseUrl"
              :placeholder="$t('user.volcengineBaseUrlPh')"
            />
          </a-form-item>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.volcengineApiKey')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input-password
              v-model:value="volcengineApiKey"
              :placeholder="$t('user.volcengineApiKeyPh')"
            />
            <p class="setting-description-no-padding">
              {{ $t('user.volcengineApiKeyDescribe') }}
            </p>
            <!-- API密钥检测和保存按钮 -->
            <div class="api-action-buttons">
              <a-button
                class="check-btn"
                size="small"
                :loading="checkLoadingVolcengine"
                @click="handleCheck('volcengine')"
              >
                检测连接
              </a-button>
              <a-button
                class="save-btn"
                size="small"
                @click="saveVolcengineConfig"
              >
                保存配置
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 已添加的模型列表 -->
        <div
          v-if="volcengineModels.length > 0"
          class="setting-item"
        >
          <a-form-item
            label="已添加的模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-list">
              <div
                v-for="model in volcengineModels"
                :key="model.id"
                class="model-item"
              >
                {{ model.name }}
                <a-button
                  type="text"
                  class="remove-button"
                  @click="removeVolcengineModel(model.id)"
                >
                  <span class="remove-icon">×</span>
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 预设模型快速添加 -->
        <div class="setting-item">
          <a-form-item
            label="预设模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="preset-models">
              <a-button
                v-for="preset in volcenginePresetModels"
                :key="preset.id"
                size="small"
                class="preset-model-btn"
                :disabled="volcengineModels.some((m) => m.name === preset.name)"
                @click="addVolcenginePresetModel(preset)"
              >
                {{ preset.name }}
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 添加新模型 -->
        <div class="setting-item">
          <a-form-item
            label="添加新模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-input-container">
              <a-input
                v-model:value="volcengineNewModelInput"
                size="small"
                class="model-input"
                placeholder="输入模型名称"
                @keydown.enter="addVolcengineNewModel"
              />
              <div class="button-group">
                <a-button
                  class="save-btn"
                  size="small"
                  @click="addVolcengineNewModel"
                >
                  添加
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-card>

      <!-- 阿里云百炼 Configuration -->
      <a-card
        class="settings-section"
        :bordered="false"
      >
        <div class="api-provider-header">
          <h4>阿里云百炼 (Alibaba Cloud)</h4>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.alibabaBaseUrl')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              v-model:value="alibabaBaseUrl"
              :placeholder="$t('user.alibabaBaseUrlPh')"
            />
          </a-form-item>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.alibabaApiKey')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input-password
              v-model:value="alibabaApiKey"
              :placeholder="$t('user.alibabaApiKeyPh')"
            />
            <p class="setting-description-no-padding">
              {{ $t('user.alibabaApiKeyDescribe') }}
            </p>
            <!-- API密钥检测和保存按钮 -->
            <div class="api-action-buttons">
              <a-button
                class="check-btn"
                size="small"
                :loading="checkLoadingAlibaba"
                @click="handleCheck('alibaba')"
              >
                检测连接
              </a-button>
              <a-button
                class="save-btn"
                size="small"
                @click="saveAlibabaConfig"
              >
                保存配置
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 已添加的模型列表 -->
        <div
          v-if="alibabaModels.length > 0"
          class="setting-item"
        >
          <a-form-item
            label="已添加的模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-list">
              <div
                v-for="model in alibabaModels"
                :key="model.id"
                class="model-item"
              >
                {{ model.name }}
                <a-button
                  type="text"
                  class="remove-button"
                  @click="removeAlibabaModel(model.id)"
                >
                  <span class="remove-icon">×</span>
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 预设模型快速添加 -->
        <div class="setting-item">
          <a-form-item
            label="预设模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="preset-models">
              <a-button
                v-for="preset in alibabaPresetModels"
                :key="preset.id"
                size="small"
                class="preset-model-btn"
                :disabled="alibabaModels.some((m) => m.name === preset.name)"
                @click="addAlibabaPresetModel(preset)"
              >
                {{ preset.name }}
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 添加新模型 -->
        <div class="setting-item">
          <a-form-item
            label="添加新模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-input-container">
              <a-input
                v-model:value="alibabaNewModelInput"
                size="small"
                class="model-input"
                placeholder="输入模型名称"
                @keydown.enter="addAlibabaNewModel"
              />
              <div class="button-group">
                <a-button
                  class="save-btn"
                  size="small"
                  @click="addAlibabaNewModel"
                >
                  添加
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-card>

      <!-- 智谱清言 Configuration -->
      <a-card
        class="settings-section"
        :bordered="false"
      >
        <div class="api-provider-header">
          <h4>智谱清言 (GLM)</h4>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.glmBaseUrl')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              v-model:value="glmBaseUrl"
              :placeholder="$t('user.glmBaseUrlPh')"
            />
          </a-form-item>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.glmApiKey')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input-password
              v-model:value="glmApiKey"
              :placeholder="$t('user.glmApiKeyPh')"
            />
            <p class="setting-description-no-padding">
              {{ $t('user.glmApiKeyDescribe') }}
            </p>
            <!-- API密钥检测和保存按钮 -->
            <div class="api-action-buttons">
              <a-button
                class="check-btn"
                size="small"
                :loading="checkLoadingGLM"
                @click="handleCheck('glm')"
              >
                检测连接
              </a-button>
              <a-button
                class="save-btn"
                size="small"
                @click="saveGlmConfig"
              >
                保存配置
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 已添加的模型列表 -->
        <div
          v-if="glmModels.length > 0"
          class="setting-item"
        >
          <a-form-item
            label="已添加的模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-list">
              <div
                v-for="model in glmModels"
                :key="model.id"
                class="model-item"
              >
                {{ model.name }}
                <a-button
                  type="text"
                  class="remove-button"
                  @click="removeGlmModel(model.id)"
                >
                  <span class="remove-icon">×</span>
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 预设模型快速添加 -->
        <div class="setting-item">
          <a-form-item
            label="预设模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="preset-models">
              <a-button
                v-for="preset in glmPresetModels"
                :key="preset.id"
                size="small"
                class="preset-model-btn"
                :disabled="glmModels.some((m) => m.name === preset.name)"
                @click="addGlmPresetModel(preset)"
              >
                {{ preset.name }}
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 添加新模型 -->
        <div class="setting-item">
          <a-form-item
            label="添加新模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-input-container">
              <a-input
                v-model:value="glmNewModelInput"
                size="small"
                class="model-input"
                placeholder="输入模型名称"
                @keydown.enter="addGlmNewModel"
              />
              <div class="button-group">
                <a-button
                  class="save-btn"
                  size="small"
                  @click="addGlmNewModel"
                >
                  添加
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-card>

      <!-- Gemini Configuration -->
      <a-card
        class="settings-section"
        :bordered="false"
      >
        <div class="api-provider-header">
          <h4>Gemini (Google)</h4>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.geminiBaseUrl')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              v-model:value="geminiBaseUrl"
              :placeholder="$t('user.geminiBaseUrlPh')"
            />
          </a-form-item>
        </div>

        <div class="setting-item">
          <a-form-item
            :label="$t('user.geminiApiKey')"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input-password
              v-model:value="geminiApiKey"
              :placeholder="$t('user.geminiApiKeyPh')"
            />
            <p class="setting-description-no-padding">
              {{ $t('user.geminiApiKeyDescribe') }}
            </p>
            <!-- API密钥检测和保存按钮 -->
            <div class="api-action-buttons">
              <a-button
                class="check-btn"
                size="small"
                :loading="checkLoadingGemini"
                @click="handleCheck('gemini')"
              >
                检测连接
              </a-button>
              <a-button
                class="save-btn"
                size="small"
                @click="saveGeminiConfig"
              >
                保存配置
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 已添加的模型列表 -->
        <div
          v-if="geminiModels.length > 0"
          class="setting-item"
        >
          <a-form-item
            label="已添加的模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-list">
              <div
                v-for="model in geminiModels"
                :key="model.id"
                class="model-item"
              >
                {{ model.name }}
                <a-button
                  type="text"
                  class="remove-button"
                  @click="removeGeminiModel(model.id)"
                >
                  <span class="remove-icon">×</span>
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 预设模型快速添加 -->
        <div class="setting-item">
          <a-form-item
            label="预设模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="preset-models">
              <a-button
                v-for="preset in geminiPresetModels"
                :key="preset.id"
                size="small"
                class="preset-model-btn"
                :disabled="geminiModels.some((m) => m.name === preset.name)"
                @click="addGeminiPresetModel(preset)"
              >
                {{ preset.name }}
              </a-button>
            </div>
          </a-form-item>
        </div>

        <!-- 添加新模型 -->
        <div class="setting-item">
          <a-form-item
            label="添加新模型"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <div class="model-input-container">
              <a-input
                v-model:value="geminiNewModelInput"
                size="small"
                class="model-input"
                placeholder="输入模型名称"
                @keydown.enter="addGeminiNewModel"
              />
              <div class="button-group">
                <a-button
                  class="save-btn"
                  size="small"
                  @click="addGeminiNewModel"
                >
                  添加
                </a-button>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue'
import { notification } from 'ant-design-vue'
import { updateGlobalState, getGlobalState, getSecret, storeSecret, getAllExtensionState } from '../../../../agent/storage/state'
import eventBus from '../../../../utils/eventBus'
import i18n from '../../../../locales'
import { getUser } from '../../../../api/user/user'

// Define interface for model options
interface ModelOption {
  id: string
  name: string
  checked: boolean
  type: string
  apiProvider: string
}

// Define interface for default models from API
interface DefaultModel {
  id: string
  name?: string
  provider?: string

  [key: string]: any
}

// Define interface for provider-specific models
interface ProviderModel {
  id: string
  name: string
  provider?: string
}

const { t } = i18n.global
const modelOptions = ref<ModelOption[]>([])

const awsRegionOptions = ref([
  { value: 'us-east-1', label: 'us-east-1' },
  { value: 'us-east-2', label: 'us-east-2' },
  { value: 'us-west-2', label: 'us-west-2' },
  { value: 'ap-south-1', label: 'ap-south-1' },
  { value: 'ap-northeast-1', label: 'ap-northeast-1' },
  { value: 'ap-northeast-2', label: 'ap-northeast-2' },
  { value: 'ap-northeast-3', label: 'ap-northeast-3' },
  { value: 'ap-southeast-1', label: 'ap-southeast-1' },
  { value: 'ap-southeast-2', label: 'ap-southeast-2' },
  { value: 'ca-central-1', label: 'ca-central-1' },
  { value: 'eu-central-1', label: 'eu-central-1' },
  { value: 'eu-central-2', label: 'eu-central-2' },
  { value: 'eu-west-1', label: 'eu-west-1' },
  { value: 'eu-west-2', label: 'eu-west-2' },
  { value: 'eu-west-3', label: 'eu-west-3' },
  { value: 'eu-north-1', label: 'eu-north-1' },
  { value: 'sa-east-1', label: 'sa-east-1' },
  { value: 'us-gov-east-1', label: 'us-gov-east-1' },
  { value: 'us-gov-west-1', label: 'us-gov-west-1' }
])

const awsModelId = ref('')
const deepSeekModelId = ref('deepseek-chat')
// DeepSeek 多模型支持
const deepSeekModels = ref<ProviderModel[]>([])
const deepSeekNewModelInput = ref('')
const deepSeekPresetModels: ProviderModel[] = [
  { id: 'deepseek-chat', name: 'deepseek-chat' },
  { id: 'deepseek-reasoner', name: 'deepseek-reasoner' }
]
const awsAccessKey = ref('')
const awsSecretKey = ref('')
const awsSessionToken = ref('')
const awsRegion = ref('us-east-1')
const awsUseCrossRegionInference = ref(false)
const awsEndpointSelected = ref(false)
const awsBedrockEndpoint = ref('')
const liteLlmBaseUrl = ref('')
const liteLlmApiKey = ref('')
const liteLlmModelId = ref('')
// LiteLLM 多模型支持
const liteLlmModels = ref<ProviderModel[]>([])
const liteLlmNewModelInput = ref('')
const liteLlmPresetModels: ProviderModel[] = [
  { id: 'gpt-4o', name: 'gpt-4o' },
  { id: 'gpt-4o-mini', name: 'gpt-4o-mini' },
  { id: 'claude-3-5-sonnet', name: 'claude-3-5-sonnet' },
  { id: 'claude-3-5-haiku', name: 'claude-3-5-haiku' }
]
const deepSeekApiKey = ref('')
const openAiBaseUrl = ref('https://api.openai.com/v1')
const openAiApiKey = ref('')
const openAiModelId = ref('')
// OpenAI Compatible 多模型支持
const openAiModels = ref<ProviderModel[]>([])
const openAiNewModelInput = ref('')
const openAiPresetModels: ProviderModel[] = [
  { id: 'gpt-4o', name: 'gpt-4o' },
  { id: 'gpt-4o-mini', name: 'gpt-4o-mini' },
  { id: 'gpt-3.5-turbo', name: 'gpt-3.5-turbo' },
  { id: 'gpt-4-turbo', name: 'gpt-4-turbo' }
]
// New providers
const ollamaBaseUrl = ref('http://localhost:11434')
const ollamaApiKey = ref('')
const ollamaModelId = ref('')
// Ollama 多模型支持
const ollamaModels = ref<ProviderModel[]>([])
const ollamaNewModelInput = ref('')
const ollamaPresetModels: ProviderModel[] = [
  { id: 'llama3.2', name: 'llama3.2' },
  { id: 'qwen2.5', name: 'qwen2.5' },
  { id: 'deepseek-coder', name: 'deepseek-coder' },
  { id: 'codellama', name: 'codellama' }
]
const volcengineBaseUrl = ref('https://ark.cn-beijing.volces.com/api/v3')
const volcengineApiKey = ref('')
const volcengineModelId = ref('')
// 火山引擎多模型支持
const volcengineModels = ref<ProviderModel[]>([])
const volcengineNewModelInput = ref('')
const volcenginePresetModels: ProviderModel[] = [
  { id: 'doubao-pro-4k', name: 'doubao-pro-4k' },
  { id: 'doubao-lite-4k', name: 'doubao-lite-4k' },
  { id: 'doubao-pro-32k', name: 'doubao-pro-32k' },
  { id: 'doubao-pro-128k', name: 'doubao-pro-128k' }
]
const alibabaBaseUrl = ref('https://dashscope.aliyuncs.com/api/v1')
const alibabaApiKey = ref('')
const alibabaModelId = ref('')
// 阿里云百炼多模型支持
const alibabaModels = ref<ProviderModel[]>([])
const alibabaNewModelInput = ref('')
const alibabaPresetModels: ProviderModel[] = [
  { id: 'qwen-max', name: 'qwen-max' },
  { id: 'qwen-max-0125', name: 'qwen-max-0125' },
  { id: 'deepseek-r1', name: 'deepseek-r1' },
  { id: 'deepseek-v3', name: 'deepseek-v3' },
  { id: 'qwen-turbo', name: 'qwen-turbo' },
  { id: 'qwen-plus', name: 'qwen-plus' }
]
const glmBaseUrl = ref('https://open.bigmodel.cn/api/paas/v4')
const glmApiKey = ref('')
const glmModelId = ref('')
// 智谱清言多模型支持
const glmModels = ref<ProviderModel[]>([])
const glmNewModelInput = ref('')
const glmPresetModels: ProviderModel[] = [
  { id: 'glm-4-plus', name: 'glm-4-plus' },
  { id: 'glm-4-0520', name: 'glm-4-0520' },
  { id: 'glm-4-air', name: 'glm-4-air' },
  { id: 'glm-4-airx', name: 'glm-4-airx' }
]
const geminiBaseUrl = ref('https://generativelanguage.googleapis.com/v1beta')
const geminiApiKey = ref('')
const geminiModelId = ref('')
// Gemini 多模型支持
const geminiModels = ref<ProviderModel[]>([])
const geminiNewModelInput = ref('')
const geminiPresetModels: ProviderModel[] = [
  { id: 'gemini-1.5-pro', name: 'gemini-1.5-pro' },
  { id: 'gemini-1.5-flash', name: 'gemini-1.5-flash' },
  { id: 'gemini-1.0-pro', name: 'gemini-1.0-pro' },
  { id: 'gemini-2.0-flash-exp', name: 'gemini-2.0-flash-exp' }
]
const checkLoadingLiteLLM = ref(false)
const checkLoadingBedrock = ref(false)
const checkLoadingDeepSeek = ref(false)
const checkLoadingOpenAI = ref(false)
const checkLoadingOllama = ref(false)
const checkLoadingVolcengine = ref(false)
const checkLoadingAlibaba = ref(false)
const checkLoadingGlm = ref(false)
const checkLoadingGemini = ref(false)

// Load saved configuration
const loadSavedConfig = async () => {
  try {
    // Load API related configuration
    // apiProvider.value = ((await getGlobalState('apiProvider')) as string) || 'litellm'
    // AWS information
    // apiModelId.value = ((await getGlobalState('apiModelId')) as string) || ''
    awsRegion.value = ((await getGlobalState('awsRegion')) as string) || ''
    awsUseCrossRegionInference.value = ((await getGlobalState('awsUseCrossRegionInference')) as boolean) || false
    awsBedrockEndpoint.value = ((await getGlobalState('awsBedrockEndpoint')) as string) || ''
    awsAccessKey.value = (await getSecret('awsAccessKey')) || ''
    awsSecretKey.value = (await getSecret('awsSecretKey')) || ''
    awsSessionToken.value = (await getSecret('awsSessionToken')) || ''
    // OpenAI information
    liteLlmBaseUrl.value = ((await getGlobalState('liteLlmBaseUrl')) as string) || ''
    liteLlmApiKey.value = (await getSecret('liteLlmApiKey')) || ''
    deepSeekApiKey.value = (await getSecret('deepSeekApiKey')) || ''
    deepSeekModelId.value = ((await getGlobalState('deepSeekModelId')) as string) || 'deepseek-chat'
    openAiBaseUrl.value = ((await getGlobalState('openAiBaseUrl')) as string) || 'https://api.openai.com/v1'
    openAiApiKey.value = (await getSecret('openAiApiKey')) || ''
    awsEndpointSelected.value = ((await getGlobalState('awsEndpointSelected')) as boolean) || false
    // New providers information
    ollamaBaseUrl.value = ((await getGlobalState('ollamaBaseUrl')) as string) || 'http://localhost:11434'
    ollamaApiKey.value = (await getSecret('apiKey')) || ''
    volcengineBaseUrl.value = ((await getGlobalState('ollamaBaseUrl')) as string) || 'https://ark.cn-beijing.volces.com/api/v3'
    volcengineApiKey.value = (await getSecret('apiKey')) || ''
    alibabaBaseUrl.value = ((await getGlobalState('ollamaBaseUrl')) as string) || 'https://dashscope.aliyuncs.com/api/v1'
    alibabaApiKey.value = (await getSecret('apiKey')) || ''
    glmBaseUrl.value = ((await getGlobalState('ollamaBaseUrl')) as string) || 'https://open.bigmodel.cn/api/paas/v4'
    glmApiKey.value = (await getSecret('apiKey')) || ''
    geminiBaseUrl.value = ((await getGlobalState('geminiBaseUrl')) as string) || 'https://generativelanguage.googleapis.com/v1beta'
    geminiApiKey.value = (await getSecret('geminiApiKey')) || ''

    // 加载多模型数据
    const savedDeepSeekModels = ((await getGlobalState('taskHistory')) as any[]) || []
    // 如果没有保存的 DeepSeek 模型，添加默认模型
    if (savedDeepSeekModels.length === 0) {
      deepSeekModels.value = [
        { id: 'deepseek-chat', name: 'deepseek-chat', provider: 'deepseek' },
        { id: 'deepseek-coder', name: 'deepseek-coder', provider: 'deepseek' }
      ]
      // 保存默认模型到全局状态
      await saveDeepSeekModels()
    } else {
      deepSeekModels.value = savedDeepSeekModels
    }
    alibabaModels.value = ((await getGlobalState('taskHistory')) as any[]) || []
    liteLlmModels.value = ((await getGlobalState('taskHistory')) as any[]) || []
    openAiModels.value = ((await getGlobalState('taskHistory')) as any[]) || []
    ollamaModels.value = ((await getGlobalState('taskHistory')) as any[]) || []
    volcengineModels.value = ((await getGlobalState('taskHistory')) as any[]) || []
    glmModels.value = ((await getGlobalState('taskHistory')) as any[]) || []
    geminiModels.value = ((await getGlobalState('taskHistory')) as any[]) || []
  } catch (error) {
    console.error('Failed to load config:', error)
    notification.error({
      message: 'Error',
      description: 'Failed to load saved configuration'
    })
  }
}

/**
 * 保存不同提供商的配置
 * 确保apiProvider和模型ID被正确保存
 */
const saveBedrockConfig = async () => {
  try {
    console.log('[saveBedrockConfig] Saving Bedrock configuration')
    await updateGlobalState('apiProvider', 'bedrock')
    await updateGlobalState('apiModelId', awsModelId.value)
    await updateGlobalState('awsRegion', awsRegion.value)
    await updateGlobalState('awsUseCrossRegionInference', awsUseCrossRegionInference.value)
    await updateGlobalState('awsBedrockEndpoint', awsBedrockEndpoint.value)
    await updateGlobalState('awsEndpointSelected', awsEndpointSelected.value)
    await storeSecret('awsAccessKey', awsAccessKey.value)
    await storeSecret('awsSecretKey', awsSecretKey.value)
    await storeSecret('awsSessionToken', awsSessionToken.value)
    console.log('[saveBedrockConfig] Bedrock configuration saved successfully')
  } catch (error) {
    console.error('Failed to save Bedrock config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveBedrockConfigFailed')
    })
  }
}

const saveLiteLLMConfig = async () => {
  try {
    console.log('[saveLiteLlmConfig] Saving LiteLLM configuration')
    await updateGlobalState('apiProvider', 'litellm')
    await updateGlobalState('liteLlmModelId', liteLlmModelId.value)
    await updateGlobalState('liteLlmBaseUrl', liteLlmBaseUrl.value)
    await storeSecret('liteLlmApiKey', liteLlmApiKey.value)
    console.log('[saveLiteLlmConfig] LiteLLM configuration saved successfully')
  } catch (error) {
    console.error('Failed to save LiteLLM config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveLiteLlmConfigFailed')
    })
  }
}

const saveDeepSeekConfig = async () => {
  try {
    console.log('[saveDeepSeekConfig] Saving DeepSeek configuration')
    await updateGlobalState('apiProvider', 'deepseek')
    await updateGlobalState('deepSeekModelId', deepSeekModelId.value)
    await storeSecret('deepSeekApiKey', deepSeekApiKey.value)

    // 确保模型列表被保存
    await saveDeepSeekModels()

    // 同步多模型到全局状态
    syncMultiModelsToGlobal()

    console.log('[saveDeepSeekConfig] DeepSeek configuration saved successfully')
  } catch (error) {
    console.error('Failed to save DeepSeek config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveDeepSeekConfigFailed')
    })
  }
}

const saveOpenAiConfig = async () => {
  try {
    console.log('[saveOpenAiConfig] Saving OpenAI configuration')
    await updateGlobalState('apiProvider', 'openai')
    await updateGlobalState('openAiModelId', openAiModelId.value)
    await updateGlobalState('openAiBaseUrl', openAiBaseUrl.value)
    await storeSecret('openAiApiKey', openAiApiKey.value)
    console.log('[saveOpenAiConfig] OpenAI configuration saved successfully')
  } catch (error) {
    console.error('Failed to save OpenAI config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveOpenAiConfigFailed')
    })
  }
}

const saveOllamaConfig = async () => {
  try {
    console.log('[saveOllamaConfig] Saving Ollama configuration')
    await updateGlobalState('apiProvider', 'ollama')
    await updateGlobalState('ollamaModelId', ollamaModelId.value)
    await updateGlobalState('ollamaBaseUrl', ollamaBaseUrl.value)
    await storeSecret('ollamaApiKey', ollamaApiKey.value)
    console.log('[saveOllamaConfig] Ollama configuration saved successfully')
  } catch (error) {
    console.error('Failed to save Ollama config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveOllamaConfigFailed')
    })
  }
}

const saveVolcengineConfig = async () => {
  try {
    console.log('[saveVolcengineConfig] Saving Volcengine configuration')
    await updateGlobalState('apiProvider', 'volcengine')
    await updateGlobalState('volcengineModelId', volcengineModelId.value)
    await updateGlobalState('volcengineBaseUrl', volcengineBaseUrl.value)
    await storeSecret('volcengineApiKey', volcengineApiKey.value)
    console.log('[saveVolcengineConfig] Volcengine configuration saved successfully')
  } catch (error) {
    console.error('Failed to save Volcengine config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveVolcengineConfigFailed')
    })
  }
}

const saveAlibabaConfig = async () => {
  try {
    console.log('[saveAlibabaConfig] Saving Alibaba configuration')
    await updateGlobalState('apiProvider', 'alibaba')
    await updateGlobalState('alibabaModelId', alibabaModelId.value)
    await updateGlobalState('alibabaBaseUrl', alibabaBaseUrl.value)
    await storeSecret('alibabaApiKey', alibabaApiKey.value)
    console.log('[saveAlibabaConfig] Alibaba configuration saved successfully')
  } catch (error) {
    console.error('Failed to save Alibaba config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveAlibabaConfigFailed')
    })
  }
}

const saveGlmConfig = async () => {
  try {
    console.log('[saveGlmConfig] Saving GLM configuration')
    await updateGlobalState('apiProvider', 'glm')
    await updateGlobalState('glmModelId', glmModelId.value)
    await updateGlobalState('glmBaseUrl', glmBaseUrl.value)
    await storeSecret('glmApiKey', glmApiKey.value)
    console.log('[saveGlmConfig] GLM configuration saved successfully')
  } catch (error) {
    console.error('Failed to save GLM config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveGlmConfigFailed')
    })
  }
}

const saveGeminiConfig = async () => {
  try {
    console.log('[saveGeminiConfig] Saving Gemini configuration')
    await updateGlobalState('apiProvider', 'gemini')
    await updateGlobalState('geminiModelId', geminiModelId.value)
    await updateGlobalState('geminiBaseUrl', geminiBaseUrl.value)
    await storeSecret('geminiApiKey', geminiApiKey.value)
    console.log('[saveGeminiConfig] Gemini configuration saved successfully')
  } catch (error) {
    console.error('Failed to save Gemini config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveGeminiConfigFailed')
    })
  }
}

// Load saved configuration when component is mounted
onMounted(async () => {
  await loadSavedConfig()
  await loadModelOptions()
})

// Save configuration before component unmounts
onBeforeUnmount(async () => {})

const isEmptyValue = (value) => value === undefined || value === ''

const checkModelConfig = async (provider) => {
  switch (provider) {
    case 'bedrock':
      if (isEmptyValue(awsModelId.value) || isEmptyValue(awsAccessKey.value) || isEmptyValue(awsSecretKey.value) || isEmptyValue(awsRegion.value)) {
        return false
      }
      break
    case 'litellm':
      if (isEmptyValue(liteLlmBaseUrl.value) || isEmptyValue(liteLlmApiKey.value) || isEmptyValue(liteLlmModelId.value)) {
        return false
      }
      break
    case 'deepseek':
      if (isEmptyValue(deepSeekApiKey.value) || isEmptyValue(deepSeekModelId.value)) {
        return false
      }
      break
    case 'openai':
      if (isEmptyValue(openAiBaseUrl.value) || isEmptyValue(openAiApiKey.value) || isEmptyValue(openAiModelId.value)) {
        return false
      }
      break
    case 'ollama':
      if (isEmptyValue(ollamaBaseUrl.value) || isEmptyValue(ollamaModelId.value)) {
        return false
      }
      break
    case 'volcengine':
      if (isEmptyValue(volcengineBaseUrl.value) || isEmptyValue(volcengineApiKey.value) || isEmptyValue(volcengineModelId.value)) {
        return false
      }
      break
    case 'alibaba':
      if (isEmptyValue(alibabaBaseUrl.value) || isEmptyValue(alibabaApiKey.value) || isEmptyValue(alibabaModelId.value)) {
        return false
      }
      break
    case 'glm':
      if (isEmptyValue(glmBaseUrl.value) || isEmptyValue(glmApiKey.value) || isEmptyValue(glmModelId.value)) {
        return false
      }
      break
    case 'gemini':
      if (isEmptyValue(geminiBaseUrl.value) || isEmptyValue(geminiApiKey.value) || isEmptyValue(geminiModelId.value)) {
        return false
      }
      break
  }
  return true
}

const handleCheck = async (provider) => {
  const checkModelConfigResult = await checkModelConfig(provider)
  if (!checkModelConfigResult) {
    notification.error({
      message: t('user.checkModelConfigFailMessage'),
      description: t('user.checkModelConfigFailDescription'),
      duration: 3
    })
    return 'SEND_ERROR'
  }

  // Set corresponding loading state, check parameters
  let checkParam = await getAllExtensionState()
  console.log('[handleCheck] getAllExtensionState.apiConfiguration', checkParam?.apiConfiguration)
  let checkApiConfiguration = checkParam?.apiConfiguration
  let checkOptions = {}

  switch (provider) {
    case 'bedrock':
      checkLoadingBedrock.value = true
      checkOptions = {
        apiProvider: provider,
        apiModelId: awsModelId.value,
        awsAccessKey: awsAccessKey.value,
        awsSecretKey: awsSecretKey.value,
        awsRegion: awsRegion.value
      }
      break
    case 'litellm':
      checkLoadingLiteLLM.value = true
      checkOptions = {
        apiProvider: provider,
        liteLlmBaseUrl: liteLlmBaseUrl.value,
        liteLlmApiKey: liteLlmApiKey.value,
        liteLlmModelId: liteLlmModelId.value
      }
      break
    case 'deepseek':
      checkLoadingDeepSeek.value = true
      checkOptions = {
        apiProvider: provider,
        apiModelId: deepSeekModelId.value,
        deepSeekApiKey: deepSeekApiKey.value
      }
      break
    case 'openai':
      checkLoadingOpenAI.value = true
      checkOptions = {
        apiProvider: provider,
        openAiBaseUrl: openAiBaseUrl.value,
        openAiApiKey: openAiApiKey.value,
        openAiModelId: openAiModelId.value
      }
      break
    case 'ollama':
      checkLoadingOllama.value = true
      checkOptions = {
        apiProvider: provider,
        ollamaBaseUrl: ollamaBaseUrl.value,
        ollamaApiKey: ollamaApiKey.value,
        ollamaModelId: ollamaModelId.value
      }
      break
    case 'volcengine':
      checkLoadingVolcengine.value = true
      checkOptions = {
        apiProvider: provider,
        volcengineBaseUrl: volcengineBaseUrl.value,
        volcengineApiKey: volcengineApiKey.value,
        volcengineModelId: volcengineModelId.value
      }
      break
    case 'alibaba':
      checkLoadingAlibaba.value = true
      checkOptions = {
        apiProvider: provider,
        alibabaBaseUrl: alibabaBaseUrl.value,
        alibabaApiKey: alibabaApiKey.value,
        alibabaModelId: alibabaModelId.value
      }
      break
    case 'glm':
      checkLoadingGlm.value = true
      checkOptions = {
        apiProvider: provider,
        glmBaseUrl: glmBaseUrl.value,
        glmApiKey: glmApiKey.value,
        glmModelId: glmModelId.value
      }
      break
    case 'gemini':
      checkLoadingGemini.value = true
      checkOptions = {
        apiProvider: provider,
        geminiBaseUrl: geminiBaseUrl.value,
        geminiApiKey: geminiApiKey.value,
        geminiModelId: geminiModelId.value
      }
      break
  }

  // Override checkApiConfiguration content
  checkApiConfiguration = { ...checkApiConfiguration, ...checkOptions }
  try {
    console.log('[validateApiKey] checkApiConfiguration', checkApiConfiguration)
    // Ensure correct parameter format is passed
    const result = await (window.api as any).validateApiKey(checkApiConfiguration)
    if (result.isValid) {
      notification.success({
        message: t('user.checkSuccessMessage'),
        description: t('user.checkSuccessDescription'),
        duration: 3
      })
    } else {
      notification.error({
        message: t('user.checkFailMessage'),
        description: result.error || t('user.checkFailDescriptionDefault'),
        duration: 3
      })
    }
  } catch (error) {
    notification.error({
      message: t('user.checkFailMessage'),
      description: String(error),
      duration: 3
    })
  } finally {
    // Reset loading state
    checkLoadingBedrock.value = false
    checkLoadingLiteLLM.value = false
    checkLoadingDeepSeek.value = false
    checkLoadingOpenAI.value = false
    checkLoadingOllama.value = false
    checkLoadingVolcengine.value = false
    checkLoadingAlibaba.value = false
    checkLoadingGlm.value = false
    checkLoadingGemini.value = false
  }
}

// Add model management methods
const handleModelChange = (model) => {
  // Update model selection state
  const index = modelOptions.value.findIndex((m) => m.id === model.id)
  if (index !== -1) {
    modelOptions.value[index].checked = model.checked
    saveModelOptions()
  }
}

const removeModel = (model) => {
  if (model.type === 'custom') {
    const index = modelOptions.value.findIndex((m) => m.id === model.id)
    if (index !== -1) {
      modelOptions.value.splice(index, 1)
      saveModelOptions()
    }
  }
}

const saveModelOptions = async () => {
  try {
    // Create a simple serializable object array
    const serializableModelOptions = modelOptions.value.map((model) => ({
      id: model.id,
      name: model.name,
      checked: Boolean(model.checked),
      type: model.type || 'standard',
      apiProvider: model.apiProvider || 'default'
    }))

    await updateGlobalState('modelOptions', serializableModelOptions)
    eventBus.emit('SettingModelOptionsChanged')
  } catch (error) {
    console.error('Failed to save model options:', error)
    notification.error({
      message: 'Error',
      description: 'Failed to save model options'
    })
  }
}

// Sort model list: built-in models first, user-defined models last
const sortModelOptions = () => {
  modelOptions.value.sort((a, b) => {
    const aIsThinking = a.name.endsWith('-Thinking')
    const bIsThinking = b.name.endsWith('-Thinking')

    if (aIsThinking && !bIsThinking) return -1
    if (!aIsThinking && bIsThinking) return 1

    // First sort by model type: standard (built-in) first, custom (user-defined) last
    if (a.type === 'standard' && b.type === 'custom') return -1
    if (a.type === 'custom' && b.type === 'standard') return 1

    // If types are the same, sort by name alphabetically
    return a.name.localeCompare(b.name)
  })
}

const loadModelOptions = async () => {
  try {
    let defaultModels: DefaultModel[] = []
    await getUser({}).then((res) => {
      defaultModels = res?.data?.models || []
      updateGlobalState('defaultBaseUrl', res?.data?.llmGatewayAddr)
      storeSecret('defaultApiKey', res?.data?.key)
    })
    const savedModelOptions = (await getGlobalState('modelOptions')) || []
    if (savedModelOptions && Array.isArray(savedModelOptions)) {
      // 1. Filter out models that type=='standard' and do not exist in defaultModels
      const filteredOptions = savedModelOptions.filter((option) => {
        if (option.type !== 'standard') return true
        return defaultModels.some((defaultModel) => defaultModel === option.name)
      })

      // 2. Add models that do not exist in savedModelOptions in defaultModels
      defaultModels.forEach((defaultModel) => {
        const exists = filteredOptions.some((option) => option.name === defaultModel)
        if (!exists) {
          filteredOptions.push({
            id: defaultModel || '',
            name: defaultModel || defaultModel || '',
            checked: true,
            type: 'standard',
            apiProvider: 'default'
          })
        }
      })

      // Ensure loaded data contains all necessary properties
      modelOptions.value = filteredOptions.map((option) => ({
        id: option.id || '',
        name: option.name || '',
        checked: Boolean(option.checked),
        type: option.type || 'standard',
        apiProvider: option.apiProvider || 'default'
      }))

      // Sort model list: built-in models first, user-defined models last
      sortModelOptions()

      // 同步多模型数据到全局模型选项
      syncMultiModelsToGlobal()
    }
    await saveModelOptions()
  } catch (error) {
    console.error('Failed to load model options:', error)
  }
}

// 多模型管理方法
// LiteLLM 多模型管理
const addLiteLlmPresetModel = async (preset) => {
  const newModel = {
    id: `litellm-${preset.id}-${Date.now()}`,
    name: preset.name,
    provider: 'litellm'
  }
  liteLlmModels.value.push(newModel)
  await saveLiteLlmModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${preset.name}`,
    duration: 2
  })
}

const addLiteLlmNewModel = async () => {
  if (!liteLlmNewModelInput.value.trim()) {
    notification.error({
      message: '错误',
      description: '请输入模型名称',
      duration: 2
    })
    return
  }

  if (liteLlmModels.value.some((m) => m.name === liteLlmNewModelInput.value.trim())) {
    notification.error({
      message: '错误',
      description: '模型已存在',
      duration: 2
    })
    return
  }

  const newModel = {
    id: `litellm-${liteLlmNewModelInput.value.trim()}-${Date.now()}`,
    name: liteLlmNewModelInput.value.trim(),
    provider: 'litellm'
  }
  liteLlmModels.value.push(newModel)
  liteLlmNewModelInput.value = ''
  await saveLiteLlmModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${newModel.name}`,
    duration: 2
  })
}

const removeLiteLlmModel = async (modelId) => {
  const index = liteLlmModels.value.findIndex((m) => m.id === modelId)
  if (index !== -1) {
    liteLlmModels.value.splice(index, 1)
    await saveLiteLlmModels()
    syncMultiModelsToGlobal()
  }
}

const saveLiteLlmModels = async () => {
  try {
    await updateGlobalState('liteLlmModels', liteLlmModels.value)
  } catch (error) {
    console.error('Failed to save LiteLLM models:', error)
  }
}

// OpenAI Compatible 多模型管理
const addOpenAiPresetModel = async (preset) => {
  const newModel = {
    id: `openai-${preset.id}-${Date.now()}`,
    name: preset.name,
    provider: 'openai'
  }
  openAiModels.value.push(newModel)
  await saveOpenAiModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${preset.name}`,
    duration: 2
  })
}

const addOpenAiNewModel = async () => {
  if (!openAiNewModelInput.value.trim()) {
    notification.error({
      message: '错误',
      description: '请输入模型名称',
      duration: 2
    })
    return
  }

  if (openAiModels.value.some((m) => m.name === openAiNewModelInput.value.trim())) {
    notification.error({
      message: '错误',
      description: '模型已存在',
      duration: 2
    })
    return
  }

  const newModel = {
    id: `openai-${openAiNewModelInput.value.trim()}-${Date.now()}`,
    name: openAiNewModelInput.value.trim(),
    provider: 'openai'
  }
  openAiModels.value.push(newModel)
  openAiNewModelInput.value = ''
  await saveOpenAiModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${newModel.name}`,
    duration: 2
  })
}

const removeOpenAiModel = async (modelId) => {
  const index = openAiModels.value.findIndex((m) => m.id === modelId)
  if (index !== -1) {
    openAiModels.value.splice(index, 1)
    await saveOpenAiModels()
    syncMultiModelsToGlobal()
  }
}

const saveOpenAiModels = async () => {
  try {
    await updateGlobalState('openAiModels', openAiModels.value)
  } catch (error) {
    console.error('Failed to save OpenAI models:', error)
  }
}

// Ollama 多模型管理
const addOllamaPresetModel = async (preset) => {
  const newModel = {
    id: `ollama-${preset.id}-${Date.now()}`,
    name: preset.name,
    provider: 'ollama'
  }
  ollamaModels.value.push(newModel)
  await saveOllamaModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${preset.name}`,
    duration: 2
  })
}

const addOllamaNewModel = async () => {
  if (!ollamaNewModelInput.value.trim()) {
    notification.error({
      message: '错误',
      description: '请输入模型名称',
      duration: 2
    })
    return
  }

  if (ollamaModels.value.some((m) => m.name === ollamaNewModelInput.value.trim())) {
    notification.error({
      message: '错误',
      description: '模型已存在',
      duration: 2
    })
    return
  }

  const newModel = {
    id: `ollama-${ollamaNewModelInput.value.trim()}-${Date.now()}`,
    name: ollamaNewModelInput.value.trim(),
    provider: 'ollama'
  }
  ollamaModels.value.push(newModel)
  ollamaNewModelInput.value = ''
  await saveOllamaModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${newModel.name}`,
    duration: 2
  })
}

const removeOllamaModel = async (modelId) => {
  const index = ollamaModels.value.findIndex((m) => m.id === modelId)
  if (index !== -1) {
    ollamaModels.value.splice(index, 1)
    await saveOllamaModels()
    syncMultiModelsToGlobal()
  }
}

const saveOllamaModels = async () => {
  try {
    await updateGlobalState('ollamaModels', ollamaModels.value)
  } catch (error) {
    console.error('Failed to save Ollama models:', error)
  }
}

// DeepSeek 多模型管理
const addDeepSeekPresetModel = async (preset) => {
  const newModel = {
    id: `deepseek-${preset.id}-${Date.now()}`,
    name: preset.name,
    provider: 'deepseek'
  }
  deepSeekModels.value.push(newModel)
  await saveDeepSeekModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${preset.name}`,
    duration: 2
  })
}

const addDeepSeekNewModel = async () => {
  if (!deepSeekNewModelInput.value.trim()) {
    notification.error({
      message: '错误',
      description: '请输入模型名称',
      duration: 2
    })
    return
  }

  if (deepSeekModels.value.some((m) => m.name === deepSeekNewModelInput.value.trim())) {
    notification.error({
      message: '错误',
      description: '模型已存在',
      duration: 2
    })
    return
  }

  const newModel = {
    id: `deepseek-${deepSeekNewModelInput.value.trim()}-${Date.now()}`,
    name: deepSeekNewModelInput.value.trim(),
    provider: 'deepseek'
  }
  deepSeekModels.value.push(newModel)
  deepSeekNewModelInput.value = ''
  await saveDeepSeekModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${newModel.name}`,
    duration: 2
  })
}

const removeDeepSeekModel = async (modelId) => {
  const index = deepSeekModels.value.findIndex((m) => m.id === modelId)
  if (index !== -1) {
    deepSeekModels.value.splice(index, 1)
    await saveDeepSeekModels()
    syncMultiModelsToGlobal()
  }
}

const saveDeepSeekModels = async () => {
  try {
    // 确保数据可以被正确序列化，避免DataCloneError
    const serializedModels = deepSeekModels.value.map((model) => ({
      id: model.id,
      name: model.name,
      provider: model.provider || 'deepseek'
    }))
    await updateGlobalState('deepSeekModels', serializedModels)
  } catch (error) {
    console.error('Failed to save DeepSeek models:', error)
  }
}

// 火山引擎多模型管理
const addVolcenginePresetModel = async (preset) => {
  const newModel = {
    id: `volcengine-${preset.id}-${Date.now()}`,
    name: preset.name,
    provider: 'volcengine'
  }
  volcengineModels.value.push(newModel)
  await saveVolcengineModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${preset.name}`,
    duration: 2
  })
}

const addVolcengineNewModel = async () => {
  if (!volcengineNewModelInput.value.trim()) {
    notification.error({
      message: '错误',
      description: '请输入模型名称',
      duration: 2
    })
    return
  }

  if (volcengineModels.value.some((m) => m.name === volcengineNewModelInput.value.trim())) {
    notification.error({
      message: '错误',
      description: '模型已存在',
      duration: 2
    })
    return
  }

  const newModel = {
    id: `volcengine-${volcengineNewModelInput.value.trim()}-${Date.now()}`,
    name: volcengineNewModelInput.value.trim(),
    provider: 'volcengine'
  }
  volcengineModels.value.push(newModel)
  volcengineNewModelInput.value = ''
  await saveVolcengineModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${newModel.name}`,
    duration: 2
  })
}

const removeVolcengineModel = async (modelId) => {
  const index = volcengineModels.value.findIndex((m) => m.id === modelId)
  if (index !== -1) {
    volcengineModels.value.splice(index, 1)
    await saveVolcengineModels()
    syncMultiModelsToGlobal()
  }
}

const saveVolcengineModels = async () => {
  try {
    await updateGlobalState('volcengineModels', volcengineModels.value)
  } catch (error) {
    console.error('Failed to save Volcengine models:', error)
  }
}

// 智谱清言多模型管理
const addGlmPresetModel = async (preset) => {
  const newModel = {
    id: `glm-${preset.id}-${Date.now()}`,
    name: preset.name,
    provider: 'glm'
  }
  glmModels.value.push(newModel)
  await saveGlmModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${preset.name}`,
    duration: 2
  })
}

const addGlmNewModel = async () => {
  if (!glmNewModelInput.value.trim()) {
    notification.error({
      message: '错误',
      description: '请输入模型名称',
      duration: 2
    })
    return
  }

  if (glmModels.value.some((m) => m.name === glmNewModelInput.value.trim())) {
    notification.error({
      message: '错误',
      description: '模型已存在',
      duration: 2
    })
    return
  }

  const newModel = {
    id: `glm-${glmNewModelInput.value.trim()}-${Date.now()}`,
    name: glmNewModelInput.value.trim(),
    provider: 'glm'
  }
  glmModels.value.push(newModel)
  glmNewModelInput.value = ''
  await saveGlmModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${newModel.name}`,
    duration: 2
  })
}

const removeGlmModel = async (modelId) => {
  const index = glmModels.value.findIndex((m) => m.id === modelId)
  if (index !== -1) {
    glmModels.value.splice(index, 1)
    await saveGlmModels()
    syncMultiModelsToGlobal()
  }
}

const saveGlmModels = async () => {
  try {
    await updateGlobalState('glmModels', glmModels.value)
  } catch (error) {
    console.error('Failed to save GLM models:', error)
  }
}

// Gemini 多模型管理
const addGeminiPresetModel = async (preset) => {
  const newModel = {
    id: `gemini-${preset.id}-${Date.now()}`,
    name: preset.name,
    provider: 'gemini'
  }
  geminiModels.value.push(newModel)
  await saveGeminiModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${preset.name}`,
    duration: 2
  })
}

const addGeminiNewModel = async () => {
  if (!geminiNewModelInput.value.trim()) {
    notification.error({
      message: '错误',
      description: '请输入模型名称',
      duration: 2
    })
    return
  }

  if (geminiModels.value.some((m) => m.name === geminiNewModelInput.value.trim())) {
    notification.error({
      message: '错误',
      description: '模型已存在',
      duration: 2
    })
    return
  }

  const newModel = {
    id: `gemini-${geminiNewModelInput.value.trim()}-${Date.now()}`,
    name: geminiNewModelInput.value.trim(),
    provider: 'gemini'
  }
  geminiModels.value.push(newModel)
  geminiNewModelInput.value = ''
  await saveGeminiModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${newModel.name}`,
    duration: 2
  })
}

const removeGeminiModel = async (modelId) => {
  const index = geminiModels.value.findIndex((m) => m.id === modelId)
  if (index !== -1) {
    geminiModels.value.splice(index, 1)
    await saveGeminiModels()
    syncMultiModelsToGlobal()
  }
}

const saveGeminiModels = async () => {
  try {
    await updateGlobalState('geminiModels', geminiModels.value)
  } catch (error) {
    console.error('Failed to save Gemini models:', error)
  }
}

// 阿里云百炼多模型管理
const addAlibabaPresetModel = async (preset) => {
  const newModel = {
    id: `alibaba-${preset.id}-${Date.now()}`,
    name: preset.name,
    provider: 'alibaba'
  }
  alibabaModels.value.push(newModel)
  await saveAlibabaModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${preset.name}`,
    duration: 2
  })
}

const addAlibabaNewModel = async () => {
  if (!alibabaNewModelInput.value.trim()) {
    notification.error({
      message: '错误',
      description: '请输入模型名称',
      duration: 2
    })
    return
  }

  if (alibabaModels.value.some((m) => m.name === alibabaNewModelInput.value.trim())) {
    notification.error({
      message: '错误',
      description: '模型已存在',
      duration: 2
    })
    return
  }

  const newModel = {
    id: `alibaba-${alibabaNewModelInput.value.trim()}-${Date.now()}`,
    name: alibabaNewModelInput.value.trim(),
    provider: 'alibaba'
  }
  alibabaModels.value.push(newModel)
  alibabaNewModelInput.value = ''
  await saveAlibabaModels()
  syncMultiModelsToGlobal()
  notification.success({
    message: '成功',
    description: `已添加模型 ${newModel.name}`,
    duration: 2
  })
}

const removeAlibabaModel = async (modelId) => {
  const index = alibabaModels.value.findIndex((m) => m.id === modelId)
  if (index !== -1) {
    alibabaModels.value.splice(index, 1)
    await saveAlibabaModels()
    syncMultiModelsToGlobal()
  }
}

const saveAlibabaModels = async () => {
  try {
    await updateGlobalState('alibabaModels', alibabaModels.value)
  } catch (error) {
    console.error('Failed to save Alibaba models:', error)
  }
}

// 同步多模型数据到全局模型选项
const syncMultiModelsToGlobal = () => {
  // 移除旧的多模型数据
  modelOptions.value = modelOptions.value.filter(
    (model) =>
      !['litellm', 'openai', 'ollama', 'volcengine', 'glm', 'gemini', 'deepseek', 'alibaba'].includes(model.apiProvider) || model.type === 'standard'
  )

  // 添加新的多模型数据
  const allMultiModels = [
    ...liteLlmModels.value.map((m) => ({ ...m, apiProvider: 'litellm', type: 'custom', checked: true })),
    ...openAiModels.value.map((m) => ({ ...m, apiProvider: 'openai', type: 'custom', checked: true })),
    ...ollamaModels.value.map((m) => ({ ...m, apiProvider: 'ollama', type: 'custom', checked: true })),
    ...volcengineModels.value.map((m) => ({ ...m, apiProvider: 'volcengine', type: 'custom', checked: true })),
    ...glmModels.value.map((m) => ({ ...m, apiProvider: 'glm', type: 'custom', checked: true })),
    ...geminiModels.value.map((m) => ({ ...m, apiProvider: 'gemini', type: 'custom', checked: true })),
    ...deepSeekModels.value.map((m) => ({ ...m, apiProvider: 'deepseek', type: 'custom', checked: true })),
    ...alibabaModels.value.map((m) => ({ ...m, apiProvider: 'alibaba', type: 'custom', checked: true }))
  ]

  modelOptions.value.push(...allMultiModels)
  sortModelOptions()
  saveModelOptions()
}
</script>

<style lang="less" scoped>
.settings-section {
  background-color: transparent;

  :deep(.ant-card-body) {
    padding: 16px;
  }
}

.section-header {
  margin-top: 8px;
  margin-left: 16px;

  h3 {
    font-size: 20px;
    font-weight: 500;
    margin: 0;
  }
}

.setting-item {
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.setting-description {
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-color-tertiary);
  padding-left: 22px;
}

.setting-description-no-padding {
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-color-tertiary);
}

// Unified component styles
:deep(.ant-checkbox-wrapper),
:deep(.ant-form-item-label label),
:deep(.ant-select),
:deep(.ant-input),
:deep(.ant-input-password) {
  color: var(--text-color-secondary);
}

:deep(.ant-checkbox),
:deep(.ant-select-selector),
:deep(.ant-input),
:deep(.ant-input-password) {
  background-color: var(--bg-color-octonary) !important;
  border: 1px solid var(--bg-color-octonary) !important;

  &:hover,
  &:focus {
    border-color: #1890ff;
  }

  &::placeholder {
    color: var(--text-color-quaternary) !important;
  }
}

// Password input specific styles
:deep(.ant-input-password) {
  .ant-input {
    background-color: var(--bg-color-octonary) !important;
    color: var(--text-color-secondary);
  }
  .anticon {
    color: var(--text-color-tertiary);
  }

  &:hover .anticon {
    color: var(--text-color-secondary-light);
  }
}

// Add specific styles for select box
:deep(.ant-select) {
  .ant-select-selector {
    background-color: var(--bg-color-octonary) !important;
    border: none;

    .ant-select-selection-placeholder {
      color: var(--text-color-quaternary) !important;
    }
  }

  &.ant-select-focused {
    .ant-select-selector {
      background-color: var(--bg-color-octonary) !important;
      border-color: #1890ff !important;
    }
  }
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

// Dropdown menu styles
:deep(.ant-select-dropdown) {
  background-color: var(--bg-color-octonary);
  border: 1px solid rgba(255, 255, 255, 0.15);

  .ant-select-item {
    color: var(--text-color-secondary);
    background-color: var(--bg-color-octonary);

    &-option-active,
    &-option-selected {
      color: var(--text-color-secondary) !important; // Add selected item text color
      background-color: rgba(24, 144, 255, 0.2);
    }

    &-option:hover {
      color: var(--text-color-secondary);
      background-color: rgba(255, 255, 255, 0.08);
    }
  }
}

// Color of selected items in select box
:deep(.ant-select-selection-item) {
  color: var(--text-color-secondary) !important;
}

.label-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}

.budget-label {
  font-weight: 500;
  display: block;
  margin-right: auto;
  color: var(--text-color-tertiary);
}

.slider-container {
  padding: 8px 0;
  color: var(--text-color-tertiary);

  :deep(.ant-slider) {
    margin: 0;
    // Track styles
    .ant-slider-rail {
      background-color: var(--bg-color-octonary);
    }

    // Styles for selected portion of track
    .ant-slider-track {
      background-color: #1890ff;
    }

    // Slider handle styles
    .ant-slider-handle {
      width: 16px;
      height: 16px;
      border: 2px solid var(--vscode-progressBar-background);
      background-color: var(--vscode-foreground);
      box-shadow: var(--box-shadow);

      &:focus {
        box-shadow: 0 0 0 5px var(--vscode-focusBorder);
      }

      &:hover {
        border-color: var(--vscode-progressBar-background);
      }

      &:active {
        box-shadow: 0 0 0 5px var(--vscode-focusBorder);
      }
    }
  }
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

// Reduce spacing between form items
:deep(.ant-form-item) {
  margin-bottom: 8px; // Reduce bottom margin
}

// Reduce spacing between label and input box
:deep(.ant-form-item-label) {
  padding-bottom: 0; // Remove label bottom padding
  > label {
    height: 24px; // Reduce label height
    line-height: 24px; // Adjust line height to match height
  }
}

:deep(.ant-form-item .ant-form-item-label > label) {
  color: var(--text-color-secondary);
}

.chat-response {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;

  .message {
    width: 100%;
    padding: 8px 12px;
    border-radius: 12px;
    font-size: 12px;
    line-height: 1.5;

    &.user {
      align-self: flex-end;
      background-color: var(--text-color-senary); // Light gray background
      color: var(--text-color); // White text
      border: none; // Remove border
      width: 90%; // Parent component's 90% width
      margin-left: auto; // Right align
    }

    &.assistant {
      align-self: flex-start;
      background-color: var(--bg-color-quinary);
      color: var(--text-color);
      border: 1px solid var(--bg-color-quinary);
      width: 100%;
    }
  }
}

.check-btn {
  margin-left: 4px;
  width: 90px;
  background-color: var(--bg-color-octonary) !important;
  color: var(--text-color) !important;
  border: none !important;
  box-shadow: none !important;
  transition: background 0.2s;
}

.check-btn:hover,
.check-btn:focus {
  background-color: var(--bg-color-novenary) !important;
  color: var(--text-color) !important;
}

/* Model list styles */
.model-header {
  margin-top: 0;
  margin-left: 0;
}

.model-label {
  display: inline-flex;
  align-items: center;
}

.thinking-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  filter: var(--icon-filter, invert(0.25));
  transition: filter 0.2s ease;
}

.model-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  height: 28px; /* 固定高度 */
  box-sizing: border-box;
}

.model-item:hover {
  background-color: var(--text-color-septenary);
}

.remove-button {
  padding: 0 8px;
  color: var(--text-color-tertiary);
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-button:hover {
  color: var(--text-color-secondary);
}

.remove-icon {
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
}

:deep(.ant-checkbox-wrapper) {
  color: var(--text-color-secondary);
  height: 24px;
  line-height: 24px;
  display: flex;
  align-items: center;
}

:deep(.ant-checkbox) {
  border: 0 !important;
  background-color: var(--bg-color) !important;
  top: 0;
}

:deep(.ant-checkbox-inner) {
  background-color: var(--bg-color-octonary) !important;
  border-color: var(--text-color-quinary) !important;
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.save-btn {
  width: 90px;
  background-color: var(--bg-color-octonary) !important;
  color: var(--text-color) !important;
  border: none !important;
  box-shadow: none !important;
  transition: background 0.2s;
}

.save-btn:hover,
.save-btn:focus {
  background-color: var(--bg-color-novenary) !important;
  color: var(--text-color) !important;
}

.model-input-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.model-input {
  flex: 1;
  margin-right: 8px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.api-provider-options {
  margin-bottom: 16px;
}

.api-provider-header {
  margin-bottom: 16px;
  border-bottom: 1px solid var(--bg-color-quaternary);
  padding-bottom: 8px;

  h4 {
    margin: 0;
    font-size: 16px;
    color: #1890ff;
  }
}

/* 多模型管理样式 */

.preset-models {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preset-model-btn {
  background-color: var(--bg-color-octonary) !important;
  color: var(--text-color-secondary) !important;
  border: 1px solid var(--bg-color-octonary) !important;
  transition: all 0.2s;
}

.preset-model-btn:hover:not(:disabled) {
  background-color: #1890ff !important;
  color: white !important;
  border-color: #1890ff !important;
}

.preset-model-btn:disabled {
  background-color: var(--bg-color-quaternary) !important;
  color: var(--text-color-quaternary) !important;
  border-color: var(--bg-color-quaternary) !important;
  cursor: not-allowed;
}

.model-list .model-item {
  background-color: var(--bg-color-octonary);
  border: 1px solid var(--bg-color-octonary);
  border-radius: 4px;
  margin-bottom: 4px;
  color: var(--text-color-secondary);
  font-size: 14px;
}

.model-list .model-item:hover {
  background-color: var(--bg-color-novenary);
  border-color: var(--bg-color-novenary);
}

/* API检测和保存按钮样式 */
.api-action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.check-btn {
  background-color: #52c41a !important;
  color: white !important;
  border: none !important;
  box-shadow: none !important;
  transition: background 0.2s;
}

.check-btn:hover,
.check-btn:focus {
  background-color: #389e0d !important;
  color: white !important;
}

.check-btn:disabled {
  background-color: var(--bg-color-quaternary) !important;
  color: var(--text-color-quaternary) !important;
}
</style>
