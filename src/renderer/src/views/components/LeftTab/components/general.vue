<template>
  <div class="userInfo">
    <h1 style="color: red; font-size: 24px; background: yellow">通用设置组件已加载</h1>
    <a-card
      :bordered="false"
      class="userInfo-container"
    >
      <a-form
        :colon="false"
        label-align="left"
        wrapper-align="right"
        :label-col="{ span: 7, offset: 0 }"
        :wrapper-col="{ span: 17, class: 'right-aligned-wrapper' }"
        class="custom-form"
      >
        <a-form-item>
          <template #label>
            <span class="label-text">{{ $t('user.baseSetting') }}</span>
          </template>
        </a-form-item>
        <a-form-item
          :label="$t('user.theme')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.theme"
            class="custom-radio-group"
            @change="changeTheme"
          >
            <a-radio value="dark">{{ $t('user.themeDark') }}</a-radio>
            <a-radio value="light">{{ $t('user.themeLight') }}</a-radio>
            <a-radio value="auto">{{ $t('user.themeAuto') }}</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          :label="$t('user.language')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.language"
            class="custom-radio-group"
            @change="changeLanguage"
          >
            <a-radio value="zh-CN">简体中文</a-radio>
            <a-radio value="en-US">English</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          :label="$t('user.watermark')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.watermark"
            class="custom-radio-group"
          >
            <a-radio value="open">{{ $t('user.watermarkOpen') }}</a-radio>
            <a-radio value="close">{{ $t('user.watermarkClose') }}</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import { notification } from 'ant-design-vue'
import { userConfigStore } from '@/services/userConfigStoreService'
import eventBus from '@/utils/eventBus'
import { getActualTheme, addSystemThemeListener } from '@/utils/themeUtils'
import { useI18n } from 'vue-i18n'

const api = window.api
const { locale, t } = useI18n()

const userConfig = ref({
  language: 'zh-CN',
  watermark: 'open',
  theme: 'auto'
})

const loadSavedConfig = async (retryCount = 0) => {
  const maxRetries = 3
  const retryDelay = 1000 // 1秒

  try {
    // 等待一小段时间确保数据库服务完全初始化
    if (retryCount === 0) {
      await new Promise((resolve) => setTimeout(resolve, 100))
    }

    const savedConfig = await userConfigStore.getConfig()
    if (savedConfig) {
      userConfig.value = {
        ...userConfig.value,
        ...savedConfig
      }
      const actualTheme = getActualTheme(userConfig.value.theme)
      document.documentElement.className = `theme-${actualTheme}`

      // 安全地调用eventBus.emit，添加空值检查
      if (eventBus && typeof eventBus.emit === 'function') {
        eventBus.emit('updateTheme', actualTheme)
      }

      // 安全地调用api.updateTheme，添加空值检查
      if (api && typeof api.updateTheme === 'function') {
        api.updateTheme(userConfig.value.theme)
      }

      console.log('Config loaded successfully')
    }
  } catch (error) {
    console.error(`Failed to load config (attempt ${retryCount + 1}):`, error)

    // 检查是否是数据库初始化相关的错误
    const isDatabaseError =
      error.message && (error.message.includes('database') || error.message.includes('IndexedDB') || error.message.includes('transaction'))

    // 如果是数据库错误且还有重试次数，等待后重试
    if (isDatabaseError && retryCount < maxRetries) {
      console.log(`Database not ready, retrying config load in ${retryDelay}ms...`)
      setTimeout(() => {
        loadSavedConfig(retryCount + 1)
      }, retryDelay)
      return
    }

    // 如果不是数据库错误或重试次数用完，使用默认配置但不显示错误通知
    if (isDatabaseError && retryCount >= maxRetries) {
      console.warn('Database initialization failed after retries, using default configuration silently')
    } else {
      // 只有在非数据库相关错误时才显示错误通知
      console.error('Config load failed with non-database error')
      notification.error({
        message: t('user.loadConfigFailed'),
        description: t('user.loadConfigFailedDescription')
      })
    }

    // 使用默认配置
    const actualTheme = getActualTheme('auto')
    document.documentElement.className = `theme-${actualTheme}`
    userConfig.value.theme = 'auto'
  }
}

const saveConfig = async () => {
  try {
    const configToStore = {
      language: userConfig.value.language,
      watermark: userConfig.value.watermark,
      theme: userConfig.value.theme
    }
    await userConfigStore.saveConfig(configToStore)

    // 安全地调用eventBus.emit，添加空值检查
    if (eventBus && typeof eventBus.emit === 'function') {
      eventBus.emit('updateWatermark', configToStore.watermark)
      eventBus.emit('updateTheme', configToStore.theme)
    }
  } catch (error) {
    console.error('Failed to save config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveConfigFailedDescription')
    })
  }
}

watch(
  () => userConfig.value,
  async () => {
    await saveConfig()
  },
  { deep: true }
)

let systemThemeListener = null

onMounted(async () => {
  console.log('General component mounted')
  console.log('userConfig initial value:', userConfig.value)
  try {
    await loadSavedConfig()
    console.log('Config loaded successfully, userConfig:', userConfig.value)

    // Add system theme change listener
    setupSystemThemeListener()
  } catch (error) {
    console.error('Error in General component onMounted:', error)
  }
})

onBeforeUnmount(() => {
  eventBus.off('updateTheme')

  // Remove system theme listener
  if (systemThemeListener) {
    systemThemeListener()
    systemThemeListener = null
  }
})

const changeLanguage = async () => {
  locale.value = userConfig.value.language
  localStorage.setItem('lang', userConfig.value.language)
  // 移除configStore调用，因为我们已经通过userConfigStore保存配置

  // 安全地通知其他组件语言已更改，需要刷新数据
  if (eventBus && typeof eventBus.emit === 'function') {
    eventBus.emit('languageChanged', userConfig.value.language)
  }

  await saveConfig()
}

// Setup system theme change listener
const setupSystemThemeListener = () => {
  systemThemeListener = addSystemThemeListener(async (newSystemTheme) => {
    // Only update theme if user has selected 'auto' mode
    if (userConfig.value.theme === 'auto') {
      const actualTheme = getActualTheme(userConfig.value.theme)
      const currentTheme = document.documentElement.className.replace('theme-', '')

      if (currentTheme !== actualTheme) {
        // System theme changed, update application theme
        document.documentElement.className = `theme-${actualTheme}`

        // 安全地调用eventBus.emit，添加空值检查
        if (eventBus && typeof eventBus.emit === 'function') {
          eventBus.emit('updateTheme', actualTheme)
        }

        // 安全地调用api.updateTheme，添加空值检查
        if (api && typeof api.updateTheme === 'function') {
          await api.updateTheme(userConfig.value.theme)
        }
        console.log(`System theme changed to ${newSystemTheme}, updating application theme to ${actualTheme}`)
      }
    }
  })

  // Listen for system theme changes from main process (Windows)
  if (window.api && window.api.onSystemThemeChanged) {
    window.api.onSystemThemeChanged((newSystemTheme) => {
      if (userConfig.value.theme === 'auto') {
        const currentTheme = document.documentElement.className.replace('theme-', '')
        if (currentTheme !== newSystemTheme) {
          document.documentElement.className = `theme-${newSystemTheme}`

          // 安全地调用eventBus.emit，添加空值检查
          if (eventBus && typeof eventBus.emit === 'function') {
            eventBus.emit('updateTheme', newSystemTheme)
          }

          console.log(`System theme changed to ${newSystemTheme} (from main process)`)
        }
      }
    })
  }
}

const changeTheme = async () => {
  try {
    const actualTheme = getActualTheme(userConfig.value.theme)
    document.documentElement.className = `theme-${actualTheme}`

    // 安全地调用eventBus.emit，添加空值检查
    if (eventBus && typeof eventBus.emit === 'function') {
      eventBus.emit('updateTheme', actualTheme)
    }

    // 安全地调用api.updateTheme，添加空值检查
    if (api && typeof api.updateTheme === 'function') {
      await api.updateTheme(userConfig.value.theme)
    }

    await saveConfig()
  } catch (error) {
    console.error('Failed to change theme:', error)
    notification.error({
      message: t('user.themeSwitchFailed'),
      description: t('user.themeSwitchFailedDescription')
    })
  }
}
</script>

<style scoped>
.userInfo {
  width: 100%;
  height: 100%;
  /* 临时调试样式 */
  background-color: cyan !important;
  border: 3px solid red !important;
  color: #000000 !important;
}

.userInfo-container {
  width: 100%;
  height: 100%;
  background-color: var(--bg-color) !important;
  border-radius: 6px;
  overflow: hidden;
  padding: 4px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: var(--text-color);
}

:deep(.ant-card) {
  height: 100%;
  background-color: var(--bg-color) !important;
}

:deep(.ant-card-body) {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
}

.custom-form {
  color: var(--text-color);
  align-content: center;
}

.custom-form :deep(.ant-form-item-label) {
  padding-right: 20px;
}

.custom-form :deep(.ant-form-item-label > label) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input),
.custom-form :deep(.ant-input-number),
.custom-form :deep(.ant-radio-wrapper) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input-number) {
  background-color: var(--input-number-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s;
  width: 100px !important;
}

.custom-form :deep(.ant-input-number:hover),
.custom-form :deep(.ant-input-number:focus),
.custom-form :deep(.ant-input-number-focused) {
  background-color: var(--input-number-hover-bg);
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.custom-form :deep(.ant-input-number-input) {
  height: 32px;
  padding: 4px 8px;
  background-color: transparent;
  color: var(--text-color);
}

.label-text {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3;
}

.user_my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 30px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
  color: #ffffff;
}

.divider-container {
  width: calc(65%);
  margin: -10px calc(16%);
}

:deep(.right-aligned-wrapper) {
  text-align: right;
  color: #ffffff;
}

.checkbox-md :deep(.ant-checkbox-inner) {
  width: 20px;
  height: 20px;
}
</style>
