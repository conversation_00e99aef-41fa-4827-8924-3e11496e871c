<template>
  <div class="storage-config">
    <a-card
      :bordered="false"
      class="storage-config-container"
    >
      <template #title>
        <div class="config-header">
          <h3>存储配置</h3>
          <a-button
            type="primary"
            :icon="h(PlusOutlined)"
            @click="showAddModal"
          >
            添加存储后端
          </a-button>
        </div>
      </template>

      <!-- 存储配置列表 -->
      <div class="storage-list">
        <a-row :gutter="[16, 16]">
          <a-col
            v-for="config in storageConfigs"
            :key="config.id"
            :xs="24"
            :sm="12"
            :lg="8"
          >
            <a-card
              class="storage-card"
              :class="{ 'storage-card-enabled': config.enabled }"
            >
              <template #title>
                <div class="storage-card-title">
                  <a-avatar
                    :icon="getStorageIcon(config.type)"
                    :style="{ backgroundColor: getStorageColor(config.type) }"
                  />
                  <span class="storage-name">{{ config.name }}</span>
                  <a-tag
                    :color="config.enabled ? 'green' : 'default'"
                    class="storage-status"
                  >
                    {{ config.enabled ? '已启用' : '已禁用' }}
                  </a-tag>
                </div>
              </template>

              <template #extra>
                <a-dropdown>
                  <a-button
                    type="text"
                    :icon="h(MoreOutlined)"
                  />
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="editConfig(config)"> <EditOutlined /> 编辑 </a-menu-item>
                      <a-menu-item @click="testConnection(config.id)"> <ApiOutlined /> 测试连接 </a-menu-item>
                      <a-menu-item @click="toggleConfig(config)"> <PoweroffOutlined /> {{ config.enabled ? '禁用' : '启用' }} </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item
                        danger
                        @click="deleteConfig(config.id)"
                      >
                        <DeleteOutlined /> 删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </template>

              <div class="storage-info">
                <p class="storage-type">{{ getStorageTypeDisplayName(config.type) }}</p>
                <p class="storage-description">{{ getStorageTypeDescription(config.type) }}</p>
                <div class="storage-meta">
                  <span class="meta-item">
                    <CalendarOutlined />
                    创建时间：{{ formatDate(config.createdAt) }}
                  </span>
                  <span class="meta-item">
                    <SyncOutlined />
                    更新时间：{{ formatDate(config.updatedAt) }}
                  </span>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 空状态 -->
        <a-empty
          v-if="storageConfigs.length === 0"
          description="暂无存储配置"
          class="empty-state"
        >
          <a-button
            type="primary"
            @click="showAddModal"
          >
            添加第一个存储后端
          </a-button>
        </a-empty>
      </div>
    </a-card>

    <!-- 添加/编辑存储配置模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEditing ? '编辑存储配置' : '添加存储配置'"
      width="600px"
      :confirm-loading="submitting"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        class="storage-form"
      >
        <!-- 基本信息 -->
        <a-form-item
          label="配置名称"
          name="name"
        >
          <a-input
            v-model:value="formData.name"
            placeholder="请输入配置名称"
          />
        </a-form-item>

        <a-form-item
          label="存储类型"
          name="type"
        >
          <a-select
            v-model:value="formData.type"
            placeholder="请选择存储类型"
            :disabled="isEditing"
            @change="onStorageTypeChange"
          >
            <a-select-option
              v-for="type in supportedTypes"
              :key="type.type"
              :value="type.type"
            >
              <div class="storage-option">
                <a-avatar
                  size="small"
                  :icon="getStorageIcon(type.type)"
                  :style="{ backgroundColor: getStorageColor(type.type) }"
                />
                <span class="option-name">{{ type.displayName }}</span>
                <span class="option-description">{{ type.description }}</span>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 动态配置字段 -->
        <template v-if="formData.type && configFields.length > 0">
          <a-divider>连接配置</a-divider>
          <a-form-item
            v-for="field in configFields"
            :key="field.name"
            :label="field.label"
            :name="['config', field.name]"
          >
            <!-- 文本输入 -->
            <a-input
              v-if="field.type === 'text'"
              v-model:value="formData.config[field.name]"
              :placeholder="field.placeholder || `请输入${field.label}`"
            />

            <!-- 密码输入 -->
            <a-input-password
              v-else-if="field.type === 'password'"
              v-model:value="formData.config[field.name]"
              :placeholder="field.placeholder || `请输入${field.label}`"
            />

            <!-- 数字输入 -->
            <a-input-number
              v-else-if="field.type === 'number'"
              v-model:value="formData.config[field.name]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              style="width: 100%"
            />

            <!-- 布尔值 -->
            <a-switch
              v-else-if="field.type === 'boolean'"
              v-model:checked="formData.config[field.name]"
            />

            <!-- 文本域 -->
            <a-textarea
              v-else-if="field.type === 'textarea'"
              v-model:value="formData.config[field.name]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :rows="4"
            />

            <!-- 字段描述 -->
            <div
              v-if="field.description"
              class="field-description"
            >
              {{ field.description }}
            </div>
          </a-form-item>
        </template>

        <a-form-item
          label="启用状态"
          name="enabled"
        >
          <a-switch
            v-model:checked="formData.enabled"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  ApiOutlined,
  PoweroffOutlined,
  CalendarOutlined,
  SyncOutlined,
  CloudOutlined,
  GithubOutlined,
  DatabaseOutlined,
  ShareAltOutlined,
  ServerOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const storageConfigs = ref([])
const supportedTypes = ref([])
const modalVisible = ref(false)
const isEditing = ref(false)
const submitting = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  type: '',
  config: {},
  enabled: true
})

// 当前编辑的配置ID
const editingConfigId = ref('')

// 配置字段
const configFields = computed(() => {
  if (!formData.type) return []
  const typeInfo = supportedTypes.value.find((t) => t.type === formData.type)
  return typeInfo ? typeInfo.configFields : []
})

// 表单验证规则
const formRules = computed(() => {
  const rules = {
    name: [{ required: true, message: '请输入配置名称' }],
    type: [{ required: true, message: '请选择存储类型' }]
  }

  // 动态添加配置字段验证规则
  configFields.value.forEach((field) => {
    if (field.required) {
      rules[`config.${field.name}`] = [{ required: true, message: `请输入${field.label}` }]
    }
  })

  return rules
})

/**
 * 组件挂载时初始化数据
 */
onMounted(async () => {
  await loadStorageConfigs()
  await loadSupportedTypes()
})

/**
 * 加载存储配置列表
 */
const loadStorageConfigs = async () => {
  try {
    // 调用主进程API获取存储配置
    const configs = await window.api.getStorageConfigs()
    storageConfigs.value = configs || []
  } catch (error) {
    console.error('加载存储配置失败:', error)
    message.error('加载存储配置失败')
  }
}

/**
 * 加载支持的存储类型
 */
const loadSupportedTypes = async () => {
  try {
    // 调用主进程API获取支持的存储类型
    const types = await window.api.getSupportedStorageTypes()
    supportedTypes.value = types || []
  } catch (error) {
    console.error('加载存储类型失败:', error)
    message.error('加载存储类型失败')
  }
}

/**
 * 显示添加模态框
 */
const showAddModal = () => {
  isEditing.value = false
  editingConfigId.value = ''
  resetForm()
  modalVisible.value = true
}

/**
 * 编辑配置
 */
const editConfig = (config) => {
  isEditing.value = true
  editingConfigId.value = config.id

  // 填充表单数据
  formData.name = config.name
  formData.type = config.type
  formData.config = { ...config.config }
  formData.enabled = config.enabled

  modalVisible.value = true
}

/**
 * 重置表单
 */
const resetForm = () => {
  formData.name = ''
  formData.type = ''
  formData.config = {}
  formData.enabled = true

  if (formRef.value) {
    formRef.value.resetFields()
  }
}

/**
 * 存储类型变化处理
 */
const onStorageTypeChange = (type) => {
  // 重置配置对象
  formData.config = {}

  // 设置默认值
  const typeInfo = supportedTypes.value.find((t) => t.type === type)
  if (typeInfo && typeInfo.configFields) {
    typeInfo.configFields.forEach((field) => {
      if (field.defaultValue !== undefined) {
        formData.config[field.name] = field.defaultValue
      }
    })
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    const configData = {
      name: formData.name,
      type: formData.type,
      config: formData.config,
      enabled: formData.enabled
    }

    if (isEditing.value) {
      // 更新配置
      await window.api.updateStorageConfig(editingConfigId.value, configData)
      message.success('存储配置更新成功')
    } else {
      // 添加配置
      await window.api.addStorageConfig(configData)
      message.success('存储配置添加成功')
    }

    modalVisible.value = false
    await loadStorageConfigs()
  } catch (error) {
    console.error('保存配置失败:', error)
    message.error(error.message || '保存配置失败')
  } finally {
    submitting.value = false
  }
}

/**
 * 取消操作
 */
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

/**
 * 测试连接
 */
const testConnection = async (configId) => {
  try {
    const result = await window.api.testStorageConnection(configId)
    if (result) {
      message.success('连接测试成功')
    } else {
      message.error('连接测试失败')
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    message.error('连接测试失败')
  }
}

/**
 * 切换配置启用状态
 */
const toggleConfig = async (config) => {
  try {
    await window.api.updateStorageConfig(config.id, {
      enabled: !config.enabled
    })
    message.success(`存储配置已${config.enabled ? '禁用' : '启用'}`)
    await loadStorageConfigs()
  } catch (error) {
    console.error('切换配置状态失败:', error)
    message.error('操作失败')
  }
}

/**
 * 删除配置
 */
const deleteConfig = (configId) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个存储配置吗？此操作不可恢复。',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      try {
        await window.api.removeStorageConfig(configId)
        message.success('存储配置删除成功')
        await loadStorageConfigs()
      } catch (error) {
        console.error('删除配置失败:', error)
        message.error('删除配置失败')
      }
    }
  })
}

/**
 * 获取存储类型图标
 */
const getStorageIcon = (type) => {
  const icons = {
    onedrive: h(CloudOutlined),
    github: h(GithubOutlined),
    minio: h(DatabaseOutlined),
    smb: h(ShareAltOutlined),
    sftp: h(ServerOutlined)
  }
  return icons[type] || h(DatabaseOutlined)
}

/**
 * 获取存储类型颜色
 */
const getStorageColor = (type) => {
  const colors = {
    onedrive: '#0078d4',
    github: '#24292e',
    minio: '#c72e29',
    smb: '#00bcf2',
    sftp: '#ff6b35'
  }
  return colors[type] || '#1890ff'
}

/**
 * 获取存储类型显示名称
 */
const getStorageTypeDisplayName = (type) => {
  const typeInfo = supportedTypes.value.find((t) => t.type === type)
  return typeInfo ? typeInfo.displayName : type
}

/**
 * 获取存储类型描述
 */
const getStorageTypeDescription = (type) => {
  const typeInfo = supportedTypes.value.find((t) => t.type === type)
  return typeInfo ? typeInfo.description : ''
}

/**
 * 格式化日期
 */
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}
</script>

<style scoped>
.storage-config {
  width: 100%;
  height: 100%;
}

.storage-config-container {
  width: 100%;
  height: 100%;
  background-color: var(--bg-color) !important;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: var(--text-color);
}

:deep(.ant-card) {
  height: 100%;
  background-color: var(--bg-color) !important;
}

:deep(.ant-card-body) {
  height: calc(100% - 60px);
  overflow-y: auto;
  background-color: var(--bg-color);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.config-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 18px;
  font-weight: 600;
}

.storage-list {
  padding: 16px 0;
}

.storage-card {
  height: 200px;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
}

.storage-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.storage-card-enabled {
  border-color: #52c41a;
  box-shadow: 0 0 0 1px rgba(82, 196, 26, 0.2);
}

.storage-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.storage-name {
  font-weight: 600;
  color: var(--text-color);
}

.storage-status {
  margin-left: auto;
}

.storage-info {
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.storage-type {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  margin: 0 0 8px 0;
}

.storage-description {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.storage-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--text-color-secondary);
}

.empty-state {
  margin: 60px 0;
}

.storage-form {
  max-height: 500px;
  overflow-y: auto;
}

.storage-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-name {
  font-weight: 500;
  min-width: 80px;
}

.option-description {
  font-size: 12px;
  color: var(--text-color-secondary);
  flex: 1;
}

.field-description {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

/* 深色主题适配 */
:deep(.ant-card-head) {
  background-color: var(--bg-color) !important;
  border-bottom: 1px solid var(--border-color);
}

:deep(.ant-card-head-title) {
  color: var(--text-color) !important;
}

:deep(.ant-form-item-label > label) {
  color: var(--text-color) !important;
}

:deep(.ant-input),
:deep(.ant-input-password),
:deep(.ant-input-number),
:deep(.ant-select-selector),
:deep(.ant-textarea) {
  background-color: var(--input-bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

:deep(.ant-input:hover),
:deep(.ant-input-password:hover),
:deep(.ant-input-number:hover),
:deep(.ant-select-selector:hover),
:deep(.ant-textarea:hover) {
  border-color: #1890ff !important;
}

:deep(.ant-input:focus),
:deep(.ant-input-password:focus),
:deep(.ant-input-number:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-textarea:focus) {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

:deep(.ant-select-dropdown) {
  background-color: var(--dropdown-bg-color) !important;
}

:deep(.ant-select-item) {
  color: var(--text-color) !important;
}

:deep(.ant-select-item:hover) {
  background-color: var(--item-hover-bg-color) !important;
}

:deep(.ant-modal-content) {
  background-color: var(--modal-bg-color) !important;
}

:deep(.ant-modal-header) {
  background-color: var(--modal-bg-color) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

:deep(.ant-modal-title) {
  color: var(--text-color) !important;
}

:deep(.ant-divider-horizontal.ant-divider-with-text) {
  color: var(--text-color) !important;
}

:deep(.ant-divider-inner-text) {
  color: var(--text-color) !important;
}

:deep(.ant-switch-checked) {
  background-color: #1890ff !important;
}

:deep(.ant-tag) {
  border-color: var(--border-color) !important;
}

:deep(.ant-dropdown) {
  background-color: var(--dropdown-bg-color) !important;
}

:deep(.ant-dropdown-menu) {
  background-color: var(--dropdown-bg-color) !important;
}

:deep(.ant-dropdown-menu-item) {
  color: var(--text-color) !important;
}

:deep(.ant-dropdown-menu-item:hover) {
  background-color: var(--item-hover-bg-color) !important;
}

:deep(.ant-dropdown-menu-item-danger) {
  color: #ff4d4f !important;
}

:deep(.ant-dropdown-menu-item-danger:hover) {
  background-color: rgba(255, 77, 79, 0.1) !important;
}
</style>
