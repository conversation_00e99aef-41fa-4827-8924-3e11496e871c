<template>
  <div class="sync-management">
    <a-card
      :bordered="false"
      class="sync-management-container"
    >
      <template #title>
        <div class="management-header">
          <h3>同步管理</h3>
          <div class="header-actions">
            <a-button
              type="primary"
              :loading="syncing"
              :disabled="!hasEnabledConfigs"
              :icon="h(SyncOutlined)"
              @click="syncAll"
            >
              全部同步
            </a-button>
            <a-button
              :icon="h(ReloadOutlined)"
              @click="refreshStatus"
            >
              刷新状态
            </a-button>
          </div>
        </div>
      </template>

      <!-- 同步状态概览 -->
      <div class="sync-overview">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="总配置数"
              :value="totalConfigs"
              :value-style="{ color: '#1890ff' }"
              :prefix="h(DatabaseOutlined)"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="已启用"
              :value="enabledConfigs"
              :value-style="{ color: '#52c41a' }"
              :prefix="h(CheckCircleOutlined)"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="同步中"
              :value="syncingConfigs"
              :value-style="{ color: '#faad14' }"
              :prefix="h(LoadingOutlined)"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="错误数"
              :value="errorConfigs"
              :value-style="{ color: '#ff4d4f' }"
              :prefix="h(ExclamationCircleOutlined)"
            />
          </a-col>
        </a-row>
      </div>

      <a-divider />

      <!-- 同步任务列表 -->
      <div class="sync-tasks">
        <h4>同步任务</h4>

        <!-- 筛选和搜索 -->
        <div class="task-filters">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索配置名称"
            style="width: 200px"
            @search="onSearch"
          />
          <a-select
            v-model:value="statusFilter"
            placeholder="筛选状态"
            style="width: 120px"
            @change="onFilterChange"
          >
            <a-select-option value="all">全部状态</a-select-option>
            <a-select-option value="idle">空闲</a-select-option>
            <a-select-option value="syncing">同步中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="error">错误</a-select-option>
          </a-select>
        </div>

        <!-- 任务列表 -->
        <div class="task-list">
          <a-list
            :data-source="filteredTasks"
            :loading="loading"
          >
            <template #renderItem="{ item }">
              <a-list-item class="task-item">
                <template #actions>
                  <a-button
                    v-if="item.progress.status === 'idle'"
                    type="primary"
                    size="small"
                    :icon="h(PlayCircleOutlined)"
                    @click="startSync(item.config.id)"
                  >
                    开始同步
                  </a-button>
                  <a-button
                    v-else-if="item.progress.status === 'syncing'"
                    danger
                    size="small"
                    :icon="h(PauseCircleOutlined)"
                    @click="stopSync(item.config.id)"
                  >
                    停止同步
                  </a-button>
                  <a-button
                    v-else
                    size="small"
                    :icon="h(RedoOutlined)"
                    @click="startSync(item.config.id)"
                  >
                    重新同步
                  </a-button>
                  <a-dropdown>
                    <a-button
                      size="small"
                      :icon="h(MoreOutlined)"
                    />
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="viewLogs(item.config.id)"> <FileTextOutlined /> 查看日志 </a-menu-item>
                        <a-menu-item @click="viewDetails(item.config.id)"> <InfoCircleOutlined /> 查看详情 </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </template>

                <a-list-item-meta>
                  <template #title>
                    <div class="task-title">
                      <a-avatar
                        :icon="getStorageIcon(item.config.type)"
                        :style="{ backgroundColor: getStorageColor(item.config.type) }"
                        size="small"
                      />
                      <span class="task-name">{{ item.config.name }}</span>
                      <a-tag
                        :color="getStatusColor(item.progress.status)"
                        class="task-status"
                      >
                        {{ getStatusText(item.progress.status) }}
                      </a-tag>
                    </div>
                  </template>

                  <template #description>
                    <div class="task-description">
                      <div class="task-info">
                        <span class="info-item">
                          <span class="info-label">类型：</span>
                          <span class="info-value">{{ getStorageTypeDisplayName(item.config.type) }}</span>
                        </span>
                        <span class="info-item">
                          <span class="info-label">进度：</span>
                          <span class="info-value">{{ item.progress.processedFiles }}/{{ item.progress.totalFiles }}</span>
                        </span>
                        <span
                          v-if="item.progress.message"
                          class="info-item"
                        >
                          <span class="info-label">消息：</span>
                          <span class="info-value">{{ item.progress.message }}</span>
                        </span>
                      </div>

                      <!-- 进度条 -->
                      <div
                        v-if="item.progress.status === 'syncing'"
                        class="task-progress"
                      >
                        <a-progress
                          :percent="item.progress.progress"
                          :status="item.progress.status === 'error' ? 'exception' : 'active'"
                          :show-info="false"
                          size="small"
                        />
                        <span class="progress-text">{{ item.progress.progress }}%</span>
                      </div>

                      <!-- 错误信息 -->
                      <div
                        v-if="item.progress.error"
                        class="task-error"
                      >
                        <a-alert
                          :message="item.progress.error"
                          type="error"
                          size="small"
                          show-icon
                          closable
                        />
                      </div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-card>

    <!-- 同步详情模态框 -->
    <a-modal
      v-model:open="detailsModalVisible"
      title="同步详情"
      width="800px"
      :footer="null"
    >
      <div
        v-if="selectedTask"
        class="sync-details"
      >
        <a-descriptions
          :column="2"
          bordered
        >
          <a-descriptions-item label="配置名称">
            {{ selectedTask.config.name }}
          </a-descriptions-item>
          <a-descriptions-item label="存储类型">
            {{ getStorageTypeDisplayName(selectedTask.config.type) }}
          </a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(selectedTask.progress.status)">
              {{ getStatusText(selectedTask.progress.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="同步进度"> {{ selectedTask.progress.progress }}% </a-descriptions-item>
          <a-descriptions-item label="已处理文件">
            {{ selectedTask.progress.processedFiles }}
          </a-descriptions-item>
          <a-descriptions-item label="总文件数">
            {{ selectedTask.progress.totalFiles }}
          </a-descriptions-item>
          <a-descriptions-item
            v-if="selectedTask.progress.currentFile"
            label="当前文件"
            :span="2"
          >
            {{ selectedTask.progress.currentFile }}
          </a-descriptions-item>
          <a-descriptions-item
            v-if="selectedTask.progress.message"
            label="消息"
            :span="2"
          >
            {{ selectedTask.progress.message }}
          </a-descriptions-item>
          <a-descriptions-item
            v-if="selectedTask.progress.error"
            label="错误信息"
            :span="2"
          >
            <a-alert
              :message="selectedTask.progress.error"
              type="error"
              show-icon
            />
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 日志查看模态框 -->
    <a-modal
      v-model:open="logsModalVisible"
      title="同步日志"
      width="900px"
      :footer="null"
    >
      <div class="sync-logs">
        <div class="logs-header">
          <a-button
            :icon="h(ReloadOutlined)"
            size="small"
            @click="refreshLogs"
          >
            刷新日志
          </a-button>
          <a-button
            :icon="h(DeleteOutlined)"
            size="small"
            @click="clearLogs"
          >
            清空日志
          </a-button>
        </div>
        <div class="logs-content">
          <a-textarea
            v-model:value="logsContent"
            :rows="20"
            readonly
            class="logs-textarea"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  SyncOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RedoOutlined,
  MoreOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  CloudOutlined,
  GithubOutlined,
  ShareAltOutlined,
  ServerOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const storageConfigs = ref([])
const syncProgress = ref([])
const loading = ref(false)
const syncing = ref(false)
const searchText = ref('')
const statusFilter = ref('all')
const detailsModalVisible = ref(false)
const logsModalVisible = ref(false)
const selectedTask = ref(null)
const logsContent = ref('')
const refreshTimer = ref(null)

// 计算属性
const totalConfigs = computed(() => storageConfigs.value.length)
const enabledConfigs = computed(() => storageConfigs.value.filter((c) => c.enabled).length)
const syncingConfigs = computed(() => syncProgress.value.filter((p) => p.status === 'syncing').length)
const errorConfigs = computed(() => syncProgress.value.filter((p) => p.status === 'error').length)
const hasEnabledConfigs = computed(() => enabledConfigs.value > 0)

// 合并配置和进度数据
const syncTasks = computed(() => {
  return storageConfigs.value.map((config) => {
    const progress = syncProgress.value.find((p) => p.configId === config.id) || {
      configId: config.id,
      status: 'idle',
      progress: 0,
      totalFiles: 0,
      processedFiles: 0
    }
    return { config, progress }
  })
})

// 筛选后的任务列表
const filteredTasks = computed(() => {
  let tasks = syncTasks.value

  // 按名称搜索
  if (searchText.value) {
    tasks = tasks.filter((task) => task.config.name.toLowerCase().includes(searchText.value.toLowerCase()))
  }

  // 按状态筛选
  if (statusFilter.value !== 'all') {
    tasks = tasks.filter((task) => task.progress.status === statusFilter.value)
  }

  return tasks
})

/**
 * 组件挂载时初始化
 */
onMounted(async () => {
  await loadData()
  startAutoRefresh()
})

/**
 * 组件卸载时清理
 */
onUnmounted(() => {
  stopAutoRefresh()
})

/**
 * 加载数据
 */
const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([loadStorageConfigs(), loadSyncProgress()])
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

/**
 * 加载存储配置
 */
const loadStorageConfigs = async () => {
  try {
    const configs = await window.api.getStorageConfigs()
    storageConfigs.value = configs || []
  } catch (error) {
    console.error('加载存储配置失败:', error)
  }
}

/**
 * 加载同步进度
 */
const loadSyncProgress = async () => {
  try {
    const progress = await window.api.getAllSyncProgress()
    syncProgress.value = progress || []
  } catch (error) {
    console.error('加载同步进度失败:', error)
  }
}

/**
 * 刷新状态
 */
const refreshStatus = async () => {
  await loadData()
  message.success('状态已刷新')
}

/**
 * 开始自动刷新
 */
const startAutoRefresh = () => {
  refreshTimer.value = setInterval(async () => {
    await loadSyncProgress()
  }, 2000) // 每2秒刷新一次进度
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

/**
 * 开始单个同步
 */
const startSync = async (configId) => {
  try {
    await window.api.startSync(configId)
    message.success('同步已开始')
  } catch (error) {
    console.error('开始同步失败:', error)
    message.error(error.message || '开始同步失败')
  }
}

/**
 * 停止单个同步
 */
const stopSync = async (configId) => {
  try {
    await window.api.stopSync(configId)
    message.success('同步已停止')
  } catch (error) {
    console.error('停止同步失败:', error)
    message.error('停止同步失败')
  }
}

/**
 * 全部同步
 */
const syncAll = async () => {
  syncing.value = true
  try {
    await window.api.syncAll()
    message.success('批量同步已开始')
  } catch (error) {
    console.error('批量同步失败:', error)
    message.error('批量同步失败')
  } finally {
    syncing.value = false
  }
}

/**
 * 搜索处理
 */
const onSearch = (value) => {
  searchText.value = value
}

/**
 * 筛选变化处理
 */
const onFilterChange = (value) => {
  statusFilter.value = value
}

/**
 * 查看详情
 */
const viewDetails = (configId) => {
  const task = syncTasks.value.find((t) => t.config.id === configId)
  if (task) {
    selectedTask.value = task
    detailsModalVisible.value = true
  }
}

/**
 * 查看日志
 */
const viewLogs = async (configId) => {
  try {
    const logs = await window.api.getSyncLogs(configId)
    logsContent.value = logs || '暂无日志'
    logsModalVisible.value = true
  } catch (error) {
    console.error('获取日志失败:', error)
    message.error('获取日志失败')
  }
}

/**
 * 刷新日志
 */
const refreshLogs = async () => {
  if (selectedTask.value) {
    await viewLogs(selectedTask.value.config.id)
  }
}

/**
 * 清空日志
 */
const clearLogs = async () => {
  try {
    if (selectedTask.value) {
      await window.api.clearSyncLogs(selectedTask.value.config.id)
      logsContent.value = ''
      message.success('日志已清空')
    }
  } catch (error) {
    console.error('清空日志失败:', error)
    message.error('清空日志失败')
  }
}

/**
 * 获取存储类型图标
 */
const getStorageIcon = (type) => {
  const icons = {
    onedrive: h(CloudOutlined),
    github: h(GithubOutlined),
    minio: h(DatabaseOutlined),
    smb: h(ShareAltOutlined),
    sftp: h(ServerOutlined)
  }
  return icons[type] || h(DatabaseOutlined)
}

/**
 * 获取存储类型颜色
 */
const getStorageColor = (type) => {
  const colors = {
    onedrive: '#0078d4',
    github: '#24292e',
    minio: '#c72e29',
    smb: '#00bcf2',
    sftp: '#ff6b35'
  }
  return colors[type] || '#1890ff'
}

/**
 * 获取存储类型显示名称
 */
const getStorageTypeDisplayName = (type) => {
  const names = {
    onedrive: 'OneDrive',
    github: 'GitHub',
    minio: 'MinIO',
    smb: 'SMB',
    sftp: 'SFTP'
  }
  return names[type] || type
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status) => {
  const colors = {
    idle: 'default',
    connecting: 'processing',
    syncing: 'processing',
    completed: 'success',
    error: 'error',
    conflict: 'warning'
  }
  return colors[status] || 'default'
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  const texts = {
    idle: '空闲',
    connecting: '连接中',
    syncing: '同步中',
    completed: '已完成',
    error: '错误',
    conflict: '冲突'
  }
  return texts[status] || status
}
</script>

<style scoped>
.sync-management {
  width: 100%;
  height: 100%;
}

.sync-management-container {
  width: 100%;
  height: 100%;
  background-color: var(--bg-color) !important;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: var(--text-color);
}

:deep(.ant-card) {
  height: 100%;
  background-color: var(--bg-color) !important;
}

:deep(.ant-card-body) {
  height: calc(100% - 60px);
  overflow-y: auto;
  background-color: var(--bg-color);
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.management-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.sync-overview {
  margin-bottom: 24px;
}

.sync-tasks h4 {
  color: var(--text-color);
  margin-bottom: 16px;
}

.task-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.task-item {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-bottom: 8px;
  padding: 16px;
  background-color: var(--card-bg-color);
  transition: all 0.3s ease;
}

.task-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-name {
  font-weight: 600;
  color: var(--text-color);
}

.task-status {
  margin-left: auto;
}

.task-description {
  margin-top: 8px;
}

.task-info {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.info-item {
  font-size: 12px;
}

.info-label {
  color: var(--text-color-secondary);
}

.info-value {
  color: var(--text-color);
  font-weight: 500;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 12px;
  color: var(--text-color-secondary);
  min-width: 35px;
}

.task-error {
  margin-top: 8px;
}

.sync-details {
  padding: 16px 0;
}

.sync-logs {
  padding: 16px 0;
}

.logs-header {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.logs-content {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
}

.logs-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  background-color: var(--code-bg-color) !important;
  border: none !important;
}

/* 深色主题适配 */
:deep(.ant-card-head) {
  background-color: var(--bg-color) !important;
  border-bottom: 1px solid var(--border-color);
}

:deep(.ant-card-head-title) {
  color: var(--text-color) !important;
}

:deep(.ant-statistic-title) {
  color: var(--text-color-secondary) !important;
}

:deep(.ant-statistic-content) {
  color: var(--text-color) !important;
}

:deep(.ant-list-item) {
  border-bottom: 1px solid var(--border-color) !important;
}

:deep(.ant-list-item-meta-title) {
  color: var(--text-color) !important;
}

:deep(.ant-list-item-meta-description) {
  color: var(--text-color-secondary) !important;
}

:deep(.ant-input) {
  background-color: var(--input-bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

:deep(.ant-select-selector) {
  background-color: var(--input-bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

:deep(.ant-modal-content) {
  background-color: var(--modal-bg-color) !important;
}

:deep(.ant-modal-header) {
  background-color: var(--modal-bg-color) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

:deep(.ant-modal-title) {
  color: var(--text-color) !important;
}

:deep(.ant-descriptions-item-label) {
  color: var(--text-color) !important;
}

:deep(.ant-descriptions-item-content) {
  color: var(--text-color) !important;
}

:deep(.ant-textarea) {
  background-color: var(--input-bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}
</style>
