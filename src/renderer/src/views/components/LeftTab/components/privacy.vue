<template>
  <div class="userInfo">
    <a-card
      :bordered="false"
      class="userInfo-container"
    >
      <a-form
        :colon="false"
        label-align="left"
        wrapper-align="right"
        :label-col="{ span: 7, offset: 0 }"
        :wrapper-col="{ span: 17, class: 'right-aligned-wrapper' }"
        class="custom-form"
      >
        <a-form-item>
          <template #label>
            <span class="label-text">{{ $t('user.privacy') }}</span>
          </template>
        </a-form-item>
        <a-form-item
          class="description-item"
          :label-col="{ span: 0 }"
          :wrapper-col="{ span: 24 }"
        >
          <div class="description">
            查看我们的隐私政策了解更多信息。
            <a
              class="privacy-link"
              style="cursor: pointer"
              @click="showPrivacyPolicy"
            >
              隐私政策
            </a>
          </div>
        </a-form-item>
        <a-form-item
          :label="$t('user.secretRedaction')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.secretRedaction"
            class="custom-radio-group"
            @change="changeSecretRedaction"
          >
            <a-radio value="enabled">{{ $t('user.secretRedactionEnabled') }}</a-radio>
            <a-radio value="disabled">{{ $t('user.secretRedactionDisabled') }}</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          class="description-item"
          :label-col="{ span: 0 }"
          :wrapper-col="{ span: 24 }"
        >
          <div class="description">
            {{ $t('user.secretRedactionDescription') }}
          </div>
          <a-collapse
            v-if="userConfig.secretRedaction === 'enabled'"
            class="patterns-collapse"
            size="small"
            ghost
          >
            <a-collapse-panel
              key="patterns"
              :header="$t('user.supportedPatterns')"
            >
              <div class="patterns-list">
                <div
                  v-for="pattern in secretPatterns"
                  :key="pattern.name"
                  class="pattern-item"
                >
                  <div class="pattern-name">
                    {{ pattern.name }}: <code>{{ pattern.regex }}</code>
                  </div>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </a-form-item>
        <a-form-item
          :label="$t('user.dataSync')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.dataSync"
            class="custom-radio-group"
            @change="changeDataSync"
          >
            <a-radio value="enabled">{{ $t('user.dataSyncEnabled') }}</a-radio>
            <a-radio value="disabled">{{ $t('user.dataSyncDisabled') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item
          class="description-item"
          :label-col="{ span: 0 }"
          :wrapper-col="{ span: 24 }"
        >
          <div class="description">
            {{ $t('user.dataSyncDescription') }}
          </div>
        </a-form-item>

        <!-- 存储配置选项 - 仅在数据同步开启时显示 -->
        <div
          v-if="userConfig.dataSync === 'enabled'"
          class="storage-config-section"
        >
          <a-divider class="storage-divider">
            <span class="divider-text">存储配置</span>
          </a-divider>

          <!-- 存储类型选择器 -->
          <a-form-item
            label="存储类型"
            class="user_my-ant-form-item"
          >
            <a-select
              v-model:value="storageConfig.type"
              placeholder="请选择存储类型"
              class="storage-type-select"
              @change="onStorageTypeChange"
            >
              <a-select-option value="onedrive">
                <div class="storage-option">
                  <span class="storage-icon">☁️</span>
                  <span>OneDrive</span>
                </div>
              </a-select-option>
              <a-select-option value="github">
                <div class="storage-option">
                  <span class="storage-icon">🐙</span>
                  <span>GitHub</span>
                </div>
              </a-select-option>
              <a-select-option value="minio">
                <div class="storage-option">
                  <span class="storage-icon">🗄️</span>
                  <span>MinIO</span>
                </div>
              </a-select-option>
              <a-select-option value="smb">
                <div class="storage-option">
                  <span class="storage-icon">🌐</span>
                  <span>SMB</span>
                </div>
              </a-select-option>
              <a-select-option value="sftp">
                <div class="storage-option">
                  <span class="storage-icon">🔐</span>
                  <span>SFTP</span>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>

          <!-- OneDrive 配置表单 -->
          <div
            v-if="storageConfig.type === 'onedrive'"
            class="storage-form"
          >
            <a-form-item
              label="客户端 ID"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.onedrive.clientId"
                placeholder="请输入 OneDrive 客户端 ID"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="租户 ID"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.onedrive.tenantId"
                placeholder="请输入租户 ID（默认：common）"
                class="config-input"
              />
            </a-form-item>
          </div>

          <!-- GitHub 配置表单 -->
          <div
            v-if="storageConfig.type === 'github'"
            class="storage-form"
          >
            <a-form-item
              label="仓库所有者"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.github.owner"
                placeholder="请输入 GitHub 用户名或组织名"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="仓库名称"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.github.repo"
                placeholder="请输入仓库名称"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="访问令牌"
              class="config-form-item"
            >
              <a-input-password
                v-model:value="storageConfig.github.token"
                placeholder="请输入 GitHub Personal Access Token"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="分支"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.github.branch"
                placeholder="分支名称（默认：main）"
                class="config-input"
              />
            </a-form-item>
          </div>

          <!-- MinIO 配置表单 -->
          <div
            v-if="storageConfig.type === 'minio'"
            class="storage-form"
          >
            <a-form-item
              label="服务端点"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.minio.endpoint"
                placeholder="例如：https://minio.example.com"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="访问密钥"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.minio.accessKey"
                placeholder="请输入 Access Key"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="秘密密钥"
              class="config-form-item"
            >
              <a-input-password
                v-model:value="storageConfig.minio.secretKey"
                placeholder="请输入 Secret Key"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="存储桶"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.minio.bucket"
                placeholder="请输入存储桶名称"
                class="config-input"
              />
            </a-form-item>
          </div>

          <!-- SMB 配置表单 -->
          <div
            v-if="storageConfig.type === 'smb'"
            class="storage-form"
          >
            <a-form-item
              label="服务器地址"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.smb.host"
                placeholder="例如：*************"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="端口"
              class="config-form-item"
            >
              <a-input-number
                v-model:value="storageConfig.smb.port"
                placeholder="445"
                :min="1"
                :max="65535"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="用户名"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.smb.username"
                placeholder="请输入用户名"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="密码"
              class="config-form-item"
            >
              <a-input-password
                v-model:value="storageConfig.smb.password"
                placeholder="请输入密码"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="共享名"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.smb.share"
                placeholder="请输入共享文件夹名称"
                class="config-input"
              />
            </a-form-item>
          </div>

          <!-- SFTP 配置表单 -->
          <div
            v-if="storageConfig.type === 'sftp'"
            class="storage-form"
          >
            <a-form-item
              label="服务器地址"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.sftp.host"
                placeholder="例如：sftp.example.com"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="端口"
              class="config-form-item"
            >
              <a-input-number
                v-model:value="storageConfig.sftp.port"
                placeholder="22"
                :min="1"
                :max="65535"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="用户名"
              class="config-form-item"
            >
              <a-input
                v-model:value="storageConfig.sftp.username"
                placeholder="请输入用户名"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              label="认证方式"
              class="config-form-item"
            >
              <a-radio-group
                v-model:value="storageConfig.sftp.authType"
                class="auth-type-group"
              >
                <a-radio value="password">密码认证</a-radio>
                <a-radio value="key">密钥认证</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item
              v-if="storageConfig.sftp.authType === 'password'"
              label="密码"
              class="config-form-item"
            >
              <a-input-password
                v-model:value="storageConfig.sftp.password"
                placeholder="请输入密码"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              v-if="storageConfig.sftp.authType === 'key'"
              label="私钥"
              class="config-form-item"
            >
              <a-textarea
                v-model:value="storageConfig.sftp.privateKey"
                placeholder="请输入 SSH 私钥内容"
                :rows="4"
                class="config-input"
              />
            </a-form-item>
            <a-form-item
              v-if="storageConfig.sftp.authType === 'key'"
              label="密钥密码"
              class="config-form-item"
            >
              <a-input-password
                v-model:value="storageConfig.sftp.passphrase"
                placeholder="私钥密码（可选）"
                class="config-input"
              />
            </a-form-item>
          </div>

          <!-- 连接测试和保存按钮 -->
          <div
            v-if="storageConfig.type"
            class="storage-actions"
          >
            <a-space>
              <a-button
                type="default"
                :loading="testingConnection"
                @click="testConnection"
              >
                <template #icon>
                  <span>🔗</span>
                </template>
                测试连接
              </a-button>
              <a-button
                type="primary"
                :loading="savingConfig"
                @click="saveStorageConfig"
              >
                <template #icon>
                  <span>💾</span>
                </template>
                保存配置
              </a-button>
            </a-space>
          </div>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, h } from 'vue'
import { notification, Modal } from 'ant-design-vue'
import { userConfigStore } from '@/services/userConfigStoreService'
import { dataSyncService } from '@/services/dataSyncService'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const userConfig = ref({
  secretRedaction: 'disabled',
  dataSync: 'disabled'
})

// 存储配置状态
const storageConfig = ref({
  type: '',
  onedrive: {
    clientId: '',
    tenantId: 'common'
  },
  github: {
    owner: '',
    repo: '',
    token: '',
    branch: 'main'
  },
  minio: {
    endpoint: '',
    accessKey: '',
    secretKey: '',
    bucket: ''
  },
  smb: {
    host: '',
    port: 445,
    username: '',
    password: '',
    share: ''
  },
  sftp: {
    host: '',
    port: 22,
    username: '',
    password: '',
    privateKey: '',
    passphrase: '',
    authType: 'password'
  }
})

// 加载和保存状态
const testingConnection = ref(false)
const savingConfig = ref(false)

const secretPatterns = computed(() => [
  {
    name: t('user.ipv4Address'),
    regex: '\\b((25[0-5]|(2[0-4]|1\\d|[1-9]|)\\d)\\.?\\b){4}\\b'
  },
  {
    name: t('user.ipv6Address'),
    regex: '\\b((([0-9A-Fa-f]{1,4}:){1,6}:)|(([0-9A-Fa-f]{1,4}:){7}))([0-9A-Fa-f]{1,4})\\b'
  },
  {
    name: t('user.slackAppToken'),
    regex: '\\bxapp-[0-9]+-[A-Za-z0-9_]+-[0-9]+-[a-f0-9]+\\b'
  },
  {
    name: t('user.phoneNumber'),
    regex: '\\b(\\+\\d{1,2}\\s)?\\(?\\d{3}\\)?[\\s.-]\\d{3}[\\s.-]\\d{4}\\b'
  },
  {
    name: t('user.awsAccessId'),
    regex: '\\b(AKIA|A3T|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{12,}\\b'
  },
  {
    name: t('user.macAddress'),
    regex: '\\b((([a-zA-z0-9]{2}[-:]){5}([a-zA-z0-9]{2}))|(([a-zA-z0-9]{2}:){5}([a-zA-z0-9]{2})))\\b'
  },
  {
    name: t('user.googleApiKey'),
    regex: '\\bAIza[0-9A-Za-z-_]{35}\\b'
  },
  {
    name: t('user.googleOAuthId'),
    regex: '\\b[0-9]+-[0-9A-Za-z_]{32}\\.apps\\.googleusercontent\\.com\\b'
  },
  {
    name: t('user.githubClassicPersonalAccessToken'),
    regex: '\\bghp_[A-Za-z0-9_]{36}\\b'
  },
  {
    name: t('user.githubFineGrainedPersonalAccessToken'),
    regex: '\\bgithub_pat_[A-Za-z0-9_]{82}\\b'
  },
  {
    name: t('user.githubOAuthAccessToken'),
    regex: '\\bgho_[A-Za-z0-9_]{36}\\b'
  },
  {
    name: t('user.githubUserToServerToken'),
    regex: '\\bghu_[A-Za-z0-9_]{36}\\b'
  },
  {
    name: t('user.githubServerToServerToken'),
    regex: '\\bghs_[A-Za-z0-9_]{36}\\b'
  },
  {
    name: t('user.stripeKey'),
    regex: '\\b(?:r|s)k_(test|live)_[0-9a-zA-Z]{24}\\b'
  },
  {
    name: t('user.firebaseAuthDomain'),
    regex: '\\b([a-z0-9-]){1,30}(\\.firebaseapp\\.com)\\b'
  },
  {
    name: t('user.jsonWebToken'),
    regex: '\\b(ey[a-zA-z0-9_\\-=]{10,}\\.){2}[a-zA-z0-9_\\-=]{10,}\\b'
  },
  {
    name: t('user.openaiApiKey'),
    regex: '\\bsk-[a-zA-Z0-9]{48}\\b'
  },
  {
    name: t('user.anthropicApiKey'),
    regex: '\\bsk-ant-api\\d{0,2}-[a-zA-Z0-9\\-]{80,120}\\b'
  },
  {
    name: t('user.fireworksApiKey'),
    regex: '\\bfw_[a-zA-Z0-9]{24}\\b'
  }
])

const loadSavedConfig = async () => {
  try {
    const savedConfig = await userConfigStore.getConfig()
    if (savedConfig) {
      userConfig.value.secretRedaction = savedConfig.secretRedaction ?? 'disabled'
      userConfig.value.dataSync = savedConfig.dataSync ?? 'disabled'

      // 加载存储配置
      if (savedConfig.storageConfig) {
        storageConfig.value = {
          ...storageConfig.value,
          ...savedConfig.storageConfig
        }
      }
    }
  } catch (error) {
    console.error('Failed to load config:', error)
    notification.error({
      message: t('user.loadConfigFailed'),
      description: t('user.loadConfigFailedDescription')
    })
  }
}

const saveConfig = async () => {
  try {
    const configToStore = {
      secretRedaction: userConfig.value.secretRedaction,
      dataSync: userConfig.value.dataSync
    }
    await userConfigStore.saveConfig(configToStore)
  } catch (error) {
    console.error('Failed to save config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveConfigFailedDescription')
    })
  }
}

watch(
  () => userConfig.value,
  async () => {
    await saveConfig()
  },
  { deep: true }
)

onMounted(async () => {
  await loadSavedConfig()
})

const showPrivacyPolicy = () => {
  Modal.info({
    title: '隐私政策',
    content: h(
      'div',
      {
        style: {
          maxHeight: '400px',
          overflow: 'auto',
          whiteSpace: 'pre-line',
          lineHeight: '1.6'
        }
      },
      [
        '欢迎使用 Chaterm\n\n',
        'RS - Chaterm（/tʃɑːtɜːm/）是一款与 AI 深度集成的智能终端工具。它不仅提供 AI 对话和终端命令执行功能，更具备基于 Agent 的 AI 自动化能力，可在任意主机上执行命令查询、错误排查和任务处理。\n\n',
        '### 为什么选择 RS - Chaterm？\n\n',
        '#### 🚀 云实践者的 AI 终端\n\n',
        '在分钟内获得数年的 SRE 经验。可并行管理多个云资源，轻松保护信息安全。\n\n',
        '### 核心特性\n\n',
        '#### 💡 智能化功能\n\n',
        '* **AI Agents** - 智能代理自动化执行任务\n',
        '* **智能补全** - 上下文感知的命令补全\n',
        '* **语音指令** - 支持语音控制操作\n\n',
        '#### ⚡ 高效工具\n\n',
        '* **全局语法高亮** - 提升 Shell 命令可读性\n',
        '* **可视化 Vim 编辑器** - 现代化的编辑体验\n',
        '* **Global Alias** - 快速访问常用命令\n\n',
        '#### 🔐 企业级安全保障\n\n',
        '* **零信任架构** - 全方位的安全防护\n',
        '* **身份和访问管理** - 精细化的权限控制\n',
        '* **隐私水印** - 数据追踪与保护\n',
        '* **数据加密** - 端到端加密保护\n',
        '* **访问控制** - 多层次的访问权限管理\n',
        '* **安全审计** - 全面的操作日志记录\n\n',
        '### 快速开始\n\n',
        '准备好开始使用 RS - Chaterm 了吗？查看我们的安装指南，开启您的智能终端之旅。'
      ]
    ),
    width: 600,
    okText: '确定'
  })
}

const changeSecretRedaction = async () => {
  await saveConfig()
}

const changeDataSync = async () => {
  await saveConfig()

  const isEnabled = userConfig.value.dataSync === 'enabled'

  try {
    // 首先检查API状态
    const apiStatus = dataSyncService.getApiStatus()
    if (!apiStatus.available) {
      console.warn('数据同步API不可用:', apiStatus)

      // 提供更详细的错误信息
      let errorDescription = '数据同步功能暂时不可用'
      if (apiStatus.reason === 'window.api 未定义') {
        errorDescription = '应用正在初始化中，请稍后再试'
      } else if (apiStatus.reason === 'setDataSyncEnabled 方法不存在') {
        errorDescription = '数据同步功能未正确加载，请重启应用'
      }

      notification.warning({
        message: '数据同步设置暂时不可用',
        description: errorDescription,
        duration: 5
      })

      // 回滚配置更改
      userConfig.value.dataSync = isEnabled ? 'disabled' : 'enabled'
      await saveConfig()
      return
    }

    let success = false

    if (isEnabled) {
      success = await dataSyncService.enableDataSync()
    } else {
      success = await dataSyncService.disableDataSync()
      // 关闭数据同步时重置存储配置
      resetStorageConfig()
    }

    if (success) {
      notification.success({
        message: '数据同步设置已更新',
        description: isEnabled ? '已开启数据同步' : '已关闭数据同步'
      })
    } else {
      // 获取更详细的错误信息
      const currentApiStatus = dataSyncService.getApiStatus()
      let errorDescription = '请稍后重试'

      if (!currentApiStatus.available) {
        errorDescription = `API不可用: ${currentApiStatus.reason}`
      }

      notification.error({
        message: '数据同步设置更新失败',
        description: errorDescription,
        duration: 8
      })

      // 回滚配置更改
      userConfig.value.dataSync = isEnabled ? 'disabled' : 'enabled'
      await saveConfig()
    }
  } catch (error) {
    console.error('Failed to change data sync setting:', error)

    // 提供更具体的错误信息
    let errorDescription = '请稍后重试'
    if (error.message && error.message.includes('API')) {
      errorDescription = '数据同步服务连接失败，请检查网络连接'
    } else if (error.message && error.message.includes('timeout')) {
      errorDescription = '操作超时，请稍后重试'
    }

    notification.error({
      message: '数据同步设置更新失败',
      description: errorDescription,
      duration: 8
    })

    // 回滚配置更改
    userConfig.value.dataSync = isEnabled ? 'disabled' : 'enabled'
    await saveConfig()
  }
}

/**
 * 存储类型变更处理
 */
const onStorageTypeChange = (type) => {
  console.log('存储类型已变更为:', type)
  // 可以在这里添加类型变更的特殊处理逻辑
}

/**
 * 重置存储配置
 */
const resetStorageConfig = () => {
  storageConfig.value = {
    type: '',
    onedrive: {
      clientId: '',
      tenantId: 'common'
    },
    github: {
      owner: '',
      repo: '',
      token: '',
      branch: 'main'
    },
    minio: {
      endpoint: '',
      accessKey: '',
      secretKey: '',
      bucket: ''
    },
    smb: {
      host: '',
      port: 445,
      username: '',
      password: '',
      share: ''
    },
    sftp: {
      host: '',
      port: 22,
      username: '',
      password: '',
      privateKey: '',
      passphrase: '',
      authType: 'password'
    }
  }
}

/**
 * 测试存储连接
 */
const testConnection = async () => {
  if (!storageConfig.value.type) {
    notification.warning({
      message: '请先选择存储类型',
      description: '需要选择一种存储类型才能测试连接'
    })
    return
  }

  testingConnection.value = true

  try {
    // 验证配置完整性
    const isValid = validateStorageConfig()
    if (!isValid) {
      return
    }

    // 模拟连接测试（实际应该调用对应的存储适配器）
    await new Promise((resolve) => setTimeout(resolve, 2000))

    notification.success({
      message: '连接测试成功',
      description: `${getStorageTypeName(storageConfig.value.type)} 连接正常`
    })
  } catch (error) {
    console.error('连接测试失败:', error)
    notification.error({
      message: '连接测试失败',
      description: error.message || '请检查配置信息是否正确'
    })
  } finally {
    testingConnection.value = false
  }
}

/**
 * 保存存储配置
 */
const saveStorageConfig = async () => {
  if (!storageConfig.value.type) {
    notification.warning({
      message: '请先选择存储类型',
      description: '需要选择一种存储类型才能保存配置'
    })
    return
  }

  savingConfig.value = true

  try {
    // 验证配置完整性
    const isValid = validateStorageConfig()
    if (!isValid) {
      return
    }

    // 保存到本地存储
    const configToSave = {
      ...userConfig.value,
      storageConfig: storageConfig.value
    }

    await userConfigStore.saveConfig(configToSave)

    notification.success({
      message: '存储配置已保存',
      description: `${getStorageTypeName(storageConfig.value.type)} 配置保存成功`
    })
  } catch (error) {
    console.error('保存存储配置失败:', error)
    notification.error({
      message: '保存配置失败',
      description: error.message || '请稍后重试'
    })
  } finally {
    savingConfig.value = false
  }
}

/**
 * 验证存储配置
 */
const validateStorageConfig = () => {
  const { type } = storageConfig.value

  switch (type) {
    case 'onedrive':
      if (!storageConfig.value.onedrive.clientId) {
        notification.error({ message: '请输入 OneDrive 客户端 ID' })
        return false
      }
      break
    case 'github':
      if (!storageConfig.value.github.owner || !storageConfig.value.github.repo || !storageConfig.value.github.token) {
        notification.error({ message: '请完整填写 GitHub 配置信息' })
        return false
      }
      break
    case 'minio':
      if (
        !storageConfig.value.minio.endpoint ||
        !storageConfig.value.minio.accessKey ||
        !storageConfig.value.minio.secretKey ||
        !storageConfig.value.minio.bucket
      ) {
        notification.error({ message: '请完整填写 MinIO 配置信息' })
        return false
      }
      break
    case 'smb':
      if (!storageConfig.value.smb.host || !storageConfig.value.smb.username || !storageConfig.value.smb.password || !storageConfig.value.smb.share) {
        notification.error({ message: '请完整填写 SMB 配置信息' })
        return false
      }
      break
    case 'sftp':
      if (!storageConfig.value.sftp.host || !storageConfig.value.sftp.username) {
        notification.error({ message: '请填写 SFTP 服务器地址和用户名' })
        return false
      }
      if (storageConfig.value.sftp.authType === 'password' && !storageConfig.value.sftp.password) {
        notification.error({ message: '请输入 SFTP 密码' })
        return false
      }
      if (storageConfig.value.sftp.authType === 'key' && !storageConfig.value.sftp.privateKey) {
        notification.error({ message: '请输入 SSH 私钥' })
        return false
      }
      break
    default:
      notification.error({ message: '未知的存储类型' })
      return false
  }

  return true
}

/**
 * 获取存储类型显示名称
 */
const getStorageTypeName = (type) => {
  const names = {
    onedrive: 'OneDrive',
    github: 'GitHub',
    minio: 'MinIO',
    smb: 'SMB',
    sftp: 'SFTP'
  }
  return names[type] || type
}
</script>

<style scoped>
.userInfo {
  width: 100%;
  height: 100%;
}

.userInfo-container {
  width: 100%;
  height: 100%;
  background-color: var(--bg-color) !important;
  border-radius: 6px;
  overflow: hidden;
  padding: 4px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: var(--text-color);
}

:deep(.ant-card) {
  height: 100%;
  background-color: var(--bg-color) !important;
}

:deep(.ant-card-body) {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
}

.custom-form {
  color: var(--text-color);
  align-content: center;
}

.custom-form :deep(.ant-form-item-label) {
  padding-right: 20px;
}

.custom-form :deep(.ant-form-item-label > label) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input),
.custom-form :deep(.ant-input-number),
.custom-form :deep(.ant-radio-wrapper) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input-number) {
  background-color: var(--input-number-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s;
  width: 100px !important;
}

.custom-form :deep(.ant-input-number:hover),
.custom-form :deep(.ant-input-number:focus),
.custom-form :deep(.ant-input-number-focused) {
  background-color: var(--input-number-hover-bg);
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.custom-form :deep(.ant-input-number-input) {
  height: 32px;
  padding: 4px 8px;
  background-color: transparent;
  color: var(--text-color);
}

.label-text {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3;
}

.user_my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 30px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
  color: #ffffff;
}

.divider-container {
  width: calc(65%);
  margin: -10px calc(16%);
}

:deep(.right-aligned-wrapper) {
  text-align: right;
  color: #ffffff;
}

.checkbox-md :deep(.ant-checkbox-inner) {
  width: 20px;
  height: 20px;
}

.description-item {
  margin-top: -15px;
  margin-bottom: 14px;
}

.description-item :deep(.ant-form-item-control) {
  margin-left: 0 !important;
  max-width: 100% !important;
}

.description {
  font-size: 12px;
  color: var(--text-color-secondary);
  line-height: 1.4;
  opacity: 0.8;
  text-align: left;
  margin: 0;
  padding: 0;
  word-wrap: break-word;
}

.description a,
.privacy-link {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.3s;
}

.description a:hover,
.privacy-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.patterns-collapse {
  margin-top: 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--bg-color-secondary);
}

.patterns-collapse :deep(.ant-collapse-header) {
  background-color: var(--bg-color-secondary);
  color: var(--text-color);
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 500;
}

.patterns-collapse :deep(.ant-collapse-content-box) {
  padding: 12px;
  background-color: var(--bg-color);
}

.patterns-list {
  max-height: 300px;
  overflow-y: auto;
}

.pattern-item {
  margin-bottom: 8px;
  padding: 8px;
  background-color: var(--bg-color-secondary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.pattern-item:last-child {
  margin-bottom: 0;
}

.pattern-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
}

.pattern-name code {
  background-color: var(--bg-color);
  color: var(--text-color-secondary);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 10px;
  word-break: break-all;
  border: 1px solid var(--border-color);
}

/* 存储配置样式 */
.storage-config-section {
  margin-top: 20px;
  padding: 16px;
  background-color: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.storage-divider {
  margin: 16px 0;
}

.storage-divider :deep(.ant-divider-inner-text) {
  color: var(--text-color);
  font-weight: 600;
  font-size: 14px;
}

.divider-text {
  color: var(--text-color);
  font-weight: 600;
}

.storage-type-select {
  width: 100%;
}

.storage-type-select :deep(.ant-select-selector) {
  background-color: var(--bg-color);
  border-color: var(--border-color);
  color: var(--text-color);
}

.storage-type-select :deep(.ant-select-selection-item) {
  color: var(--text-color);
}

.storage-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.storage-icon {
  font-size: 16px;
}

.storage-form {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--bg-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.config-form-item {
  margin-bottom: 16px;
}

.config-form-item:last-child {
  margin-bottom: 0;
}

.config-form-item :deep(.ant-form-item-label) {
  padding-right: 12px;
}

.config-form-item :deep(.ant-form-item-label > label) {
  color: var(--text-color);
  font-size: 13px;
  font-weight: 500;
}

.config-input {
  width: 100%;
}

.config-input :deep(.ant-input),
.config-input :deep(.ant-input-password),
.config-input :deep(.ant-input-number),
.config-input :deep(.ant-select-selector) {
  background-color: var(--bg-color-secondary);
  border-color: var(--border-color);
  color: var(--text-color);
}

.config-input :deep(.ant-input:focus),
.config-input :deep(.ant-input-password:focus),
.config-input :deep(.ant-input-number:focus),
.config-input :deep(.ant-select-focused .ant-select-selector) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.config-input :deep(.ant-input::placeholder),
.config-input :deep(.ant-input-password::placeholder) {
  color: var(--text-color-secondary);
  opacity: 0.6;
}

.auth-type-group {
  display: flex;
  gap: 16px;
}

.auth-type-group :deep(.ant-radio-wrapper) {
  color: var(--text-color);
}

.storage-actions {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.storage-actions :deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
}

.storage-actions :deep(.ant-btn-default) {
  background-color: var(--bg-color);
  border-color: var(--border-color);
  color: var(--text-color);
}

.storage-actions :deep(.ant-btn-default:hover) {
  background-color: var(--bg-color-secondary);
  border-color: #1890ff;
  color: #1890ff;
}

.storage-actions :deep(.ant-btn-primary) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.storage-actions :deep(.ant-btn-primary:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .storage-config-section {
    padding: 12px;
  }

  .storage-form {
    padding: 12px;
  }

  .config-form-item :deep(.ant-form-item-label) {
    text-align: left;
  }

  .storage-actions {
    justify-content: center;
  }

  .auth-type-group {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
