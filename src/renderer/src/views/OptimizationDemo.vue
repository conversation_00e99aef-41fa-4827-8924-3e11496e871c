<template>
  <div class="optimization-demo">
    <div class="demo-header">
      <h1>Chaterm 优化功能展示</h1>
      <p>这里展示了所有已实现的优化功能</p>
    </div>

    <div class="demo-grid">
      <!-- 性能监控 -->
      <div class="demo-card" @click="showPerformanceMonitor = !showPerformanceMonitor">
        <div class="card-icon">📊</div>
        <h3>性能监控</h3>
        <p>实时监控终端性能指标，包括内存使用、CPU占用、连接状态等</p>
        <div class="card-status" :class="{ active: showPerformanceMonitor }">
          {{ showPerformanceMonitor ? '已开启' : '点击开启' }}
        </div>
      </div>

      <!-- AI助手 -->
      <div class="demo-card" @click="showAIAssistant = !showAIAssistant">
        <div class="card-icon">🤖</div>
        <h3>AI智能助手</h3>
        <p>AI驱动的错误诊断、命令优化和智能建议系统</p>
        <div class="card-status" :class="{ active: showAIAssistant }">
          {{ showAIAssistant ? '已开启' : '点击开启' }}
        </div>
      </div>

      <!-- 云端集成 -->
      <div class="demo-card" @click="showCloudIntegration = !showCloudIntegration">
        <div class="card-icon">☁️</div>
        <h3>云端集成</h3>
        <p>支持AWS、Azure、GCP等多云服务提供商的统一管理</p>
        <div class="card-status" :class="{ active: showCloudIntegration }">
          {{ showCloudIntegration ? '已开启' : '点击开启' }}
        </div>
      </div>

      <!-- 数据分析 -->
      <div class="demo-card" @click="showDataAnalytics = !showDataAnalytics">
        <div class="card-icon">📈</div>
        <h3>数据分析</h3>
        <p>用户行为分析、性能洞察和个性化建议系统</p>
        <div class="card-status" :class="{ active: showDataAnalytics }">
          {{ showDataAnalytics ? '已开启' : '点击开启' }}
        </div>
      </div>

      <!-- 协作功能 -->
      <div class="demo-card" @click="showCollaboration = !showCollaboration">
        <div class="card-icon">👥</div>
        <h3>实时协作</h3>
        <p>多用户实时协作、会话共享和团队聊天功能</p>
        <div class="card-status" :class="{ active: showCollaboration }">
          {{ showCollaboration ? '已开启' : '点击开启' }}
        </div>
      </div>

      <!-- 插件管理 -->
      <div class="demo-card" @click="showPluginManager = !showPluginManager">
        <div class="card-icon">🔌</div>
        <h3>插件系统</h3>
        <p>可扩展的插件架构，支持第三方插件和自定义功能</p>
        <div class="card-status" :class="{ active: showPluginManager }">
          {{ showPluginManager ? '已开启' : '点击开启' }}
        </div>
      </div>
    </div>

    <!-- 功能面板展示区域 -->
    <div class="demo-panels">
      <Suspense v-if="showPerformanceMonitor">
        <PerformanceMonitor @close="showPerformanceMonitor = false" />
        <template #fallback>
          <div class="loading">加载性能监控中...</div>
        </template>
      </Suspense>

      <Suspense v-if="showAIAssistant">
        <AIAssistantPanel @close="showAIAssistant = false" />
        <template #fallback>
          <div class="loading">加载AI助手中...</div>
        </template>
      </Suspense>

      <Suspense v-if="showCloudIntegration">
        <CloudIntegrationPanel @close="showCloudIntegration = false" />
        <template #fallback>
          <div class="loading">加载云端集成中...</div>
        </template>
      </Suspense>

      <Suspense v-if="showDataAnalytics">
        <DataAnalyticsPanel @close="showDataAnalytics = false" />
        <template #fallback>
          <div class="loading">加载数据分析中...</div>
        </template>
      </Suspense>

      <Suspense v-if="showCollaboration">
        <CollaborationPanel @close="showCollaboration = false" />
        <template #fallback>
          <div class="loading">加载协作功能中...</div>
        </template>
      </Suspense>

      <Suspense v-if="showPluginManager">
        <PluginManager @close="showPluginManager = false" />
        <template #fallback>
          <div class="loading">加载插件管理中...</div>
        </template>
      </Suspense>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, Suspense } from 'vue'

// 异步导入组件
const PerformanceMonitor = () => import('../components/PerformanceMonitor.vue')
const AIAssistantPanel = () => import('../components/AIAssistantPanel.vue')
const CloudIntegrationPanel = () => import('../components/CloudIntegrationPanel.vue')
const DataAnalyticsPanel = () => import('../components/DataAnalyticsPanel.vue')
const CollaborationPanel = () => import('../components/CollaborationPanel.vue')
const PluginManager = () => import('../components/PluginManager.vue')

// 状态管理
const showPerformanceMonitor = ref(false)
const showAIAssistant = ref(false)
const showCloudIntegration = ref(false)
const showDataAnalytics = ref(false)
const showCollaboration = ref(false)
const showPluginManager = ref(false)
</script>

<style lang="less" scoped>
.optimization-demo {
  padding: 20px;
  background: var(--bg-color);
  min-height: 100vh;
  color: var(--text-color);

  .demo-header {
    text-align: center;
    margin-bottom: 40px;

    h1 {
      font-size: 32px;
      margin-bottom: 10px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
      color: var(--text-color-secondary);
    }
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;

    .demo-card {
      background: var(--card-bg-color);
      border: 1px solid var(--border-color);
      border-radius: 12px;
      padding: 24px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: var(--primary-color);
      }

      .card-icon {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }

      h3 {
        font-size: 20px;
        margin-bottom: 12px;
        color: var(--text-color);
      }

      p {
        font-size: 14px;
        color: var(--text-color-secondary);
        line-height: 1.5;
        margin-bottom: 16px;
      }

      .card-status {
        position: absolute;
        top: 16px;
        right: 16px;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
        background: var(--border-color);
        color: var(--text-color-secondary);
        transition: all 0.3s ease;

        &.active {
          background: var(--success-color);
          color: white;
        }
      }
    }
  }

  .demo-panels {
    position: relative;
  }

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    font-size: 16px;
    color: var(--text-color-secondary);
  }
}
</style>
</script>
