export type SecretKey =
  | 'apiKey'
  | 'clineApiKey'
  | 'openRouterApiKey'
  | 'awsAccessKey'
  | 'awsSecretKey'
  | 'awsSessionToken'
  | 'openAiApiKey'
  | 'geminiApiKey'
  | 'openAiNativeApiKey'
  | 'deepSeekApiKey'
  | 'requestyApiKey'
  | 'togetherApiKey'
  | 'fireworksApiKey'
  | 'qwenApiKey'
  | 'doubaoApiKey'
  | 'mistralApiKey'
  | 'liteLlmApiKey'
  | 'authNonce'
  | 'asksageApiKey'
  | 'xaiApiKey'
  | 'sambanovaApiKey'
  | 'defaultApiKey'

export type GlobalStateKey =
  | 'apiProvider'
  | 'apiModelId'
  | 'awsRegion'
  | 'awsUseCrossRegionInference'
  | 'awsBedrockUsePromptCache'
  | 'awsBedrockEndpoint'
  | 'awsEndpointSelected'
  | 'awsProfile'
  | 'awsUseProfile'
  | 'awsBedrockCustomSelected'
  | 'awsBedrockCustomModelBaseId'
  | 'vertexProjectId'
  | 'vertexRegion'
  | 'lastShownAnnouncementId'
  | 'customInstructions'
  | 'taskHistory'
  | 'favoriteTaskList'
  | 'openAiBaseUrl'
  | 'openAiModelId'
  | 'openAiModelInfo'
  | 'openAiHeaders'
  | 'ollamaModelId'
  | 'ollamaBaseUrl'
  | 'ollamaApiOptionsCtxNum'
  | 'lmStudioModelId'
  | 'lmStudioBaseUrl'
  | 'anthropicBaseUrl'
  | 'geminiBaseUrl'
  | 'azureApiVersion'
  | 'openRouterModelId'
  | 'openRouterModelInfo'
  | 'openRouterProviderSorting'
  | 'autoApprovalSettings'
  | 'globalClineRulesToggles'
  | 'browserSettings'
  | 'chatSettings'
  | 'vsCodeLmModelSelector'
  | 'userInfo'
  | 'previousModeApiProvider'
  | 'previousModeModelId'
  | 'previousModeThinkingBudgetTokens'
  | 'previousModeReasoningEffort'
  | 'previousModeVsCodeLmModelSelector'
  | 'previousModeAwsBedrockCustomSelected'
  | 'previousModeAwsBedrockCustomModelBaseId'
  | 'previousModeModelInfo'
  | 'liteLlmBaseUrl'
  | 'liteLlmModelId'
  | 'liteLlmModelInfo'
  | 'fireworksModelId'
  | 'fireworksModelMaxCompletionTokens'
  | 'fireworksModelMaxTokens'
  | 'qwenApiLine'
  | 'requestyModelId'
  | 'requestyModelInfo'
  | 'togetherModelId'
  | 'mcpMarketplaceCatalog'
  | 'telemetrySetting'
  | 'asksageApiUrl'
  | 'thinkingBudgetTokens'
  | 'reasoningEffort'
  | 'planActSeparateModelsSetting'
  | 'favoritedModelIds'
  | 'requestTimeoutMs'
  | 'shellIntegrationTimeout'
  | 'userConfig'
  | 'needProxy'
  | 'proxyConfig'
  | 'modelOptions'
  | 'defaultBaseUrl'
  | 'defaultModelId'
  | 'deepSeekModelId'
  | 'messageFeedbacks'
  | 'userRules'

export type LocalStateKey = 'localClineRulesToggles'
