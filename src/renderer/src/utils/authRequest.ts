// 认证请求功能已屏蔽 - Authentication request functionality disabled
import axios from 'axios'
import config from '@/config'
// import { removeToken } from '@/utils/permission'

// 原有的认证请求配置已屏蔽 - Original authentication request configuration disabled
// const authRequest = axios.create({
//   baseURL: config.api
// })
//
// authRequest.interceptors.request.use(
//   async function (config) {
//     const BearerToken = localStorage.getItem('ctm-token')
//     config.headers['Authorization'] = `Bearer ${BearerToken}`
//     return config
//   },
//   function (error) {
//     return Promise.reject(error)
//   }
// )
//
// authRequest.interceptors.response.use(
//   (res) => {
//     return res.data
//   },
//   function (error) {
//     if (error.response?.status === 401) {
//       const data = error.response.data
//       if (!(data.result && data.result.isLogin)) {
//         removeToken()
//         window.location.hash = '#/login'
//       }
//     }
//     return Promise.reject(error)
//   }
// )

// 屏蔽后的简化请求配置 - Simplified request configuration after disabling
const authRequest = axios.create({
  baseURL: config.api
})

// 移除认证拦截器，使用基础配置 - Remove authentication interceptors, use basic configuration
authRequest.interceptors.request.use(
  async function (config) {
    console.log('认证请求拦截器已屏蔽')
    return config
  },
  function (error) {
    return Promise.reject(error)
  }
)

authRequest.interceptors.response.use(
  function (response) {
    return response
  },
  function (error) {
    console.log('认证响应拦截器已屏蔽')
    return Promise.reject(error)
  }
)

export default authRequest
