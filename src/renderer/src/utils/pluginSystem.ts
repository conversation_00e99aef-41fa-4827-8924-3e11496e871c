/**
 * 插件系统 - 可扩展的第三方插件支持
 * 提供插件加载、管理、通信和生命周期管理
 */

export interface PluginManifest {
  id: string
  name: string
  version: string
  description: string
  author: string
  homepage?: string
  repository?: string
  license: string
  keywords: string[]
  main: string
  dependencies?: Record<string, string>
  permissions: Permission[]
  hooks: HookDefinition[]
  ui?: UIDefinition
  settings?: SettingDefinition[]
}

export interface Permission {
  type: 'terminal' | 'filesystem' | 'network' | 'system' | 'ui' | 'storage'
  scope: 'read' | 'write' | 'execute' | 'all'
  description: string
}

export interface HookDefinition {
  name: string
  type: 'before' | 'after' | 'replace' | 'async'
  target: string
  priority?: number
}

export interface UIDefinition {
  panels?: PanelDefinition[]
  commands?: CommandDefinition[]
  menus?: MenuDefinition[]
  themes?: ThemeDefinition[]
}

export interface PanelDefinition {
  id: string
  title: string
  position: 'left' | 'right' | 'bottom' | 'floating'
  component: string
  icon?: string
  defaultVisible?: boolean
}

export interface CommandDefinition {
  id: string
  title: string
  description: string
  shortcut?: string
  category: string
  handler: string
}

export interface MenuDefinition {
  id: string
  label: string
  position: 'context' | 'main' | 'toolbar'
  items: MenuItem[]
}

export interface MenuItem {
  id: string
  label: string
  action: string
  shortcut?: string
  separator?: boolean
  submenu?: MenuItem[]
}

export interface ThemeDefinition {
  id: string
  name: string
  colors: Record<string, string>
  styles: Record<string, any>
}

export interface SettingDefinition {
  key: string
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect'
  title: string
  description: string
  default: any
  options?: { label: string; value: any }[]
  validation?: {
    required?: boolean
    min?: number
    max?: number
    pattern?: string
  }
}

export interface Plugin {
  manifest: PluginManifest
  instance: any
  enabled: boolean
  loaded: boolean
  error?: string
  settings: Record<string, any>
}

export interface PluginContext {
  terminal: TerminalAPI
  ui: UIAPI
  storage: StorageAPI
  network: NetworkAPI
  filesystem: FilesystemAPI
  events: EventAPI
  utils: UtilsAPI
}

export interface TerminalAPI {
  write(data: string): void
  writeln(data: string): void
  clear(): void
  getSelection(): string
  onData(callback: (data: string) => void): void
  onResize(callback: (cols: number, rows: number) => void): void
  executeCommand(command: string): Promise<string>
}

export interface UIAPI {
  showNotification(message: string, type?: 'info' | 'success' | 'warning' | 'error'): void
  showDialog(options: DialogOptions): Promise<any>
  createPanel(definition: PanelDefinition): void
  removePanel(id: string): void
  addCommand(definition: CommandDefinition): void
  removeCommand(id: string): void
  addMenu(definition: MenuDefinition): void
  removeMenu(id: string): void
}

export interface StorageAPI {
  get(key: string): Promise<any>
  set(key: string, value: any): Promise<void>
  remove(key: string): Promise<void>
  clear(): Promise<void>
  keys(): Promise<string[]>
}

export interface NetworkAPI {
  fetch(url: string, options?: RequestInit): Promise<Response>
  websocket(url: string): WebSocket
}

export interface FilesystemAPI {
  readFile(path: string): Promise<string>
  writeFile(path: string, content: string): Promise<void>
  exists(path: string): Promise<boolean>
  mkdir(path: string): Promise<void>
  readdir(path: string): Promise<string[]>
  stat(path: string): Promise<any>
}

export interface EventAPI {
  on(event: string, callback: Function): void
  off(event: string, callback: Function): void
  emit(event: string, ...args: any[]): void
  once(event: string, callback: Function): void
}

export interface UtilsAPI {
  uuid(): string
  hash(data: string): string
  encrypt(data: string, key: string): string
  decrypt(data: string, key: string): string
  debounce(fn: Function, delay: number): Function
  throttle(fn: Function, delay: number): Function
}

export interface DialogOptions {
  title: string
  content: string
  type?: 'info' | 'confirm' | 'prompt' | 'custom'
  buttons?: { label: string; value: any }[]
  defaultValue?: string
  component?: string
}

export class PluginSystem {
  private plugins = new Map<string, Plugin>()
  private hooks = new Map<string, Function[]>()
  private context: PluginContext
  private eventHandlers = new Map<string, Function[]>()

  constructor() {
    this.context = this.createPluginContext()
    this.initializeBuiltinHooks()
  }

  /**
   * 创建插件上下文
   */
  private createPluginContext(): PluginContext {
    return {
      terminal: this.createTerminalAPI(),
      ui: this.createUIAPI(),
      storage: this.createStorageAPI(),
      network: this.createNetworkAPI(),
      filesystem: this.createFilesystemAPI(),
      events: this.createEventAPI(),
      utils: this.createUtilsAPI()
    }
  }

  /**
   * 加载插件
   */
  async loadPlugin(manifestPath: string): Promise<Plugin> {
    try {
      // 读取插件清单
      const manifestContent = await this.context.filesystem.readFile(manifestPath)
      const manifest: PluginManifest = JSON.parse(manifestContent)

      // 验证插件清单
      this.validateManifest(manifest)

      // 检查权限
      await this.checkPermissions(manifest.permissions)

      // 加载插件代码
      const pluginPath = manifestPath.replace('manifest.json', manifest.main)
      const pluginCode = await this.context.filesystem.readFile(pluginPath)

      // 创建插件实例
      const pluginInstance = this.createPluginInstance(pluginCode, manifest)

      // 创建插件对象
      const plugin: Plugin = {
        manifest,
        instance: pluginInstance,
        enabled: true,
        loaded: true,
        settings: this.getPluginSettings(manifest.id)
      }

      // 注册插件
      this.plugins.set(manifest.id, plugin)

      // 注册钩子
      this.registerPluginHooks(plugin)

      // 初始化插件
      if (pluginInstance.activate) {
        await pluginInstance.activate(this.context)
      }

      this.emit('pluginLoaded', plugin)
      console.log(`[Plugin] 插件已加载: ${manifest.name} v${manifest.version}`)

      return plugin
    } catch (error) {
      console.error('[Plugin] 加载插件失败:', error)
      throw error
    }
  }

  /**
   * 卸载插件
   */
  async unloadPlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error(`插件不存在: ${pluginId}`)
    }

    try {
      // 停用插件
      if (plugin.instance.deactivate) {
        await plugin.instance.deactivate()
      }

      // 移除钩子
      this.unregisterPluginHooks(plugin)

      // 移除UI元素
      this.removePluginUI(plugin)

      // 移除插件
      this.plugins.delete(pluginId)

      this.emit('pluginUnloaded', plugin)
      console.log(`[Plugin] 插件已卸载: ${plugin.manifest.name}`)
    } catch (error) {
      console.error('[Plugin] 卸载插件失败:', error)
      throw error
    }
  }

  /**
   * 启用插件
   */
  async enablePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error(`插件不存在: ${pluginId}`)
    }

    if (plugin.enabled) return

    try {
      plugin.enabled = true

      if (plugin.instance.activate) {
        await plugin.instance.activate(this.context)
      }

      this.registerPluginHooks(plugin)
      this.emit('pluginEnabled', plugin)
    } catch (error) {
      plugin.enabled = false
      console.error('[Plugin] 启用插件失败:', error)
      throw error
    }
  }

  /**
   * 禁用插件
   */
  async disablePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error(`插件不存在: ${pluginId}`)
    }

    if (!plugin.enabled) return

    try {
      plugin.enabled = false

      if (plugin.instance.deactivate) {
        await plugin.instance.deactivate()
      }

      this.unregisterPluginHooks(plugin)
      this.emit('pluginDisabled', plugin)
    } catch (error) {
      console.error('[Plugin] 禁用插件失败:', error)
      throw error
    }
  }

  /**
   * 执行钩子
   */
  async executeHook(hookName: string, ...args: any[]): Promise<any> {
    const hooks = this.hooks.get(hookName) || []
    let result = args[0] // 默认返回第一个参数

    for (const hook of hooks) {
      try {
        const hookResult = await hook(...args)
        if (hookResult !== undefined) {
          result = hookResult
        }
      } catch (error) {
        console.error(`[Plugin] 钩子执行失败 (${hookName}):`, error)
      }
    }

    return result
  }

  /**
   * 验证插件清单
   */
  private validateManifest(manifest: PluginManifest): void {
    const required = ['id', 'name', 'version', 'description', 'author', 'main', 'permissions']

    for (const field of required) {
      if (!manifest[field as keyof PluginManifest]) {
        throw new Error(`插件清单缺少必需字段: ${field}`)
      }
    }

    // 验证版本格式
    if (!/^\d+\.\d+\.\d+/.test(manifest.version)) {
      throw new Error('插件版本格式无效')
    }

    // 验证ID格式
    if (!/^[a-z0-9-_]+$/.test(manifest.id)) {
      throw new Error('插件ID格式无效')
    }
  }

  /**
   * 检查权限
   */
  private async checkPermissions(permissions: Permission[]): Promise<void> {
    // 这里可以实现权限检查逻辑
    // 例如显示权限确认对话框
    for (const permission of permissions) {
      console.log(`[Plugin] 请求权限: ${permission.type} (${permission.scope})`)
    }
  }

  /**
   * 创建插件实例
   */
  private createPluginInstance(code: string, manifest: PluginManifest): any {
    // 创建安全的执行环境
    const sandbox = {
      console,
      setTimeout,
      setInterval,
      clearTimeout,
      clearInterval,
      Promise,
      JSON,
      Date,
      Math,
      RegExp,
      String,
      Number,
      Boolean,
      Array,
      Object,
      Map,
      Set,
      WeakMap,
      WeakSet
    }

    // 执行插件代码
    const func = new Function('exports', 'require', 'module', ...Object.keys(sandbox), code)
    const exports = {}
    const module = { exports }

    func(exports, () => {}, module, ...Object.values(sandbox))

    return module.exports || exports
  }

  /**
   * 注册插件钩子
   */
  private registerPluginHooks(plugin: Plugin): void {
    for (const hookDef of plugin.manifest.hooks) {
      if (!this.hooks.has(hookDef.name)) {
        this.hooks.set(hookDef.name, [])
      }

      const hookHandler = plugin.instance[hookDef.target]
      if (typeof hookHandler === 'function') {
        this.hooks.get(hookDef.name)!.push(hookHandler.bind(plugin.instance))
      }
    }
  }

  /**
   * 移除插件钩子
   */
  private unregisterPluginHooks(plugin: Plugin): void {
    for (const hookDef of plugin.manifest.hooks) {
      const hooks = this.hooks.get(hookDef.name)
      if (hooks) {
        const hookHandler = plugin.instance[hookDef.target]
        if (hookHandler) {
          const index = hooks.indexOf(hookHandler)
          if (index > -1) {
            hooks.splice(index, 1)
          }
        }
      }
    }
  }

  /**
   * 移除插件UI
   */
  private removePluginUI(plugin: Plugin): void {
    if (plugin.manifest.ui) {
      // 移除面板
      if (plugin.manifest.ui.panels) {
        for (const panel of plugin.manifest.ui.panels) {
          this.context.ui.removePanel(panel.id)
        }
      }

      // 移除命令
      if (plugin.manifest.ui.commands) {
        for (const command of plugin.manifest.ui.commands) {
          this.context.ui.removeCommand(command.id)
        }
      }

      // 移除菜单
      if (plugin.manifest.ui.menus) {
        for (const menu of plugin.manifest.ui.menus) {
          this.context.ui.removeMenu(menu.id)
        }
      }
    }
  }

  /**
   * 获取插件设置
   */
  private getPluginSettings(pluginId: string): Record<string, any> {
    // 从存储中读取插件设置
    // 这里返回默认设置
    return {}
  }

  /**
   * 初始化内置钩子
   */
  private initializeBuiltinHooks(): void {
    // 注册一些内置钩子点
    this.hooks.set('terminal.beforeInput', [])
    this.hooks.set('terminal.afterInput', [])
    this.hooks.set('terminal.beforeOutput', [])
    this.hooks.set('terminal.afterOutput', [])
    this.hooks.set('command.beforeExecute', [])
    this.hooks.set('command.afterExecute', [])
    this.hooks.set('session.beforeConnect', [])
    this.hooks.set('session.afterConnect', [])
    this.hooks.set('session.beforeDisconnect', [])
    this.hooks.set('session.afterDisconnect', [])
  }

  /**
   * 创建各种API实现
   */
  private createTerminalAPI(): TerminalAPI {
    return {
      write: (data: string) => {
        // 实现终端写入
        this.emit('terminal.write', data)
      },
      writeln: (data: string) => {
        this.emit('terminal.writeln', data)
      },
      clear: () => {
        this.emit('terminal.clear')
      },
      getSelection: () => {
        // 返回终端选择的文本
        return ''
      },
      onData: (callback: (data: string) => void) => {
        this.on('terminal.data', callback)
      },
      onResize: (callback: (cols: number, rows: number) => void) => {
        this.on('terminal.resize', callback)
      },
      executeCommand: async (command: string) => {
        return new Promise((resolve) => {
          this.emit('terminal.executeCommand', command, resolve)
        })
      }
    }
  }

  private createUIAPI(): UIAPI {
    return {
      showNotification: (message: string, type = 'info') => {
        this.emit('ui.notification', { message, type })
      },
      showDialog: (options: DialogOptions) => {
        return new Promise((resolve) => {
          this.emit('ui.dialog', options, resolve)
        })
      },
      createPanel: (definition: PanelDefinition) => {
        this.emit('ui.createPanel', definition)
      },
      removePanel: (id: string) => {
        this.emit('ui.removePanel', id)
      },
      addCommand: (definition: CommandDefinition) => {
        this.emit('ui.addCommand', definition)
      },
      removeCommand: (id: string) => {
        this.emit('ui.removeCommand', id)
      },
      addMenu: (definition: MenuDefinition) => {
        this.emit('ui.addMenu', definition)
      },
      removeMenu: (id: string) => {
        this.emit('ui.removeMenu', id)
      }
    }
  }

  private createStorageAPI(): StorageAPI {
    return {
      get: async (key: string) => {
        return localStorage.getItem(`plugin_${key}`)
      },
      set: async (key: string, value: any) => {
        localStorage.setItem(`plugin_${key}`, JSON.stringify(value))
      },
      remove: async (key: string) => {
        localStorage.removeItem(`plugin_${key}`)
      },
      clear: async () => {
        const keys = Object.keys(localStorage).filter((k) => k.startsWith('plugin_'))
        keys.forEach((k) => localStorage.removeItem(k))
      },
      keys: async () => {
        return Object.keys(localStorage)
          .filter((k) => k.startsWith('plugin_'))
          .map((k) => k.replace('plugin_', ''))
      }
    }
  }

  private createNetworkAPI(): NetworkAPI {
    return {
      fetch: (url: string, options?: RequestInit) => {
        return fetch(url, options)
      },
      websocket: (url: string) => {
        return new WebSocket(url)
      }
    }
  }

  private createFilesystemAPI(): FilesystemAPI {
    return {
      readFile: async (path: string) => {
        // 实现文件读取（需要与主进程通信）
        return ''
      },
      writeFile: async (path: string, content: string) => {
        // 实现文件写入
      },
      exists: async (path: string) => {
        return false
      },
      mkdir: async (path: string) => {
        // 实现目录创建
      },
      readdir: async (path: string) => {
        return []
      },
      stat: async (path: string) => {
        return {}
      }
    }
  }

  private createEventAPI(): EventAPI {
    return {
      on: (event: string, callback: Function) => {
        this.on(event, callback)
      },
      off: (event: string, callback: Function) => {
        this.off(event, callback)
      },
      emit: (event: string, ...args: any[]) => {
        this.emit(event, ...args)
      },
      once: (event: string, callback: Function) => {
        this.once(event, callback)
      }
    }
  }

  private createUtilsAPI(): UtilsAPI {
    return {
      uuid: () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
          const r = (Math.random() * 16) | 0
          const v = c === 'x' ? r : (r & 0x3) | 0x8
          return v.toString(16)
        })
      },
      hash: (data: string) => {
        // 简单哈希实现
        let hash = 0
        for (let i = 0; i < data.length; i++) {
          const char = data.charCodeAt(i)
          hash = (hash << 5) - hash + char
          hash = hash & hash
        }
        return hash.toString()
      },
      encrypt: (data: string, key: string) => {
        // 简单加密实现（实际应用中应使用更安全的方法）
        return btoa(data)
      },
      decrypt: (data: string, key: string) => {
        return atob(data)
      },
      debounce: (fn: Function, delay: number) => {
        let timeoutId: NodeJS.Timeout
        return (...args: any[]) => {
          clearTimeout(timeoutId)
          timeoutId = setTimeout(() => fn(...args), delay)
        }
      },
      throttle: (fn: Function, delay: number) => {
        let lastCall = 0
        return (...args: any[]) => {
          const now = Date.now()
          if (now - lastCall >= delay) {
            lastCall = now
            fn(...args)
          }
        }
      }
    }
  }

  /**
   * 事件系统
   */
  on(event: string, callback: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event)!.push(callback)
  }

  off(event: string, callback: Function): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      const index = handlers.indexOf(callback)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  once(event: string, callback: Function): void {
    const onceCallback = (...args: any[]) => {
      callback(...args)
      this.off(event, onceCallback)
    }
    this.on(event, onceCallback)
  }

  private emit(event: string, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach((handler) => {
        try {
          handler(...args)
        } catch (error) {
          console.error(`[Plugin] 事件处理器错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 获取所有插件
   */
  getAllPlugins(): Plugin[] {
    return Array.from(this.plugins.values())
  }

  /**
   * 获取插件
   */
  getPlugin(id: string): Plugin | undefined {
    return this.plugins.get(id)
  }

  /**
   * 获取已启用的插件
   */
  getEnabledPlugins(): Plugin[] {
    return Array.from(this.plugins.values()).filter((p) => p.enabled)
  }

  /**
   * 搜索插件
   */
  searchPlugins(query: string): Plugin[] {
    const lowerQuery = query.toLowerCase()
    return Array.from(this.plugins.values()).filter(
      (plugin) =>
        plugin.manifest.name.toLowerCase().includes(lowerQuery) ||
        plugin.manifest.description.toLowerCase().includes(lowerQuery) ||
        plugin.manifest.keywords.some((keyword) => keyword.toLowerCase().includes(lowerQuery))
    )
  }

  /**
   * 更新插件设置
   */
  async updatePluginSettings(pluginId: string, settings: Record<string, any>): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error(`插件不存在: ${pluginId}`)
    }

    plugin.settings = { ...plugin.settings, ...settings }

    // 保存到存储
    await this.context.storage.set(`${pluginId}_settings`, plugin.settings)

    // 通知插件设置已更新
    if (plugin.instance.onSettingsChanged) {
      await plugin.instance.onSettingsChanged(plugin.settings)
    }

    this.emit('pluginSettingsUpdated', plugin)
  }

  /**
   * 获取插件统计信息
   */
  getPluginStats(): {
    total: number
    enabled: number
    disabled: number
    loaded: number
    errors: number
  } {
    const plugins = Array.from(this.plugins.values())
    return {
      total: plugins.length,
      enabled: plugins.filter((p) => p.enabled).length,
      disabled: plugins.filter((p) => !p.enabled).length,
      loaded: plugins.filter((p) => p.loaded).length,
      errors: plugins.filter((p) => p.error).length
    }
  }
}

// 单例实例
export const pluginSystem = new PluginSystem()
