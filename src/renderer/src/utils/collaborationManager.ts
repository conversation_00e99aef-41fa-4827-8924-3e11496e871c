/**
 * 协作管理器 - 会话共享和团队协作
 * 支持实时会话共享、多用户协作、权限控制等功能
 */

export interface CollaborationSession {
  id: string
  name: string
  description: string
  owner: string
  participants: Participant[]
  connectionInfo: ConnectionInfo
  settings: SessionSettings
  createdAt: number
  lastActivity: number
  status: 'active' | 'paused' | 'ended'
}

export interface Participant {
  id: string
  name: string
  email: string
  role: 'owner' | 'admin' | 'editor' | 'viewer'
  status: 'online' | 'offline' | 'away'
  joinedAt: number
  permissions: Permission[]
  cursor?: CursorPosition
}

export interface Permission {
  type: 'read' | 'write' | 'execute' | 'admin'
  scope: 'all' | 'specific'
  resources?: string[]
}

export interface ConnectionInfo {
  host: string
  port: number
  username: string
  protocol: 'ssh' | 'telnet' | 'local'
  encrypted: boolean
}

export interface SessionSettings {
  allowViewers: boolean
  requireApproval: boolean
  recordSession: boolean
  shareClipboard: boolean
  syncCursor: boolean
  maxParticipants: number
  sessionTimeout: number
}

export interface CursorPosition {
  x: number
  y: number
  timestamp: number
}

export interface SessionEvent {
  id: string
  sessionId: string
  type: 'join' | 'leave' | 'input' | 'output' | 'cursor' | 'message' | 'permission'
  userId: string
  data: any
  timestamp: number
}

export interface ChatMessage {
  id: string
  sessionId: string
  userId: string
  userName: string
  content: string
  type: 'text' | 'command' | 'file' | 'system'
  timestamp: number
  replyTo?: string
}

export class CollaborationManager {
  private sessions = new Map<string, CollaborationSession>()
  private currentSession: CollaborationSession | null = null
  private eventHandlers = new Map<string, Function[]>()
  private websocket: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5

  constructor() {
    this.initializeEventHandlers()
  }

  /**
   * 初始化事件处理器
   */
  private initializeEventHandlers(): void {
    // 监听窗口关闭事件
    window.addEventListener('beforeunload', () => {
      this.leaveCurrentSession()
    })

    // 监听网络状态变化
    window.addEventListener('online', () => {
      this.handleNetworkReconnect()
    })

    window.addEventListener('offline', () => {
      this.handleNetworkDisconnect()
    })
  }

  /**
   * 创建协作会话
   */
  async createSession(
    name: string,
    description: string,
    connectionInfo: ConnectionInfo,
    settings: Partial<SessionSettings> = {}
  ): Promise<CollaborationSession> {
    const sessionId = this.generateSessionId()
    
    const session: CollaborationSession = {
      id: sessionId,
      name,
      description,
      owner: this.getCurrentUserId(),
      participants: [
        {
          id: this.getCurrentUserId(),
          name: this.getCurrentUserName(),
          email: this.getCurrentUserEmail(),
          role: 'owner',
          status: 'online',
          joinedAt: Date.now(),
          permissions: [
            { type: 'read', scope: 'all' },
            { type: 'write', scope: 'all' },
            { type: 'execute', scope: 'all' },
            { type: 'admin', scope: 'all' }
          ]
        }
      ],
      connectionInfo,
      settings: {
        allowViewers: true,
        requireApproval: false,
        recordSession: true,
        shareClipboard: false,
        syncCursor: true,
        maxParticipants: 10,
        sessionTimeout: 24 * 60 * 60 * 1000, // 24小时
        ...settings
      },
      createdAt: Date.now(),
      lastActivity: Date.now(),
      status: 'active'
    }

    this.sessions.set(sessionId, session)
    this.currentSession = session

    // 连接到协作服务器
    await this.connectToCollaborationServer(sessionId)

    this.emit('sessionCreated', session)
    return session
  }

  /**
   * 加入协作会话
   */
  async joinSession(sessionId: string, password?: string): Promise<CollaborationSession> {
    try {
      // 连接到协作服务器
      await this.connectToCollaborationServer(sessionId)

      // 发送加入请求
      const joinRequest = {
        type: 'join',
        sessionId,
        userId: this.getCurrentUserId(),
        userName: this.getCurrentUserName(),
        userEmail: this.getCurrentUserEmail(),
        password
      }

      this.sendMessage(joinRequest)

      // 等待服务器响应
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('加入会话超时'))
        }, 10000)

        this.once('joinResponse', (response) => {
          clearTimeout(timeout)
          if (response.success) {
            this.currentSession = response.session
            this.sessions.set(sessionId, response.session)
            resolve(response.session)
          } else {
            reject(new Error(response.error))
          }
        })
      })
    } catch (error) {
      console.error('[Collaboration] 加入会话失败:', error)
      throw error
    }
  }

  /**
   * 离开当前会话
   */
  async leaveCurrentSession(): Promise<void> {
    if (!this.currentSession) return

    try {
      // 发送离开消息
      this.sendMessage({
        type: 'leave',
        sessionId: this.currentSession.id,
        userId: this.getCurrentUserId()
      })

      this.emit('sessionLeft', this.currentSession)
      this.currentSession = null

      // 断开WebSocket连接
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
    } catch (error) {
      console.error('[Collaboration] 离开会话失败:', error)
    }
  }

  /**
   * 发送终端输入
   */
  sendTerminalInput(data: string): void {
    if (!this.currentSession) return

    const event: SessionEvent = {
      id: this.generateEventId(),
      sessionId: this.currentSession.id,
      type: 'input',
      userId: this.getCurrentUserId(),
      data: { input: data },
      timestamp: Date.now()
    }

    this.sendMessage(event)
    this.emit('inputSent', event)
  }

  /**
   * 发送终端输出
   */
  sendTerminalOutput(data: string): void {
    if (!this.currentSession) return

    const event: SessionEvent = {
      id: this.generateEventId(),
      sessionId: this.currentSession.id,
      type: 'output',
      userId: this.getCurrentUserId(),
      data: { output: data },
      timestamp: Date.now()
    }

    this.sendMessage(event)
  }

  /**
   * 更新光标位置
   */
  updateCursorPosition(x: number, y: number): void {
    if (!this.currentSession) return

    const event: SessionEvent = {
      id: this.generateEventId(),
      sessionId: this.currentSession.id,
      type: 'cursor',
      userId: this.getCurrentUserId(),
      data: { x, y },
      timestamp: Date.now()
    }

    this.sendMessage(event)
  }

  /**
   * 发送聊天消息
   */
  sendChatMessage(content: string, type: 'text' | 'command' | 'file' = 'text'): void {
    if (!this.currentSession) return

    const message: ChatMessage = {
      id: this.generateEventId(),
      sessionId: this.currentSession.id,
      userId: this.getCurrentUserId(),
      userName: this.getCurrentUserName(),
      content,
      type,
      timestamp: Date.now()
    }

    this.sendMessage({
      type: 'message',
      sessionId: this.currentSession.id,
      userId: this.getCurrentUserId(),
      data: message,
      timestamp: Date.now()
    })

    this.emit('messageSent', message)
  }

  /**
   * 邀请用户加入会话
   */
  async inviteUser(email: string, role: 'admin' | 'editor' | 'viewer' = 'viewer'): Promise<void> {
    if (!this.currentSession) throw new Error('没有活动会话')

    const invitation = {
      type: 'invite',
      sessionId: this.currentSession.id,
      inviterUserId: this.getCurrentUserId(),
      inviterUserName: this.getCurrentUserName(),
      inviteeEmail: email,
      role,
      sessionName: this.currentSession.name,
      timestamp: Date.now()
    }

    this.sendMessage(invitation)
    this.emit('invitationSent', invitation)
  }

  /**
   * 连接到协作服务器
   */
  private async connectToCollaborationServer(sessionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 这里应该连接到实际的协作服务器
        // 目前使用模拟的WebSocket连接
        const wsUrl = `ws://localhost:8080/collaboration/${sessionId}`
        this.websocket = new WebSocket(wsUrl)

        this.websocket.onopen = () => {
          console.log('[Collaboration] 已连接到协作服务器')
          this.reconnectAttempts = 0
          resolve()
        }

        this.websocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data)
            this.handleServerMessage(message)
          } catch (error) {
            console.error('[Collaboration] 解析服务器消息失败:', error)
          }
        }

        this.websocket.onclose = () => {
          console.log('[Collaboration] 与协作服务器断开连接')
          this.handleDisconnection()
        }

        this.websocket.onerror = (error) => {
          console.error('[Collaboration] WebSocket错误:', error)
          reject(error)
        }

        // 连接超时
        setTimeout(() => {
          if (this.websocket?.readyState !== WebSocket.OPEN) {
            reject(new Error('连接协作服务器超时'))
          }
        }, 10000)
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 处理服务器消息
   */
  private handleServerMessage(message: any): void {
    switch (message.type) {
      case 'joinResponse':
        this.emit('joinResponse', message)
        break
      case 'participantJoined':
        this.handleParticipantJoined(message.data)
        break
      case 'participantLeft':
        this.handleParticipantLeft(message.data)
        break
      case 'terminalInput':
        this.emit('terminalInput', message.data)
        break
      case 'terminalOutput':
        this.emit('terminalOutput', message.data)
        break
      case 'cursorUpdate':
        this.emit('cursorUpdate', message.data)
        break
      case 'chatMessage':
        this.emit('chatMessage', message.data)
        break
      default:
        console.log('[Collaboration] 未知消息类型:', message.type)
    }
  }

  /**
   * 处理参与者加入
   */
  private handleParticipantJoined(participant: Participant): void {
    if (this.currentSession) {
      this.currentSession.participants.push(participant)
      this.emit('participantJoined', participant)
    }
  }

  /**
   * 处理参与者离开
   */
  private handleParticipantLeft(participantId: string): void {
    if (this.currentSession) {
      this.currentSession.participants = this.currentSession.participants.filter(
        p => p.id !== participantId
      )
      this.emit('participantLeft', participantId)
    }
  }

  /**
   * 发送消息到服务器
   */
  private sendMessage(message: any): void {
    if (this.websocket?.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message))
    } else {
      console.warn('[Collaboration] WebSocket未连接，无法发送消息')
    }
  }

  /**
   * 处理断线重连
   */
  private handleDisconnection(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = Math.pow(2, this.reconnectAttempts) * 1000 // 指数退避
      
      setTimeout(() => {
        if (this.currentSession) {
          this.connectToCollaborationServer(this.currentSession.id)
            .catch(error => {
              console.error('[Collaboration] 重连失败:', error)
            })
        }
      }, delay)
    } else {
      this.emit('connectionLost')
    }
  }

  /**
   * 处理网络重连
   */
  private handleNetworkReconnect(): void {
    if (this.currentSession && (!this.websocket || this.websocket.readyState !== WebSocket.OPEN)) {
      this.reconnectAttempts = 0
      this.connectToCollaborationServer(this.currentSession.id)
        .catch(error => {
          console.error('[Collaboration] 网络重连失败:', error)
        })
    }
  }

  /**
   * 处理网络断开
   */
  private handleNetworkDisconnect(): void {
    this.emit('networkDisconnected')
  }

  /**
   * 事件监听
   */
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event)!.push(handler)
  }

  /**
   * 一次性事件监听
   */
  once(event: string, handler: Function): void {
    const onceHandler = (...args: any[]) => {
      handler(...args)
      this.off(event, onceHandler)
    }
    this.on(event, onceHandler)
  }

  /**
   * 移除事件监听
   */
  off(event: string, handler: Function): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          console.error(`[Collaboration] 事件处理器错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return 'event_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 获取当前用户信息（模拟）
   */
  private getCurrentUserId(): string {
    return 'user_' + Math.random().toString(36).substr(2, 9)
  }

  private getCurrentUserName(): string {
    return 'User ' + Math.floor(Math.random() * 1000)
  }

  private getCurrentUserEmail(): string {
    return '<EMAIL>'
  }

  /**
   * 获取当前会话
   */
  getCurrentSession(): CollaborationSession | null {
    return this.currentSession
  }

  /**
   * 获取所有会话
   */
  getAllSessions(): CollaborationSession[] {
    return Array.from(this.sessions.values())
  }
}

// 单例实例
export const collaborationManager = new CollaborationManager()
