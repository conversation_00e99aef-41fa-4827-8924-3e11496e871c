// 权限管理功能已屏蔽 - Permission management functionality disabled
export const CtmTokenKey: string = 'Ctm-Token'

// 登录URL获取功能已屏蔽 - Login URL functionality disabled
// const baseSso = import.meta.env.RENDERER_SSO
// const currentUrl = location.href
// export function getLoginUrl() {
//   return baseSso + currentUrl
// }

// Token移除功能已屏蔽 - Token removal functionality disabled
// export function removeToken() {
//   localStorage.removeItem('ctm-token')
//   localStorage.removeItem('bearer-token')
//   localStorage.removeItem('userInfo')
//   localStorage.removeItem('login-skipped')
// }

// 用户信息设置功能已屏蔽 - User info setting functionality disabled
// export const setUserInfo = (info) => {
//   const userStore = userInfoStore(pinia)
//   userStore.updateInfo(info)
// }

// 用户信息获取功能已屏蔽 - User info getting functionality disabled
// export const getUserInfo = () => {
//   const userStore = userInfoStore(pinia)
//   return userStore.userInfo
// }

// 屏蔽后的占位函数 - Placeholder functions after disabling
export function getLoginUrl() {
  console.log('登录URL获取功能已屏蔽')
  return '#'
}

export function removeToken() {
  console.log('Token移除功能已屏蔽')
}

export const setUserInfo = (info) => {
  console.log('用户信息设置功能已屏蔽', info)
}

export const getUserInfo = () => {
  console.log('用户信息获取功能已屏蔽')
  return null
}
