/**
 * 命令安全检查器
 * 实现实时安全检查、风险评估和安全建议
 */

export interface SecurityRule {
  id: string
  name: string
  pattern: RegExp
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  suggestion: string
  category: 'destructive' | 'privilege' | 'network' | 'system' | 'data'
}

export interface SecurityCheckResult {
  isSecure: boolean
  risks: SecurityRisk[]
  suggestions: string[]
  severity: 'safe' | 'low' | 'medium' | 'high' | 'critical'
}

export interface SecurityRisk {
  rule: SecurityRule
  matches: string[]
  context: string
}

export interface SecurityAuditLog {
  timestamp: number
  command: string
  user: string
  host: string
  risks: SecurityRisk[]
  action: 'allowed' | 'blocked' | 'warned'
}

export class SecurityChecker {
  private rules: SecurityRule[] = []
  private auditLogs: SecurityAuditLog[] = []
  private maxAuditLogs = 1000

  constructor() {
    this.initializeDefaultRules()
  }

  /**
   * 初始化默认安全规则
   */
  private initializeDefaultRules(): void {
    this.rules = [
      // 危险删除操作
      {
        id: 'dangerous-rm',
        name: '危险删除操作',
        pattern: /rm\s+(-rf?|--recursive|--force)\s+[\/~]/,
        severity: 'critical',
        description: '检测到可能删除重要系统文件的命令',
        suggestion: '请确认删除路径，考虑使用 trash 命令或先备份',
        category: 'destructive'
      },
      {
        id: 'rm-root',
        name: '删除根目录',
        pattern: /rm\s+.*\s+\/\s*$/,
        severity: 'critical',
        description: '尝试删除根目录',
        suggestion: '这将删除整个系统，请立即停止',
        category: 'destructive'
      },
      
      // 权限相关
      {
        id: 'chmod-777',
        name: '过度权限开放',
        pattern: /chmod\s+(777|a\+rwx)/,
        severity: 'high',
        description: '设置过度开放的文件权限',
        suggestion: '考虑使用更安全的权限设置，如 755 或 644',
        category: 'privilege'
      },
      {
        id: 'sudo-force',
        name: 'Sudo强制执行',
        pattern: /sudo\s+.*--force/,
        severity: 'high',
        description: '使用sudo强制执行命令',
        suggestion: '强制执行可能跳过安全检查，请谨慎使用',
        category: 'privilege'
      },

      // 网络相关
      {
        id: 'wget-execute',
        name: '下载并执行',
        pattern: /wget.*\|\s*(bash|sh|python|perl)/,
        severity: 'critical',
        description: '从网络下载并直接执行脚本',
        suggestion: '先下载文件，检查内容后再执行',
        category: 'network'
      },
      {
        id: 'curl-execute',
        name: 'Curl执行',
        pattern: /curl.*\|\s*(bash|sh|python|perl)/,
        severity: 'critical',
        description: '使用curl下载并执行脚本',
        suggestion: '先下载文件，检查内容后再执行',
        category: 'network'
      },

      // 系统相关
      {
        id: 'format-disk',
        name: '格式化磁盘',
        pattern: /(mkfs|format)\s+\/dev\//,
        severity: 'critical',
        description: '尝试格式化磁盘',
        suggestion: '这将清除磁盘上的所有数据，请确认操作',
        category: 'destructive'
      },
      {
        id: 'dd-dangerous',
        name: '危险DD操作',
        pattern: /dd\s+.*of=\/dev\//,
        severity: 'critical',
        description: '使用dd命令写入设备',
        suggestion: '这可能覆盖重要数据，请仔细检查参数',
        category: 'destructive'
      },

      // 数据相关
      {
        id: 'database-drop',
        name: '删除数据库',
        pattern: /(DROP\s+DATABASE|DROP\s+TABLE)/i,
        severity: 'high',
        description: '尝试删除数据库或表',
        suggestion: '请确认是否需要备份，并验证删除的对象',
        category: 'data'
      },

      // 恶意软件相关
      {
        id: 'suspicious-base64',
        name: '可疑Base64',
        pattern: /echo\s+[A-Za-z0-9+\/=]{50,}\s*\|\s*base64\s+-d/,
        severity: 'medium',
        description: '检测到可疑的Base64解码操作',
        suggestion: '请检查解码内容是否安全',
        category: 'system'
      }
    ]
  }

  /**
   * 检查命令安全性
   */
  checkCommand(command: string, context?: { user?: string; host?: string }): SecurityCheckResult {
    const risks: SecurityRisk[] = []
    const suggestions: string[] = []

    // 检查每个规则
    for (const rule of this.rules) {
      const matches = command.match(rule.pattern)
      if (matches) {
        risks.push({
          rule,
          matches: matches.slice(1), // 排除完整匹配
          context: command
        })
        suggestions.push(rule.suggestion)
      }
    }

    // 确定整体风险级别
    const severity = this.calculateOverallSeverity(risks)
    const isSecure = severity === 'safe'

    // 记录审计日志
    this.logSecurityCheck(command, context, risks, isSecure ? 'allowed' : 'warned')

    return {
      isSecure,
      risks,
      suggestions: [...new Set(suggestions)], // 去重
      severity
    }
  }

  /**
   * 计算整体风险级别
   */
  private calculateOverallSeverity(risks: SecurityRisk[]): 'safe' | 'low' | 'medium' | 'high' | 'critical' {
    if (risks.length === 0) return 'safe'

    const severityLevels = { low: 1, medium: 2, high: 3, critical: 4 }
    const maxSeverity = Math.max(...risks.map(risk => severityLevels[risk.rule.severity]))

    const severityMap = { 1: 'low', 2: 'medium', 3: 'high', 4: 'critical' } as const
    return severityMap[maxSeverity as keyof typeof severityMap]
  }

  /**
   * 记录安全检查日志
   */
  private logSecurityCheck(
    command: string,
    context: { user?: string; host?: string } = {},
    risks: SecurityRisk[],
    action: 'allowed' | 'blocked' | 'warned'
  ): void {
    const log: SecurityAuditLog = {
      timestamp: Date.now(),
      command,
      user: context.user || 'unknown',
      host: context.host || 'unknown',
      risks,
      action
    }

    this.auditLogs.push(log)

    // 保持日志数量限制
    if (this.auditLogs.length > this.maxAuditLogs) {
      this.auditLogs.shift()
    }
  }

  /**
   * 添加自定义规则
   */
  addRule(rule: SecurityRule): void {
    this.rules.push(rule)
  }

  /**
   * 移除规则
   */
  removeRule(ruleId: string): boolean {
    const index = this.rules.findIndex(rule => rule.id === ruleId)
    if (index > -1) {
      this.rules.splice(index, 1)
      return true
    }
    return false
  }

  /**
   * 获取所有规则
   */
  getRules(): SecurityRule[] {
    return [...this.rules]
  }

  /**
   * 获取审计日志
   */
  getAuditLogs(limit?: number): SecurityAuditLog[] {
    const logs = [...this.auditLogs].reverse() // 最新的在前
    return limit ? logs.slice(0, limit) : logs
  }

  /**
   * 清空审计日志
   */
  clearAuditLogs(): void {
    this.auditLogs = []
  }

  /**
   * 导出审计日志
   */
  exportAuditLogs(): string {
    return JSON.stringify(this.auditLogs, null, 2)
  }

  /**
   * 获取风险统计
   */
  getRiskStatistics(): {
    totalChecks: number
    risksByCategory: Record<string, number>
    risksBySeverity: Record<string, number>
    recentRisks: SecurityRisk[]
  } {
    const risksByCategory: Record<string, number> = {}
    const risksBySeverity: Record<string, number> = {}
    const recentRisks: SecurityRisk[] = []

    // 统计最近的风险
    const recentLogs = this.auditLogs.slice(-100)
    for (const log of recentLogs) {
      for (const risk of log.risks) {
        // 按类别统计
        risksByCategory[risk.rule.category] = (risksByCategory[risk.rule.category] || 0) + 1
        
        // 按严重程度统计
        risksBySeverity[risk.rule.severity] = (risksBySeverity[risk.rule.severity] || 0) + 1
        
        recentRisks.push(risk)
      }
    }

    return {
      totalChecks: this.auditLogs.length,
      risksByCategory,
      risksBySeverity,
      recentRisks: recentRisks.slice(-10) // 最近10个风险
    }
  }

  /**
   * 检查命令是否应该被阻止
   */
  shouldBlockCommand(command: string, context?: { user?: string; host?: string }): boolean {
    const result = this.checkCommand(command, context)
    return result.severity === 'critical'
  }

  /**
   * 获取命令建议
   */
  getCommandSuggestions(command: string): string[] {
    const suggestions: string[] = []

    // 基于常见错误模式提供建议
    if (command.includes('rm ') && !command.includes('-i')) {
      suggestions.push('考虑添加 -i 参数进行交互式确认')
    }

    if (command.includes('chmod ') && command.includes('777')) {
      suggestions.push('考虑使用更安全的权限，如 755 (rwxr-xr-x) 或 644 (rw-r--r--)')
    }

    if (command.includes('sudo ') && command.includes('curl')) {
      suggestions.push('避免使用sudo执行网络下载的脚本')
    }

    return suggestions
  }
}

// 单例实例
export const securityChecker = new SecurityChecker()
