import { defineStore } from 'pinia'

/**
 * 获取浏览器环境下的模拟设备信息
 */
const getBrowserDeviceInfo = () => {
  // 生成基于浏览器指纹的模拟 MAC 地址
  const generateMockMac = () => {
    const userAgent = navigator.userAgent
    const screen = `${window.screen.width}x${window.screen.height}`
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const fingerprint = userAgent + screen + timezone

    // 简单哈希生成模拟 MAC
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // 转换为32位整数
    }

    // 转换为 MAC 地址格式
    const hex = Math.abs(hash).toString(16).padStart(12, '0').slice(0, 12)
    return hex.match(/.{2}/g)?.join(':') || '00:00:00:00:00:00'
  }

  return {
    ip: '127.0.0.1', // 浏览器环境默认本地IP
    macAddress: generateMockMac()
  }
}

export const useDeviceStore = defineStore('device', {
  state: () => {
    // 在浏览器环境下初始化默认设备信息
    const browserInfo = typeof window !== 'undefined' && !window.api ? getBrowserDeviceInfo() : { ip: '', macAddress: '' }

    return {
      ip: browserInfo.ip,
      macAddress: browserInfo.macAddress
    }
  },
  getters: {
    getDeviceIp: (state): string => {
      if (state?.ip) return state.ip

      // 如果状态中没有IP，尝试获取浏览器环境信息
      if (typeof window !== 'undefined' && !window.api) {
        return getBrowserDeviceInfo().ip
      }

      return 'Unknown'
    },
    getMacAddress: (state): string => {
      if (state?.macAddress) return state.macAddress

      // 如果状态中没有MAC地址，尝试获取浏览器环境信息
      if (typeof window !== 'undefined' && !window.api) {
        return getBrowserDeviceInfo().macAddress
      }

      return 'Unknown'
    }
  },
  actions: {
    setDeviceIp(ipStr) {
      this.ip = ipStr
    },
    setMacAddress(macAddressStr) {
      this.macAddress = macAddressStr
    },
    /**
     * 初始化设备信息
     * 在浏览器环境下自动获取模拟设备信息
     */
    initDeviceInfo() {
      if (typeof window !== 'undefined' && !window.api) {
        const browserInfo = getBrowserDeviceInfo()
        this.setDeviceIp(browserInfo.ip)
        this.setMacAddress(browserInfo.macAddress)
      }
    }
  }
})
