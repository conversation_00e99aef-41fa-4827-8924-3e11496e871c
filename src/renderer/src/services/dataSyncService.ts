import { userConfigStore } from './userConfigStoreService'

/**
 * 数据同步服务 - 在渲染进程中管理数据同步的启动和停止
 */
export class DataSyncService {
  private static instance: DataSyncService | null = null
  private isInitialized = false
  private apiWaitTimeout = 10000 // 10秒超时
  private apiCheckInterval = 100 // 100ms检查间隔

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    // Private constructor for singleton pattern
  }

  static getInstance(): DataSyncService {
    if (!DataSyncService.instance) {
      DataSyncService.instance = new DataSyncService()
    }
    return DataSyncService.instance
  }

  /**
   * 初始化数据同步服务（同步版本，会阻塞调用者）
   * 在用户登录后调用，检查用户配置并决定是否启动数据同步
   * 只有正常登录的用户才会启用数据同步，guest用户跳过
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('数据同步服务已初始化，跳过重复初始化')
      return
    }

    try {
      console.log('初始化数据同步服务...')

      // 首先检查API是否可用
      if (!this.isApiAvailable()) {
        console.warn('数据同步API不可用，跳过初始化')
        return
      }

      // 获取用户配置
      const userConfig = await userConfigStore.getConfig()

      if (!userConfig) {
        console.log('无法获取用户配置，跳过数据同步初始化')
        return
      }

      // 检查数据同步是否启用
      const isDataSyncEnabled = userConfig.dataSync === 'enabled'
      console.log(`用户数据同步配置: ${isDataSyncEnabled ? '启用' : '禁用'}`)

      if (isDataSyncEnabled) {
        const success = await this.enableDataSync()
        if (!success) {
          console.warn('数据同步启用失败，但不影响应用正常运行')
        }
      } else {
        console.log('数据同步已禁用，不启动同步服务')
      }

      this.isInitialized = true
      console.log('数据同步服务初始化完成')
    } catch (error) {
      console.error('数据同步服务初始化失败:', error)
      // 即使初始化失败，也标记为已初始化，避免重复尝试
      this.isInitialized = true
    }
  }

  /**
   * 启用数据同步
   */
  async enableDataSync(): Promise<boolean> {
    try {
      console.log('启用数据同步...')

      // 等待API可用
      const apiAvailable = await this.waitForApi()
      if (!apiAvailable) {
        console.error('数据同步API不可用: API初始化超时')
        return false
      }

      const result = await window.api.setDataSyncEnabled(true)

      if (result?.success) {
        console.log('数据同步已成功启用，后台同步任务正在进行中...')
        return true
      } else {
        console.error('启用数据同步失败:', result?.error || '未知错误')
        return false
      }
    } catch (error) {
      console.error('启用数据同步时发生错误:', error)
      return false
    }
  }

  /**
   * 禁用数据同步
   */
  async disableDataSync(): Promise<boolean> {
    try {
      console.log('禁用数据同步...')

      // 等待API可用
      const apiAvailable = await this.waitForApi()
      if (!apiAvailable) {
        console.error('数据同步API不可用: API初始化超时')
        return false
      }

      const result = await window.api.setDataSyncEnabled(false)

      if (result?.success) {
        console.log('数据同步已成功禁用')
        return true
      } else {
        console.error('禁用数据同步失败:', result?.error || '未知错误')
        return false
      }
    } catch (error) {
      console.error('禁用数据同步时发生错误:', error)
      return false
    }
  }

  /**
   * 重置初始化状态（用于用户切换等场景）
   */
  reset(): void {
    this.isInitialized = false
    console.log('数据同步服务状态已重置')
  }

  /**
   * 检查是否已初始化
   */
  getInitializationStatus(): boolean {
    return this.isInitialized
  }

  /**
   * 等待API可用
   * @returns Promise<boolean> API是否可用
   */
  private async waitForApi(): Promise<boolean> {
    const startTime = Date.now()

    while (Date.now() - startTime < this.apiWaitTimeout) {
      if (this.isApiAvailable()) {
        return true
      }

      // 等待一段时间后再次检查
      await new Promise((resolve) => setTimeout(resolve, this.apiCheckInterval))
    }

    // 超时后记录详细的错误信息
    const status = this.getApiStatus()
    console.error('API等待超时:', status)
    return false
  }

  /**
   * 检查数据同步API是否可用
   */
  private isApiAvailable(): boolean {
    return typeof window !== 'undefined' && window.api && typeof window.api.setDataSyncEnabled === 'function'
  }

  /**
   * 获取API可用性状态
   */
  public getApiStatus(): { available: boolean; reason?: string; details?: any } {
    if (typeof window === 'undefined') {
      return { available: false, reason: 'window 对象不可用' }
    }

    if (!window.api) {
      return {
        available: false,
        reason: 'window.api 未定义',
        details: {
          windowExists: typeof window !== 'undefined',
          preloadLoaded: !!window.electron
        }
      }
    }

    if (typeof window.api.setDataSyncEnabled !== 'function') {
      return {
        available: false,
        reason: 'setDataSyncEnabled 方法不存在',
        details: {
          apiKeys: Object.keys(window.api || {}),
          setDataSyncEnabledType: typeof window.api.setDataSyncEnabled
        }
      }
    }

    return { available: true }
  }
}

// 导出单例实例
export const dataSyncService = DataSyncService.getInstance()
