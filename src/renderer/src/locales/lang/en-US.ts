export default {
  common: {
    language: 'English',
    workspace: 'WorkSpace',
    files: 'Files',
    keychain: 'Keychain',
    extensions: 'Extensions',
    ai: 'AI',
    user: 'User',
    setting: 'Setting',
    notice: 'Notice',
    logout: 'Logout',
    login: 'Login',
    userInfo: 'User Info',
    userConfig: 'Setting',
    alias: '<PERSON><PERSON> Config',
    assetConfig: 'Asset Config',
    keyChainConfig: 'Key Chain',
    search: 'Search',
    connect: 'Connect',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    create: 'Create',
    cancel: 'Cancel',
    done: 'Done',
    remove: 'Remove',
    noData: 'No Data',
    close: 'Close',
    closeOther: 'Close Others',
    closeAll: 'Close All',
    copy: 'Copy',
    paste: 'Paste',
    clear: 'Clear',
    copyWithShortcut: 'Copy',
    pasteWithShortcut: 'Paste',
    disconnect: 'Disconnect',
    reconnect: 'Reconnect',
    newTerminal: 'New Terminal',
    closeTerminal: 'Close Terminal',
    splitRight: 'Split Right',
    splitDown: 'Split Down',
    clone: 'Clone',
    clearTerm: 'Clear Screen',
    shrotenName: 'Shorten The Host Name',
    fontsize: 'Fontsize',
    largen: 'Largen',
    smaller: 'Smaller',
    globalExecOn: 'Global Execution (On)',
    globalExec: 'Global Execution',
    syncInputOn: 'Synchronous Input (On)',
    syncInput: 'Synchronous Input',
    allExecuted: 'All Executed',
    pleaseLoginFirst: 'Please login first',
    select: 'Select',
    rightArrowKey: 'Right arrow to suggestion',
    reset: 'Reset',
    confirm: 'Confirm',
    ok: 'OK',
    quickCommandOn: 'Quick Command (On) ',
    quickCommand: 'Quick Command',
    fileManager: 'File Manager',
    add: 'Add',
    all: 'All',
    refresh: 'Refresh',
    fullscreen: 'Fullscreen',
    exitFullscreen: 'Exit Fullscreen',
    editFile: 'Edit File:',
    newFile: 'New File:',
    saveSuccess: 'Save successful',
    saveFailed: 'Save failed',
    saveError: 'Save error',
    saveConfirmTitle: 'Save Changes',
    saveConfirmContent: 'Do you want to save changes to {filePath}?',
    pleaseInputLabel: 'Please input label name',
    pleaseInputPrivateKey: 'Please input private key',
    localhost: 'Localhost',
    favoriteBar: 'Favorites',
    executeCommandToAllWindows: 'Execute command to all windows',
    reloadAliasDataFailed: 'Failed to reload alias data',
    personalAssetFavoriteError: 'Personal asset favorite error',
    organizationAssetFavoriteError: 'Organization asset favorite error',
    updateOrganizationAssetFavoriteMethodNotFound: 'window.api.updateOrganizationAssetFavorite method not found!',
    favoriteStatusUpdateFailed: 'Favorite status update failed',
    updateOrganizationAssetFavoriteError: 'updateOrganizationAssetFavorite error',
    refreshFailed: 'Refresh failed',
    clipboardReadFailed: 'Failed to read clipboard content',
    getUserInfoFailed: 'Failed to get user info',
    updateTerminalStatusError: 'Error updating terminal status',
    sendTerminalStatusError: 'Error sending terminal status',
    getCursorPositionFailed: 'Failed to get cursor position',
    permissionDenied: 'Permission denied',
    timeoutGettingAssetInfo: 'Timeout getting asset information',
    errorGettingAssetInfo: 'Error getting asset information',
    unknownError: 'Unknown error',
    badRequest: 'Bad request',
    unauthorized: 'Unauthorized',
    forbidden: 'Forbidden',
    notFound: 'Not found',
    internalServerError: 'Internal server error',
    badGateway: 'Bad gateway',
    serviceUnavailable: 'Service unavailable'
  },
  term: {
    welcome: 'Welcome to use Chaterm',
    searchPlaceholder: 'Search terminal content...',
    searchPrevious: 'Previous (Shift+Enter)',
    searchNext: 'Next (Enter)'
  },
  // 登录相关翻译已屏蔽 - Login related translations disabled
  // login: {
  //   enterprise: 'Enterprise',
  //   personal: 'Personal',
  //   contact: 'Contact us',
  //   welcome: 'Welcome to use',
  //   title: 'Chaterm',
  //   loginByUser: 'Login with account',
  //   loginByEmail: 'Login with email',
  //   login: 'Login',
  //   loggingIn: 'Logging in...',
  //   skip: "Don't want to sign in right now?",
  //   skipLogin: 'Skip for now',
  //   applyTag: 'No account ? ',
  //   apply: 'Apply',
  //   pleaseInputUsername: 'Please input username',
  //   pleaseInputPassword: 'Please input password',
  //   pleaseInputEmail: 'Please input email',
  //   pleaseInputCode: 'Please input code',
  //   invalidEmail: 'Please input a valid email address',
  //   usernameTooltip: 'Please input your username',
  //   passwordTooltip: 'Please input your password',
  //   retryAfter: 'Retry after {seconds} seconds',
  //   getCode: 'Send code',
  //   pleaseInputEmailAndCode: 'Please input email and verification code',
  //   codeSent: 'Verification code sent',
  //   codeSendFailed: 'Failed to send verification code',
  //   loginFailed: 'Login failed',
  //   externalLoginFailed: 'Failed to start external login',
  //   loginProcessFailed: 'Login process failed',
  //   databaseInitFailed: 'Database initialization failed',
  //   initializationFailed: 'Initialization failed, please try again',
  //   routeNavigationFailed: 'Navigation failed, please try again',
  //   operationFailed: 'Operation failed, please try again',
  //   recordLoginLogFailed: 'Failed to record login log',
  //   recordLoginFailedLogFailed: 'Failed to record login failure log',
  //   guestDatabaseInitFailed: 'Guest database initialization failed',
  //   externalLoginIPDetectionError: 'External login IP detection error',
  //   loginPageIPDetectionError: 'Login page IP detection error',
  //   handleProtocolUrlFailed: 'Failed to handle protocol URL',
  //   routeJumpFailed: 'Route navigation failed',
  //   skipLoginHandleFailed: 'Skip login handling failed',
  //   startExternalLoginFailed: 'Failed to start external login',
  //   databaseInitializationFailed: 'Database initialization failed',
  //   loginHandleFailed: 'Login handling failed',
  //   authCallbackDetected: 'Authentication callback URL detected',
  //   linuxPlatformHandleAuth: 'Handling authentication callback on Linux platform'
  // },
  login: {
    // 登录功能已屏蔽，保留基本字段以避免错误 - Login functionality disabled, keep basic fields to avoid errors
    welcome: 'Welcome to use',
    title: 'Chaterm',
    login: 'Functionality Disabled',
    skip: 'Functionality Disabled',
    skipLogin: 'Enter App'
  },
  workspace: {
    workspace: 'Workspace',
    personal: 'Personal',
    searchHost: 'Search host'
  },
  header: {
    title: 'Chaterm Intelligent Terminal'
  },
  user: {
    autoCompleteStatus: 'Auto completion',
    quickVimStatus: 'Quick vim',
    commonVimStatus: 'Common vim',
    aliasStatus: 'Global alias',
    highlightStatus: 'Highlight',
    fontSize: 'Font size',
    fontFamily: 'Font family',
    cursorStyle: 'Cursor type',
    scrollBack: 'Number of scroll rollback lines',
    terminalType: 'Specify terminal type',
    install: 'Installed',
    installing: 'Installing',
    notInstall: 'Not Installed',
    uninstall: 'Uninstall',
    uninstalling: 'Uninstalling',
    baseSetting: 'Base setting',
    terminalSetting: 'Terminal setting',
    ai: 'AI',
    keychain: 'Keychain',
    textEditor: 'Text editor',
    visualVimEditor: 'Visual Vim Editor',
    fileManagerPlugin: 'File manager plugin',
    fileManagerPluginDescribe: 'Open the file manager by right clicking with the mouse',
    cursorStyleBlock: 'Block',
    cursorStyleBar: 'Bar',
    cursorStyleUnderline: 'Underline',
    mouseEvent: 'Mouse Event',
    middleMouseEvent: 'Middle Mouse Event',
    rightMouseEvent: 'Right Mouse Event',
    pasteClipboard: 'Paste Clipboard',
    showContextMenu: 'Show Context Menu',
    none: 'None',
    terminalBackground: 'Terminal Background',
    terminalBackgroundType: 'Background Type',
    terminalBackgroundDefault: 'Default',
    terminalBackgroundColor: 'Solid Color',
    terminalBackgroundImage: 'Image Background',
    terminalBackgroundColorPicker: 'Background Color',
    terminalBackgroundImageUrl: 'Background Image URL',
    terminalBackgroundOpacity: 'Background Opacity',
    terminalBackgroundImageUpload: 'Upload Image',
    terminalBackgroundImageUrlPlaceholder: 'Enter image URL or upload image',
    themeColors: 'App Theme Colors',
    primaryColor: 'Primary Color',
    accentColor: 'Accent Color',
    backgroundColor: 'Background Color',
    surfaceColor: 'Surface Color',
    textColor: 'Text Color',
    borderColor: 'Border Color',
    resetToDefault: 'Reset to Default',
    applyTheme: 'Apply Theme',
    themeColorsApplied: 'Theme colors applied',
    watermark: 'Watermark',
    watermarkDescribe: 'Show the watermark on the terminal',
    watermarkOpen: 'Open',
    watermarkClose: 'Close',
    language: 'Language',
    theme: 'Theme',
    themeDark: 'Dark',
    themeLight: 'Light',
    themeAuto: 'Auto',
    telemetry: 'Telemetry',
    telemetryEnabled: 'Enabled',
    telemetryDisabled: 'Disabled',
    telemetryDescription:
      'Help improve Chaterm by sending anonymous usage data and error reports. We never send any code, prompt content, or personal information. For more information, please see our <a href="../../../../../PRIVACY.md" target="_blank" rel="noopener noreferrer">privacy policy</a>.',
    telemetryDescriptionText:
      'Help improve Chaterm by sending anonymous usage data and error reports. We never send any code, prompt content, or personal information. For more information, please see our',
    privacyPolicy: 'privacy policy',
    enterprise: 'Enterprise',
    personal: 'Personal',
    name: 'Name',
    email: 'Email',
    mobile: 'Mobile',
    organization: 'Organization',
    ip: 'IP Address',
    macAddress: 'MAC Address',
    general: 'General',
    extensions: 'Extensions',
    about: 'About',
    privacy: 'Privacy',
    secretRedaction: 'Secret Redaction',
    secretRedactionDescription:
      'Secret Redaction attempts to automatically redact secrets and sensitive information in your AI dialog output, including passwords, IP addresses, API keys, and PII.',
    secretRedactionEnabled: 'Enabled',
    secretRedactionDisabled: 'Disabled',
    supportedPatterns: 'Supported Regex Patterns',
    ipv4Address: 'IPv4 Address',
    ipv6Address: 'IPv6 Address',
    slackAppToken: 'Slack App Token',
    phoneNumber: 'Phone Number',
    awsAccessId: 'AWS Access ID',
    googleApiKey: 'Google API Key',
    googleOAuthId: 'Google OAuth ID',
    githubClassicPersonalAccessToken: 'GitHub Classic Personal Access Token',
    githubFineGrainedPersonalAccessToken: 'GitHub Fine Grained Personal Access Token',
    githubOAuthAccessToken: 'GitHub OAuth Access Token',
    githubUserToServerToken: 'GitHub User to Server Token',
    githubServerToServerToken: 'GitHub Server to Server Token',
    stripeKey: 'Stripe Key',
    firebaseAuthDomain: 'Firebase Auth Domain',
    jsonWebToken: 'JSON Web Token',
    openaiApiKey: 'OpenAI API Key',
    anthropicApiKey: 'Anthropic API Key',
    fireworksApiKey: 'Fireworks API Key',
    dataSync: 'Data Sync',
    dataSyncDescription: 'Data synchronization is designed to synchronize the user-configured assets, keys, and other information across devices.',
    dataSyncEnabled: 'Enabled',
    dataSyncDisabled: 'Disabled',
    shortcuts: 'Shortcuts',
    shortcutSettings: 'Shortcut Settings',
    shortcutDescription: 'Customize application shortcuts',
    rules: 'Rules',
    userRules: 'User Rules',
    userRulesDescription: 'Manage your custom user rules and preferences',
    addRule: 'Add Rule',
    noRulesYet: 'No Rules Yet',
    noRulesDescription: 'Add rules and preferences for Agent',
    rulePlaceholder: 'Style request, response language, tone...',
    shortcutAction: 'Action',
    shortcutKey: 'Shortcut',
    shortcutModify: 'Modify',
    shortcutReset: 'Reset',
    shortcutSave: 'Save',
    shortcutCancel: 'Cancel',
    shortcutConflict: 'Shortcut Conflict',
    shortcutConflictMessage: 'This shortcut is already in use, please choose another',
    shortcutInvalid: 'Invalid Shortcut',
    shortcutInvalidMessage: 'Please enter a valid shortcut combination',
    shortcutSaveSuccess: 'Shortcut saved successfully',
    shortcutSaveFailed: 'Failed to save shortcut',
    shortcutResetSuccess: 'Shortcut reset successfully',
    shortcutPressKeys: 'Press shortcut keys',
    shortcutRecording: 'Recording...',
    shortcutClickToModify: 'Click to modify',
    model: 'Model',
    enableExtendedThinking: 'Enable extended thinking',
    enableExtendedThinkingDescribe: 'Higher budgets may allow you to achieve more comprehensive and nuanced reasoning',
    autoApproval: 'Auto Approval',
    autoApprovalDescribe: 'Allow Agent to run tools without asking for confirmation',
    features: 'Features',
    enableCheckpoints: 'Enable Checkpoints',
    enableCheckpointsDescribe: 'Enables extension to save checkpoints of workspace throughout the task',
    openAIReasoningEffort: 'OpenAI Reasoning Effort',
    openAIReasoningEffortLow: 'Low',
    openAIReasoningEffortMedium: 'Medium',
    openAIReasoningEffortHigh: 'High',
    proxySettings: 'Proxy Settings',
    enableProxy: 'Enable Proxy',
    proxyType: 'Proxy Protocol',
    proxyHost: 'Host Name',
    noProxyAdd: 'No proxy added yet',
    proxyName: 'Proxy Name',
    pleaseInputProxyName: 'Please enter the proxy configuration name',
    pleaseInputOtherProxyName: 'The proxy configuration name already exists, please use a different name',
    pleaseInputProxyHost: 'Please enter the proxy host address',
    errorProxyPort: 'The port range should be between 1-65535',
    proxyPort: 'Port Number',
    enableProxyIdentity: 'Proxy Authentication',
    proxyUsername: 'Username',
    proxyPassword: 'Password',
    shellIntegrationTimeout: 'Shell integration timeout (seconds)',
    shellIntegrationTimeoutPh: 'Enter timeout in seconds',
    shellIntegrationTimeoutDescribe: 'Set how long to wait for shell integration to activate',
    terminal: 'Terminal',
    apiConfiguration: 'API Configuration',
    apiProvider: 'API Provider',
    apiProviderDescribe:
      'Authenticate by either providing the keys above or use the default AWS credential providers, i.e. ~/.aws/credentials or environment variables. These credentials are only used locally to make API requests from this client.',
    awsAccessKey: 'AWS Access Key',
    awsAccessKeyPh: 'Enter Access Key',
    awsSecretKey: 'AWS Secret Key',
    awsSecretKeyPh: 'Enter Secret Key',
    awsSessionToken: 'AWS Session Token',
    awsSessionTokenPh: 'Enter Session Token',
    awsRegion: 'AWS Region',
    awsRegionPh: 'Select a region...',
    awsEndpointSelected: 'Use custom VPC endpoint',
    awsBedrockEndpointPh: 'Enter VPC Endpoint URL (optional)',
    awsUseCrossRegionInference: 'Use cross-region inference',
    chatSettings: 'Chat Mode',
    liteLlmBaseUrl: 'Base URL',
    liteLlmBaseUrlPh: 'Enter Base URL',
    liteLlmApiKey: 'API Key',
    liteLlmApiKeyPh: 'Enter API Key',
    liteLlmApiKeyDescribe: 'This key is stored locally and only used to make API requests from this client.',
    customInstructions: 'Custom Instructions',
    customInstructionsPh: `These instructions are added to the end of the system prompt sent with every request. e.g. Always respond in Chinese-simplified`,
    deepSeekApiKey: `DeepSeek API Key`,
    deepSeekApiKeyPh: `Enter API Key...`,
    deepSeekApiKeyDescribe: `This key is stored locally and only used to make API requests from this client.`,
    openAiBaseUrl: 'OpenAI Base URL',
    openAiBaseUrlPh: 'Enter Base URL',
    openAiApiKey: 'OpenAI API Key',
    openAiApiKeyPh: 'Enter API Key',
    openAiApiKeyDescribe: 'This key is stored locally and only used to make API requests from this client.',
    checkModelConfigFailMessage: 'Missing configuration parameters',
    checkModelConfigFailDescription: 'Please fill in the necessary model configuration, Setting -> Models -> Add Model -> API Configuration',
    checkSuccessMessage: 'Connection successful',
    checkSuccessDescription: 'API key is valid',
    checkFailMessage: 'Connection failed',
    checkFailDescriptionDefault: 'unknown error',
    checkFailDescriptionMain: 'Unable to connect to the main process',
    models: 'Models',
    modelNames: 'Model Names',
    aiPreferences: 'AI Preferences',
    addModel: 'Add Model',
    addModelExistError: 'A model with this name already exists',
    addModelSuccess: 'Model added successfully',
    billing: 'Billing Usage',
    subscription: 'Subscription Type',
    expires: 'Expiration Date',
    ratio: 'Usage Ratio',
    budgetResetAt: 'Next Reset Time',
    sshAgentSettings: 'SSH Agent Settiong',
    noKeyAdd: 'No key added yet',
    remove: 'Remove',
    comment: 'Comment',
    fingerprint: 'Fingerprint',
    addSuccess: 'add Success',
    addFailed: 'Add Failed',
    type: 'Type',
    loadConfigFailed: 'Failed to load configuration',
    loadConfigFailedDescription: 'Default configuration will be used',
    telemetryUpdateFailed: 'Failed to update telemetry settings',
    telemetryUpdateFailedDescription: 'Please try again later',
    error: 'Error',
    saveConfigFailedDescription: 'Failed to save configuration',
    themeSwitchFailed: 'Failed to switch theme',
    themeSwitchFailedDescription: 'Please try again later',
    saveAliasStatusFailed: 'Failed to save alias status',
    saveBedrockConfigFailed: 'Failed to save Bedrock configuration',
    saveLiteLlmConfigFailed: 'Failed to save LiteLLM configuration',
    saveDeepSeekConfigFailed: 'Failed to save DeepSeek configuration',
    saveOpenAiConfigFailed: 'Failed to save OpenAI configuration',
    // Ollama
    ollamaBaseUrl: 'Ollama Base URL',
    ollamaBaseUrlPh: 'Enter Ollama service URL, e.g.: http://localhost:11434',
    ollamaBaseUrlDescribe: 'Ollama local service URL, typically http://localhost:11434',
    ollamaApiKey: 'Ollama API Key',
    ollamaApiKeyPh: 'Enter API Key (optional)',
    ollamaApiKeyDescribe: 'Ollama API key (optional), usually not required for local deployment',
    saveOllamaConfigFailed: 'Failed to save Ollama configuration',
    // Volcengine
    volcengineBaseUrl: 'Volcengine Base URL',
    volcengineBaseUrlPh: 'Enter Volcengine API URL',
    volcengineApiKey: 'Volcengine API Key',
    volcengineApiKeyPh: 'Enter Volcengine API Key',
    volcengineApiKeyDescribe: 'This key is stored locally and only used to make API requests from this client.',
    saveVolcengineConfigFailed: 'Failed to save Volcengine configuration',
    // Alibaba Cloud
    alibabaBaseUrl: 'Alibaba Cloud Base URL',
    alibabaBaseUrlPh: 'Enter Alibaba Cloud API URL',
    alibabaApiKey: 'Alibaba Cloud API Key',
    alibabaApiKeyPh: 'Enter Alibaba Cloud API Key',
    alibabaApiKeyDescribe: 'This key is stored locally and only used to make API requests from this client.',
    saveAlibabaConfigFailed: 'Failed to save Alibaba Cloud configuration',
    // GLM
    glmBaseUrl: 'GLM Base URL',
    glmBaseUrlPh: 'Enter GLM API URL',
    glmApiKey: 'GLM API Key',
    glmApiKeyPh: 'Enter GLM API Key',
    glmApiKeyDescribe: 'This key is stored locally and only used to make API requests from this client.',
    saveGlmConfigFailed: 'Failed to save GLM configuration',
    // Gemini
    geminiBaseUrl: 'Gemini Base URL',
    geminiBaseUrlPh: 'Enter Gemini API URL',
    geminiApiKey: 'Gemini API Key',
    geminiApiKeyPh: 'Enter Gemini API Key',
    geminiApiKeyDescribe: 'This key is stored locally and only used to make API requests from this client.',
    saveGeminiConfigFailed: 'Failed to save Gemini configuration'
  },
  extensions: {
    extensions: 'Extensions',
    alias: 'Alias',
    aliasDescription: 'Global alias config',
    fuzzySearch: 'Fuzzy Search',
    command: 'Command',
    action: 'Action',
    success: 'Success',
    error: 'Error',
    errorDescription: 'Creation failed!',
    errorNetWork: 'Network request exception',
    warning: 'Warning',
    missingAliasCommand: 'Missing alias or command!',
    aliasAlreadyExists: 'Alias already exists！',
    addCommand: 'Add Command'
  },
  commandDialog: {
    placeholder: 'Command instructions',
    generating: 'Generating command...',
    submit: 'Generate'
  },
  shortcuts: {
    actions: {
      openSettings: 'Open Settings',
      toggleLeftSidebar: 'Toggle Left Sidebar',
      toggleRightSidebar: 'Toggle Right Sidebar',
      sendOrToggleAi: 'Send to AI / Toggle AI Sidebar',
      switchToNextTab: 'Next Tab',
      switchToPrevTab: 'Previous Tab',
      switchToSpecificTab: 'Switch to specific Tab[1...9]',
      openCommandDialog: 'Open AI Command Generator',
      newTab: 'New Tab',
      openFileManager: 'Open File Manager',
      clearTerminal: 'Clear Terminal',
      fontSizeIncrease: 'Font Size Increase',
      fontSizeDecrease: 'Font Size Decrease'
    }
  },
  personal: {
    host: 'Asset Management',
    newHost: 'New Host',
    keyChain: 'KeyChain',
    address: 'Connection Address',
    general: 'General',
    group: 'Group',
    accountPassword: 'Account Password',
    key: 'Key',
    username: 'Username',
    password: 'Password',
    remoteHost: 'Remote host',
    port: 'Port',
    verificationMethod: 'Verification Method',
    alias: 'Alias',
    pleaseInputRemoteHost: 'Please input remote host',
    pleaseInputPort: 'Please input port',
    pleaseInputUsername: 'Please input username',
    pleaseInputPassword: 'Please input password',
    pleaseSelectKeychain: 'Please select keychain',
    pleaseInputAlias: 'Please input alias',
    pleaseSelectGroup: 'Please select group',
    pleaseSelectSshProxy: 'Please Select SSH Proxy',
    proxyConfig: 'SSH Proxy',
    personal: 'Personal',
    enterprise: 'Enterprise',
    editHost: 'Edit Host',
    saveAsset: 'Save',
    createAsset: 'Create',
    deleteConfirm: 'Delete Confirmation',
    deleteConfirmContent: 'Are you sure you want to delete asset "{name}"?',
    deleteSuccess: 'Asset {name} deleted successfully',
    deleteFailure: 'Delete failed',
    deleteError: 'Delete error: {error}',
    createSuccess: 'Asset created successfully',
    saveSuccess: 'Save successful',
    saveError: 'Save error',
    favoriteUpdateSuccess: 'Asset {name} favorite status updated',
    favoriteUpdateFailure: 'Failed to update favorite status',
    favoriteUpdateError: 'Error updating favorite status',
    defaultGroup: 'Hosts',
    hostType: 'ssh',
    personalAsset: 'Personal Asset',
    enterpriseAsset: 'Enterprise Asset',
    organizationTip: 'Connect to enterprise assets via Jumpserver',
    refreshAssets: 'Refresh Assets',
    refreshingAssets: 'Refreshing assets...',
    refreshSuccess: 'Assets refreshed successfully',
    refreshError: 'Failed to refresh assets',
    validationRemoteHostRequired: 'Remote host cannot be empty',
    validationPortRequired: 'Port cannot be empty',
    validationUsernameRequired: 'Username cannot be empty',
    validationPasswordRequired: 'Password cannot be empty',
    validationKeychainRequired: 'Keychain cannot be empty',
    validationEnterpriseRequiredFields: 'Enterprise asset remote host, port, username, password or keychain cannot be empty',
    import: 'Import',
    export: 'Export',
    importSuccess: 'Import successful',
    importError: 'Import failed',
    importFormatError: 'File format error, please select a valid JSON file',
    importNoData: 'No data to import',
    importSuccessCount: 'Successfully imported {count} assets',
    importErrorCount: 'Failed to import {count} assets',
    exportSuccess: 'Successfully exported {count} assets',
    exportError: 'Export failed',
    exportNoData: 'No assets to export',
    importHelp: 'Import format help',
    importFormatTitle: 'Asset Import Format Guide',
    importFormatGuide: 'Import Format Guide:',
    importFormatStep1: '1. File must be in JSON format',
    importFormatStep2: '2. Data must be in array format',
    importFormatStep3: '3. Each asset object must contain the following fields:',
    importFormatRequired: 'Required',
    importFormatOptional: 'Optional',
    importFormatUsername: 'Username (required)',
    importFormatIp: 'IP address (required)',
    importFormatPassword: 'Password (optional, can be empty for key-based auth)',
    importFormatLabel: 'Label (optional, defaults to IP address)',
    importFormatGroup: 'Group name (optional, defaults to "Hosts")',
    importFormatAuthType: 'Auth type (optional, "password" or "keyBased")',
    importFormatKeyChain: 'Keychain ID (optional, used for key-based auth)',
    importFormatPort: 'Port (optional, defaults to 22)',
    importFormatAssetType: 'Asset type (optional, "person" or "organization")',
    importFormatExample: 'Example:',
    comment: 'Comment',
    addComment: 'Add Comment',
    editComment: 'Edit Comment',
    commentPlaceholder: 'Please enter comment information',
    commentSaved: 'Comment saved successfully',
    commentSaveFailed: 'Comment save failed',
    commentSaveError: 'Comment save error',
    customFolders: 'Custom Folders',
    folder: 'Folder',
    createFolder: 'Create Folder',
    editFolder: 'Edit Folder',
    deleteFolder: 'Delete Folder',
    folderName: 'Folder Name',
    folderDescription: 'Folder Description',
    pleaseInputFolderName: 'Please input folder name',
    pleaseInputFolderDescription: 'Please input folder description (optional)',
    folderCreated: 'Folder created successfully',
    folderUpdated: 'Folder updated successfully',
    folderDeleted: 'Folder deleted successfully',
    folderCreateFailed: 'Folder create failed',
    folderUpdateFailed: 'Folder update failed',
    folderDeleteFailed: 'Folder delete failed',
    moveToFolder: 'Move to Folder',
    removeFromFolder: 'Remove from Folder',
    assetMoved: 'Asset moved successfully',
    assetRemoved: 'Asset removed successfully',
    assetMoveFailed: 'Asset move failed',
    assetRemoveFailed: 'Asset remove failed',
    selectFolder: 'Select Folder',
    noFolders: 'No custom folders',
    createFolderFirst: 'Please create a folder first',
    folderDeleteConfirm: 'Delete folder confirmation',
    folderDeleteConfirmContent: 'Are you sure you want to delete folder "{name}"?',
    folderDeleteConfirmWithAssets:
      'Are you sure you want to delete folder "{name}"? This folder contains {count} assets, and after deletion, the assets will return to their original position.',
    missingAssetId: 'Missing asset ID'
  },
  ai: {
    welcome: 'What can I do for you in the terminal?',
    loginPrompt: 'Login to use AI features, new users can use for free for two weeks',
    searchHost: 'Search by IP',
    noMatchingHosts: 'No matching hosts',
    copy: 'Copy',
    run: 'Run',
    reject: 'Reject',
    cancel: 'Cancel',
    resume: 'Resume',
    agentMessage: 'Command query,troubleshoot errors,handle tasks,anything',
    cmdMessage: 'Work on explicitly opened terminal',
    chatMessage: 'Ask, learn, brainstorm ',
    localhost: 'Localhost',
    newChat: 'New Chat',
    showChatHistory: 'Show History',
    closeAiSidebar: 'Close AI Sidebar',
    addHost: 'Add Host',
    processing: 'processing...',
    searchHistoryPH: 'Please Input',
    loading: 'loading...',
    loadMore: 'load more',
    copyToClipboard: 'Copy to clipboard',
    retry: 'Retry',
    taskCompleted: 'Task Completed',
    newTask: 'Start New Task',
    codePreview: 'Code Preview ({lines} lines)',
    enterCustomOption: 'Enter your answer...',
    submit: 'Submit',
    // Voice input related
    startVoiceInput: 'Start Voice Input',
    stopRecording: 'Stop Voice Recording',
    startRecording: 'Start Voice Recording',
    recordingTooShort: 'Recording Too Short',
    recordingTooShortDesc: 'Please record longer voice content',
    recordingFailed: 'Recording Failed',
    recordingErrorDesc: 'An error occurred during recording',
    recordingTimeLimit: 'Recording Time Limit Reached',
    recordingTimeLimitDesc: 'Recording stopped automatically',
    microphonePermissionDenied: 'Microphone permission denied, please allow microphone access in settings',
    microphoneNotFound: 'Microphone device not found',
    microphoneInUse: 'Microphone is being used by another application',
    voiceInputFailed: 'Voice Input Failed',
    recordingStopped: 'Recording Stopped',
    processingVoice: 'Processing voice recognition...',
    audioFileTooLarge: 'Audio file too large, please record shorter voice (recommended no more than 60 seconds, maximum {maxSize}MB)',
    audioFormatConversion: 'Audio Format Conversion',
    formatConversionDesc: 'Unsupported format detected, converted to {format} format',
    voiceRecognitionSuccess: 'Voice Recognition Successful',
    recognitionResult: 'Recognition result: {text}',
    voiceRecognitionEmpty: 'Voice Recognition Result Empty',
    recognitionEmptyDesc: 'Please re-record or check voice content',
    voiceRecognitionFailed: 'Voice Transcription Failed',
    voiceRecognitionServiceUnavailable: 'Voice transcription service temporarily unavailable',
    startRecordingDesc: 'Please start speaking, recording up to 60 seconds',
    recordingFormat: 'Format used',
    stopAudioTracks: 'Stop all audio tracks',
    startRecordingNote: 'Start recording, collect data every 100ms',
    websocketConnectionFailed: 'WebSocket connection failed, please check backend service',
    uploadFile: 'Upload File',
    fileUploadSuccess: 'File Upload Success',
    fileUploadSuccessDesc: 'File "{fileName}" content has been read',
    fileTooLarge: 'File Too Large',
    fileTooLargeDesc: 'File size cannot exceed 1MB, please select a smaller file',
    fileReadFailed: 'File Read Failed',
    fileReadErrorDesc: 'Unable to read file content, please check if the file format is correct',
    fileContent: 'File Content ({fileName})',
    sendContentError: 'Send Content Error',
    sendContentEmpty: 'Send content is empty, please enter content',
    getAssetInfoFailed: 'Failed to get current asset connection information',
    pleaseConnectAsset: 'Please establish asset connection first',
    operationFailed: 'Operation Failed',
    noOperableMessage: 'No operable message',
    commandCopied: 'Command Copied',
    commandCopiedToClipboard: 'Command copied to clipboard',
    executionCompleted: 'Execution completed',
    commandExecutionFailed: 'Command execution failed',
    localCommandExecutionError: 'Local command execution error',
    cannotExecuteCommand: 'Cannot execute command',
    wrongServerWindow:
      'Current window is not the server window for executing commands.\nTarget server: {targetServer}\nCurrent window: {currentWindow}\n\nPlease switch to the correct server window before executing commands.',
    nonTerminalWindow: 'Non-terminal window',
    commandExecutedOnLocalhost: 'Command executed on localhost',
    timeoutGettingAssetInfo: 'Timeout getting asset information',
    guestModeModelTip: 'Using default models in guest mode, login to configure more AI providers'
  },
  keyChain: {
    newKey: 'New Key',
    editKey: 'Edit Key',
    saveKey: 'Save Key',
    keyDrop: 'Drag and drop private key file here to import',
    createKey: 'Create Key',
    deleteConfirm: 'Delete Confirmation',
    deleteConfirmContent: 'Are you sure you want to delete key "{name}"?',
    privateKey: 'Private Key',
    publicKey: 'Public Key',
    passphrase: 'Passphrase',
    alias: 'Alias',
    key: 'Key',
    pleaseInput: 'Please Input',
    name: 'Name',
    type: 'Type: ',
    deleteSuccess: 'Key {name} deleted successfully',
    deleteFailure: 'Delete failed',
    deleteError: 'Delete error: {error}',
    createSuccess: 'Key created successfully',
    saveSuccess: 'Save successful',
    saveError: 'Save error',
    getKeyListFailed: 'Failed to get key list',
    createError: 'Creation error',
    missingKeyId: 'Missing key ID',
    keyFileImported: 'Key file imported',
    readFileFailed: 'Failed to read file'
  },
  userInfo: {
    enterprise: 'Enterprise User',
    personal: 'Personal User',
    vip: 'VIP User',
    name: 'Name',
    username: 'Username',
    mobile: 'Mobile',
    email: 'Email',
    organization: 'Organization',
    ip: 'IP Address',
    macAddress: 'Mac Address',
    password: 'Password',
    pleaseInputName: 'Please input name',
    pleaseInputUsername: 'Please input username',
    pleaseInputMobile: 'Please input mobile number',
    emailPlaceholder: 'Please enter email address',
    pleaseInputNewPassword: 'Please input new password',
    nameRequired: 'Name is required',
    nameTooLong: 'Name length cannot exceed 20 characters',
    usernameLengthError: 'Username length must be between 6-20 characters',
    usernameFormatError: 'Username can only contain letters, numbers and underscores',
    mobileInvalid: 'Please enter a valid mobile number',
    passwordLengthError: 'Password length cannot be less than 6 characters',
    passwordStrengthError: 'Password strength must be at least weak',
    passwordStrength: 'Password Strength',
    passwordStrengthWeak: 'Weak',
    passwordStrengthMedium: 'Medium',
    passwordStrengthStrong: 'Strong',
    updateSuccess: 'Update successful',
    updateFailed: 'Update failed',
    passwordResetSuccess: 'Password reset successfully',
    passwordResetFailed: 'Failed to reset password',
    edit: 'Edit',
    save: 'Save',
    cancel: 'Cancel',
    resetPassword: 'Reset Password',
    expirationTime: 'Expiration Time',
    enterpriseCertification: 'Enterprise Certification',
    guestModeNoPasswordChange: 'Cannot change password in guest mode'
  },
  update: {
    available: 'A new version is available',
    update: 'Update',
    later: 'Later',
    downloading: 'Downloading update',
    complete: 'Download complete, install now?',
    install: 'Install',
    clickUpdate: 'Click Restart to update'
  },
  files: {
    name: 'Name',
    permissions: 'Permissions',
    size: 'Size',
    modifyDate: 'Modified Date',
    uploadDirectory: 'Upload Directory',
    uploadFile: 'Upload File',
    rollback: 'Back',
    moveTo: 'Move To',
    cpTo: 'Copy To',
    originPath: 'Original Path',
    targetPath: 'Target Path',
    pathInputTips: 'Please enter the target path, e.g., /root/tmp',
    noDirTips: 'No subdirectories available',
    dirEdit: 'Edit',
    conflictTips: 'A file with the same name already exists in the target directory',
    newFileName: 'New File Name',
    rename: 'rename',
    overwrite: 'Overwrite',
    overwriteTips: 'Do you want to overwrite or save with a new name?',
    file: 'File',
    exists: 'already exists in',
    deleteFileTips: 'Are you sure you want to delete the following files?',
    deleting: 'Deleting...',
    deleteSuccess: 'Delete Successful',
    deleteFailed: 'Delete Failed',
    deleteError: 'Delete Error',
    modifySuccess: 'Modification Successful',
    modifyFailed: 'Modification Failed',
    modifyError: 'Modification Error',
    downloading: 'Downloading...',
    downloadSuccess: 'Download Successful',
    downloadFailed: 'Download Failed',
    downloadError: 'Download Error',
    uploading: 'Uploading...',
    uploadSuccess: 'Upload Successful',
    uploadFailed: 'Upload Failed',
    uploadError: 'Upload Error',
    copyFileSuccess: 'File Copied Successfully',
    copyFileFailed: 'File Copy Failed',
    copyFileError: 'File Copy Error',
    moveFileSuccess: 'File Moved Successfully',
    moveFileFailed: 'File Move Failed',
    moveFileError: 'File Move Error',
    modifyFilePermissionsSuccess: 'File Permissions Modified Successfully',
    modifyFilePermissionsFailed: 'Failed to Modify File Permissions',
    modifyFilePermissionsError: 'File Permissions Modification Error',
    read: 'Read',
    write: 'Write',
    exec: 'exec',
    applyToSubdirectories: 'Apply to Subdirectories',
    publicGroup: 'Public Group',
    userGroups: 'User Groups',
    owner: 'Owner',
    permissionSettings: 'Permission Settings',
    delete: 'Delete',
    move: 'Move',
    copy: 'Copy',
    more: 'More',
    download: 'Download',
    doubleClickToOpen: 'Double Click to Open',
    sftpConnectFailed: 'SFTP connection failed',
    pleaseInputNewFileName: 'Please input new file name'
  },
  about: {
    version: 'Version',
    checkUpdate: 'Check Update',
    latestVersion: 'Latest Version',
    downLoadUpdate: 'Download Update',
    downloading: 'Downloading',
    checkUpdateError: 'Check update failed',
    checking: 'Checking',
    install: 'Install'
  },
  mfa: {
    title: 'Two-Factor Authentication',
    verificationCode: 'Verification Code',
    verificationCodeError: 'Verification code error',
    pleaseInputVerificationCode: 'Please input verification code',
    remainingTime: 'Remaining time',
    confirm: 'Confirm',
    cancel: 'Cancel',
    setupGlobalListeners: 'Setting up global MFA listeners'
  },
  ssh: {
    disconnected: 'Disconnected.',
    pressEnterToReconnect: 'Press Enter to reconnect...',
    disconnectError: 'Disconnect error: {message}',
    unknownError: 'Unknown error',
    connectionFailed: 'Connection failed: {message}',
    connectionError: 'Connection error: {message}',
    shellStartFailed: 'Shell start failed: {message}',
    shellError: 'Shell error: {message}',
    connectionClosed: 'Connection closed.',
    disconnectedFromHost: 'Disconnected from remote host({host}) at {date}',
    pressEnterToReconnectEn: 'Press Enter to reconnect...',
    connectingTo: 'Connecting to {ip}',
    welcomeMessage: 'Welcome to RS - Chaterm',
    reconnecting: 'Reconnecting...',
    terminalConnectionError: 'Connection error. Please check if the terminal server is running.'
  },
  quickCommand: {
    scriptName: 'Script Name',
    scriptContent: 'Please enter script content...',
    scriptSyntaxHelp: '📖 Script Syntax Help',
    basicCommands: '⚡ Basic Commands:',
    basicCommandsDesc: 'One command per line, executed in sequence',
    delayCommand: '⏰ Delay Command:',
    delayCommandDesc: 'e.g.:',
    specialKeys: '⌨️ Special Keys:',
    exampleScript: '💡 Example Script',
    copy: 'Copy',
    copied: 'Copied',
    copyFailed: 'Copy failed',
    seconds: 'seconds'
  }
}
