import { HttpsProxyAgent } from 'https-proxy-agent'
import { HttpProxyAgent } from 'http-proxy-agent'
import { SocksProxyAgent } from 'socks-proxy-agent'
import type { Agent } from 'http'
import { ProxyConfig } from '@shared/Proxy'
import net from 'net'
import tls from 'tls'
import { SocksClient } from 'socks'

// Create proxy
export function createProxyAgent(config?: ProxyConfig): Agent | undefined {
  if (!config) return undefined
  const { type, host, port, enableProxyIdentity, username, password } = config
  const auth = enableProxyIdentity && username && password ? `${username}:${password}@` : ''
  const url = `${type!.toLowerCase()}://${auth}${host}:${port}`

  switch (type) {
    case 'HTTP':
      return new HttpProxyAgent(url)
    case 'HTTPS':
      return new HttpsProxyAgent(url)
    case 'SOCKS4':
    case 'SOCKS5':
      return new SocksProxyAgent(url)
    default:
      throw new Error(`Unsupported proxy type: ${type}`)
  }
}

// Validate proxy connectivity
export async function checkProxyConnectivity(config?: ProxyConfig): Promise<void> {
  if (!config) return

  const { type, host, port } = config

  try {
    // Validate proxy configuration
    if (!type) {
      throw new Error('Proxy type is required')
    }
    if (!host || !port) {
      throw new Error('Proxy host and port are required')
    }
    if (port < 1 || port > 65535) {
      throw new Error(`Invalid proxy port: ${port}. Port must be between 1 and 65535`)
    }

    console.log(`[Proxy] Checking connectivity to ${type} proxy at ${host}:${port}`)

    switch (type) {
      case 'HTTP':
        await checkTcpConnection(host, port)
        break

      case 'HTTPS':
        await checkTlsConnection(host, port)
        break

      case 'SOCKS4':
      case 'SOCKS5':
        await checkSocksConnection(config)
        break

      default:
        throw new Error(`Unsupported proxy type: ${type}. Supported types: HTTP, HTTPS, SOCKS4, SOCKS5`)
    }

    console.log(`[Proxy] Successfully connected to ${type} proxy at ${host}:${port}`)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error(`[Proxy] Connection failed for ${type} proxy at ${host}:${port}:`, errorMessage)
    throw new Error(`Proxy connectivity check failed: ${errorMessage}`)
  }
}

function checkTcpConnection(host: string, port: number): Promise<void> {
  return new Promise((resolve, reject) => {
    const socket = net.connect(port, host)
    socket.setTimeout(5000) // Increased timeout to 5 seconds

    socket.once('connect', () => {
      console.log(`[Proxy] TCP connection to ${host}:${port} established`)
      socket.destroy()
      resolve()
    })

    socket.once('error', (error) => {
      console.error(`[Proxy] TCP connection error to ${host}:${port}:`, error.message)
      socket.destroy()
      reject(new Error(`TCP connection failed: ${error.message}`))
    })

    socket.once('timeout', () => {
      console.error(`[Proxy] TCP connection timeout to ${host}:${port}`)
      socket.destroy()
      reject(new Error(`TCP connection timed out after 5 seconds`))
    })
  })
}

function checkTlsConnection(host: string, port: number): Promise<void> {
  return new Promise((resolve, reject) => {
    const socket = tls.connect(port, host, {
      rejectUnauthorized: false,
      timeout: 5000 // Set connection timeout
    })
    socket.setTimeout(5000) // Increased timeout to 5 seconds

    socket.once('secureConnect', () => {
      console.log(`[Proxy] TLS connection to ${host}:${port} established`)
      socket.end()
      resolve()
    })

    socket.once('error', (error) => {
      console.error(`[Proxy] TLS connection error to ${host}:${port}:`, error.message)
      socket.destroy()
      reject(new Error(`TLS connection failed: ${error.message}`))
    })

    socket.once('timeout', () => {
      console.error(`[Proxy] TLS connection timeout to ${host}:${port}`)
      socket.destroy()
      reject(new Error(`TLS handshake timed out after 5 seconds`))
    })
  })
}

async function checkSocksConnection(config: ProxyConfig): Promise<void> {
  const { host, port, type, enableProxyIdentity, username, password } = config

  try {
    const proxyType = type === 'SOCKS4' ? 4 : (5 as 4 | 5)

    // Validate SOCKS-specific configuration
    if (type === 'SOCKS4' && enableProxyIdentity && password) {
      throw new Error('SOCKS4 does not support password authentication')
    }

    const options = {
      proxy: {
        host: host!,
        port: port!,
        type: proxyType, // 4 or 5
        userId: enableProxyIdentity ? username : undefined,
        password: enableProxyIdentity ? password : undefined
      },
      command: 'connect' as const,
      destination: {
        host: 'example.com',
        port: 80
      },
      timeout: 5000 // Increased timeout to 5 seconds
    }

    console.log(`[Proxy] Testing SOCKS${proxyType} connection to ${host}:${port}`)

    const info = await SocksClient.createConnection(options)
    console.log(`[Proxy] SOCKS${proxyType} connection to ${host}:${port} established`)
    info.socket.end()
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : String(err)
    console.error(`[Proxy] SOCKS connection error to ${host}:${port}:`, errorMessage)
    throw new Error(`SOCKS proxy connection failed: ${errorMessage}`)
  }
}
