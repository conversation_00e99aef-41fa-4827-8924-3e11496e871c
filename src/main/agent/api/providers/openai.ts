import { Anthropic } from '@anthropic-ai/sdk'
import OpenAI, { AzureOpenAI } from 'openai'
import { withRetry } from '../retry'
import { ApiHandlerOptions, azureOpenAiDefaultApiVersion, ModelInfo, openAiModelInfoSaneDefaults } from '@shared/api'
import { Api<PERSON>and<PERSON> } from '../index'
import { convertToOpenAiMessages } from '../transform/openai-format'
import type { ApiStream } from '../transform/stream'
import { convertToR1Format } from '../transform/r1-format'
import type { ChatCompletionReasoningEffort } from 'openai/resources/chat/completions'
import { checkProxyConnectivity, createProxyAgent } from './proxy'
import type { Agent } from 'http'

export class OpenAiHandler implements ApiHandler {
  private options: ApiHandlerOptions
  private client: OpenAI

  constructor(options: ApiHandlerOptions) {
    this.options = options
    // Azure API shape slightly differs from the core API shape: https://github.com/openai/openai-node?tab=readme-ov-file#microsoft-azure-openai
    // Use azureApiVersion to determine if this is an Azure endpoint, since the URL may not always contain 'azure.com'
    let httpAgent: Agent | undefined = undefined
    if (this.options.needProxy !== false) {
      const proxyConfig = this.options.proxyConfig
      httpAgent = createProxyAgent(proxyConfig)
    }
    const timeoutMs = this.options.requestTimeoutMs || 20000

    if (
      this.options.azureApiVersion ||
      (this.options.openAiBaseUrl?.toLowerCase().includes('azure.com') && !this.options.openAiModelId?.toLowerCase().includes('deepseek'))
    ) {
      this.client = new AzureOpenAI({
        baseURL: this.options.openAiBaseUrl,
        apiKey: this.options.openAiApiKey,
        apiVersion: this.options.azureApiVersion || azureOpenAiDefaultApiVersion,
        defaultHeaders: this.options.openAiHeaders,
        httpAgent: httpAgent,
        timeout: timeoutMs
      })
    } else {
      this.client = new OpenAI({
        baseURL: this.options.openAiBaseUrl,
        apiKey: this.options.openAiApiKey,
        defaultHeaders: this.options.openAiHeaders,
        httpAgent: httpAgent,
        timeout: timeoutMs
      })
    }
  }

  @withRetry()
  async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
    const modelId = this.options.openAiModelId ?? ''
    const isDeepseekReasoner = modelId.includes('deepseek-reasoner')
    const isR1FormatRequired = this.options.openAiModelInfo?.isR1FormatRequired ?? false
    const isReasoningModelFamily = modelId.includes('o1') || modelId.includes('o3') || modelId.includes('o4')

    let openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [{ role: 'system', content: systemPrompt }, ...convertToOpenAiMessages(messages)]
    let temperature: number | undefined = this.options.openAiModelInfo?.temperature ?? openAiModelInfoSaneDefaults.temperature
    let reasoningEffort: ChatCompletionReasoningEffort | undefined = undefined
    let maxTokens: number | undefined

    if (this.options.openAiModelInfo?.maxTokens && this.options.openAiModelInfo.maxTokens > 0) {
      maxTokens = Number(this.options.openAiModelInfo.maxTokens)
    } else {
      maxTokens = undefined
    }

    if (isDeepseekReasoner || isR1FormatRequired) {
      openAiMessages = convertToR1Format([{ role: 'user', content: systemPrompt }, ...messages])
    }

    if (isReasoningModelFamily) {
      openAiMessages = [{ role: 'developer', content: systemPrompt }, ...convertToOpenAiMessages(messages)]
      temperature = undefined // does not support temperature
      reasoningEffort = (this.options.o3MiniReasoningEffort as ChatCompletionReasoningEffort) || 'medium'
    }

    const stream = await this.client.chat.completions.create({
      model: modelId,
      messages: openAiMessages,
      temperature,
      max_tokens: maxTokens,
      reasoning_effort: reasoningEffort,
      stream: true,
      stream_options: { include_usage: true }
    })
    for await (const chunk of stream) {
      const delta = chunk.choices[0]?.delta
      if (delta?.content) {
        yield {
          type: 'text',
          text: delta.content
        }
      }

      if (delta && 'reasoning_content' in delta && delta.reasoning_content) {
        yield {
          type: 'reasoning',
          reasoning: (delta.reasoning_content as string | undefined) || ''
        }
      }

      if (chunk.usage) {
        yield {
          type: 'usage',
          inputTokens: chunk.usage.prompt_tokens || 0,
          outputTokens: chunk.usage.completion_tokens || 0
        }
      }
    }
  }

  getModel(): { id: string; info: ModelInfo } {
    return {
      id: this.options.openAiModelId ?? '',
      info: this.options.openAiModelInfo ?? openAiModelInfoSaneDefaults
    }
  }

  async validateApiKey(): Promise<{ isValid: boolean; error?: string }> {
    try {
      console.log('[OpenAI] Starting API key validation')

      // Validate required configuration
      if (!this.options.openAiApiKey) {
        throw new Error('OpenAI API key is required')
      }

      const modelId = this.options.openAiModelId || 'gpt-3.5-turbo'
      console.log(`[OpenAI] Using model: ${modelId}`)

      // Validate Azure-specific configuration
      if (this.options.azureApiVersion) {
        console.log('[OpenAI] Validating Azure OpenAI configuration')
        if (!this.options.azureResourceName) {
          throw new Error('Azure resource name is required for Azure OpenAI')
        }
        if (!this.options.azureDeploymentName) {
          throw new Error('Azure deployment name is required for Azure OpenAI')
        }
      }

      // Validate custom base URL
      if (this.options.openAiBaseUrl) {
        console.log(`[OpenAI] Using custom base URL: ${this.options.openAiBaseUrl}`)
      }

      // Validate proxy if needed
      if (this.options.needProxy) {
        console.log('[OpenAI] Validating proxy connectivity')
        try {
          await checkProxyConnectivity(this.options.proxyConfig!)
          console.log('[OpenAI] Proxy connectivity validated successfully')
        } catch (proxyError) {
          console.error('[OpenAI] Proxy validation failed:', proxyError)
          throw new Error(`Proxy validation failed: ${proxyError instanceof Error ? proxyError.message : String(proxyError)}`)
        }
      }

      console.log('[OpenAI] Testing API connection')

      const response = await this.client.chat.completions.create({
        model: modelId,
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 1
      })

      if (!response || !response.choices || response.choices.length === 0) {
        throw new Error('Invalid response format: no choices returned')
      }

      console.log('[OpenAI] API key validation successful')
      return { isValid: true }
    } catch (error: any) {
      console.error('[OpenAI] Configuration validation failed:', {
        message: error?.message,
        status: error?.status,
        code: error?.code,
        type: error?.type,
        stack: error?.stack
      })

      // Categorize error types for better user feedback
      let errorMessage = 'Validation failed'

      if (error?.status === 401) {
        errorMessage = 'Invalid API key or unauthorized access'
      } else if (error?.status === 403) {
        errorMessage = 'Access forbidden - check API key permissions'
      } else if (error?.status === 404) {
        errorMessage = 'Model not found or invalid deployment name'
      } else if (error?.status === 429) {
        errorMessage = 'Rate limit exceeded - please try again later'
      } else if (error?.status >= 500) {
        errorMessage = 'Server error - please try again later'
      } else if (error?.code === 'ECONNREFUSED' || error?.code === 'ENOTFOUND') {
        errorMessage = 'Connection failed - check base URL and network connectivity'
      } else if (error?.code === 'ETIMEDOUT') {
        errorMessage = 'Connection timeout - check network connectivity'
      } else if (error?.message) {
        errorMessage = error.message
      }

      return {
        isValid: false,
        error: errorMessage
      }
    }
  }
}
