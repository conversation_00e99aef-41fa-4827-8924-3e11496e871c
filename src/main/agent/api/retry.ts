interface RetryOptions {
  maxRetries?: number
  baseDelay?: number
  maxDelay?: number
  retryAllErrors?: boolean
}

const DEFAULT_OPTIONS: Required<RetryOptions> = {
  maxRetries: 3,
  baseDelay: 1_000,
  maxDelay: 10_000,
  retryAllErrors: false
}

export function withRetry(options: RetryOptions = {}) {
  const { maxRetries, baseDelay, maxDelay, retryAllErrors } = { ...DEFAULT_OPTIONS, ...options }

  return function (_target: any, _propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function* (...args: any[]) {
      let lastError: any = null

      for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
          if (attempt > 0) {
            console.log(`[Retry] Attempt ${attempt + 1}/${maxRetries} for ${_propertyKey}`)
          }

          yield* originalMethod.apply(this, args)

          if (attempt > 0) {
            console.log(`[Retry] Success on attempt ${attempt + 1}/${maxRetries} for ${_propertyKey}`)
          }
          return
        } catch (error: any) {
          lastError = error
          const isRateLimit = error?.status === 429
          const isServerError = error?.status >= 500 && error?.status < 600
          const isNetworkError = isNetworkRelatedError(error)
          const isLastAttempt = attempt === maxRetries - 1

          // Determine if we should retry
          const shouldRetry = (isRateLimit || isServerError || isNetworkError || retryAllErrors) && !isLastAttempt

          if (!shouldRetry) {
            console.error(`[Retry] Final failure on attempt ${attempt + 1}/${maxRetries} for ${_propertyKey}:`, {
              status: error?.status,
              message: error?.message,
              code: error?.code,
              type: error?.type
            })
            throw error
          }

          // Log retry attempt
          console.warn(`[Retry] Attempt ${attempt + 1}/${maxRetries} failed for ${_propertyKey}:`, {
            status: error?.status,
            message: error?.message,
            code: error?.code,
            type: error?.type,
            willRetry: true
          })

          // Get retry delay from header or calculate exponential backoff
          // Check various rate limit headers
          const retryAfter = error.headers?.['retry-after'] || error.headers?.['x-ratelimit-reset'] || error.headers?.['ratelimit-reset']

          let delay: number
          if (retryAfter) {
            // Handle both delta-seconds and Unix timestamp formats
            const retryValue = parseInt(retryAfter, 10)
            if (retryValue > Date.now() / 1000) {
              // Unix timestamp
              delay = Math.max(0, retryValue * 1000 - Date.now())
            } else {
              // Delta seconds
              delay = retryValue * 1000
            }
            console.log(`[Retry] Using server-provided retry delay: ${delay}ms`)
          } else {
            // Use exponential backoff if no header
            delay = Math.min(maxDelay, baseDelay * Math.pow(2, attempt))
            console.log(`[Retry] Using exponential backoff delay: ${delay}ms`)
          }

          // Ensure delay is reasonable
          delay = Math.max(100, Math.min(delay, maxDelay))

          await new Promise((resolve) => setTimeout(resolve, delay))
        }
      }

      // This should never be reached, but just in case
      throw lastError || new Error('All retry attempts failed')
    }

    return descriptor
  }
}

// Helper function to identify network-related errors
function isNetworkRelatedError(error: any): boolean {
  if (!error) return false

  const networkErrorCodes = ['ECONNRESET', 'ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND', 'ENETUNREACH', 'EHOSTUNREACH', 'EPIPE', 'EAI_AGAIN']

  const networkErrorTypes = ['network', 'system', 'request-timeout']

  return (
    networkErrorCodes.includes(error.code) ||
    networkErrorTypes.includes(error.type) ||
    (error.message && error.message.toLowerCase().includes('network')) ||
    (error.message && error.message.toLowerCase().includes('timeout')) ||
    (error.message && error.message.toLowerCase().includes('connection'))
  )
}
