// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: common.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from '@bufbuild/protobuf/wire'

export const protobufPackage = 'cline'

export interface Metadata {}

export interface EmptyRequest {
  metadata?: Metadata | undefined
}

export interface Empty {}

export interface StringRequest {
  metadata?: Metadata | undefined
  value: string
}

export interface StringArrayRequest {
  metadata?: Metadata | undefined
  value: string[]
}

export interface String {
  value: string
}

export interface Int64Request {
  metadata?: Metadata | undefined
  value: number
}

export interface Int64 {
  value: number
}

export interface BytesRequest {
  metadata?: Metadata | undefined
  value: Buffer
}

export interface Bytes {
  value: Buffer
}

export interface BooleanRequest {
  metadata?: Metadata | undefined
  value: boolean
}

export interface Boolean {
  value: boolean
}

export interface StringArray {
  values: string[]
}

function createBaseMetadata(): Metadata {
  return {}
}

export const Metadata: MessageFns<Metadata> = {
  encode(_: <PERSON>ada<PERSON>, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Metadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseMetadata()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(_: any): Metadata {
    return {}
  },

  toJSON(_: Metadata): unknown {
    const obj: any = {}
    return obj
  },

  create<I extends Exact<DeepPartial<Metadata>, I>>(base?: I): Metadata {
    return Metadata.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<Metadata>, I>>(_: I): Metadata {
    const message = createBaseMetadata()
    return message
  }
}

function createBaseEmptyRequest(): EmptyRequest {
  return { metadata: undefined }
}

export const EmptyRequest: MessageFns<EmptyRequest> = {
  encode(message: EmptyRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmptyRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseEmptyRequest()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break
          }

          message.metadata = Metadata.decode(reader, reader.uint32())
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): EmptyRequest {
    return { metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined }
  },

  toJSON(message: EmptyRequest): unknown {
    const obj: any = {}
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata)
    }
    return obj
  },

  create<I extends Exact<DeepPartial<EmptyRequest>, I>>(base?: I): EmptyRequest {
    return EmptyRequest.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<EmptyRequest>, I>>(object: I): EmptyRequest {
    const message = createBaseEmptyRequest()
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? Metadata.fromPartial(object.metadata)
        : undefined
    return message
  }
}

function createBaseEmpty(): Empty {
  return {}
}

export const Empty: MessageFns<Empty> = {
  encode(_: Empty, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Empty {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseEmpty()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(_: any): Empty {
    return {}
  },

  toJSON(_: Empty): unknown {
    const obj: any = {}
    return obj
  },

  create<I extends Exact<DeepPartial<Empty>, I>>(base?: I): Empty {
    return Empty.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<Empty>, I>>(_: I): Empty {
    const message = createBaseEmpty()
    return message
  }
}

function createBaseStringRequest(): StringRequest {
  return { metadata: undefined, value: '' }
}

export const StringRequest: MessageFns<StringRequest> = {
  encode(message: StringRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
    }
    if (message.value !== '') {
      writer.uint32(18).string(message.value)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StringRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseStringRequest()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break
          }

          message.metadata = Metadata.decode(reader, reader.uint32())
          continue
        }
        case 2: {
          if (tag !== 18) {
            break
          }

          message.value = reader.string()
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): StringRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    }
  },

  toJSON(message: StringRequest): unknown {
    const obj: any = {}
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata)
    }
    if (message.value !== '') {
      obj.value = message.value
    }
    return obj
  },

  create<I extends Exact<DeepPartial<StringRequest>, I>>(base?: I): StringRequest {
    return StringRequest.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<StringRequest>, I>>(object: I): StringRequest {
    const message = createBaseStringRequest()
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? Metadata.fromPartial(object.metadata)
        : undefined
    message.value = object.value ?? ''
    return message
  }
}

function createBaseStringArrayRequest(): StringArrayRequest {
  return { metadata: undefined, value: [] }
}

export const StringArrayRequest: MessageFns<StringArrayRequest> = {
  encode(message: StringArrayRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
    }
    for (const v of message.value) {
      writer.uint32(18).string(v!)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StringArrayRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseStringArrayRequest()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break
          }

          message.metadata = Metadata.decode(reader, reader.uint32())
          continue
        }
        case 2: {
          if (tag !== 18) {
            break
          }

          message.value.push(reader.string())
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): StringArrayRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      value: globalThis.Array.isArray(object?.value)
        ? object.value.map((e: any) => globalThis.String(e))
        : []
    }
  },

  toJSON(message: StringArrayRequest): unknown {
    const obj: any = {}
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata)
    }
    if (message.value?.length) {
      obj.value = message.value
    }
    return obj
  },

  create<I extends Exact<DeepPartial<StringArrayRequest>, I>>(base?: I): StringArrayRequest {
    return StringArrayRequest.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<StringArrayRequest>, I>>(object: I): StringArrayRequest {
    const message = createBaseStringArrayRequest()
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? Metadata.fromPartial(object.metadata)
        : undefined
    message.value = object.value?.map((e) => e) || []
    return message
  }
}

function createBaseString(): String {
  return { value: '' }
}

export const String: MessageFns<String> = {
  encode(message: String, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== '') {
      writer.uint32(10).string(message.value)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): String {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseString()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break
          }

          message.value = reader.string()
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): String {
    return { value: isSet(object.value) ? globalThis.String(object.value) : '' }
  },

  toJSON(message: String): unknown {
    const obj: any = {}
    if (message.value !== '') {
      obj.value = message.value
    }
    return obj
  },

  create<I extends Exact<DeepPartial<String>, I>>(base?: I): String {
    return String.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<String>, I>>(object: I): String {
    const message = createBaseString()
    message.value = object.value ?? ''
    return message
  }
}

function createBaseInt64Request(): Int64Request {
  return { metadata: undefined, value: 0 }
}

export const Int64Request: MessageFns<Int64Request> = {
  encode(message: Int64Request, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Int64Request {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseInt64Request()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break
          }

          message.metadata = Metadata.decode(reader, reader.uint32())
          continue
        }
        case 2: {
          if (tag !== 16) {
            break
          }

          message.value = longToNumber(reader.int64())
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): Int64Request {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    }
  },

  toJSON(message: Int64Request): unknown {
    const obj: any = {}
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata)
    }
    if (message.value !== 0) {
      obj.value = Math.round(message.value)
    }
    return obj
  },

  create<I extends Exact<DeepPartial<Int64Request>, I>>(base?: I): Int64Request {
    return Int64Request.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<Int64Request>, I>>(object: I): Int64Request {
    const message = createBaseInt64Request()
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? Metadata.fromPartial(object.metadata)
        : undefined
    message.value = object.value ?? 0
    return message
  }
}

function createBaseInt64(): Int64 {
  return { value: 0 }
}

export const Int64: MessageFns<Int64> = {
  encode(message: Int64, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== 0) {
      writer.uint32(8).int64(message.value)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Int64 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseInt64()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break
          }

          message.value = longToNumber(reader.int64())
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): Int64 {
    return { value: isSet(object.value) ? globalThis.Number(object.value) : 0 }
  },

  toJSON(message: Int64): unknown {
    const obj: any = {}
    if (message.value !== 0) {
      obj.value = Math.round(message.value)
    }
    return obj
  },

  create<I extends Exact<DeepPartial<Int64>, I>>(base?: I): Int64 {
    return Int64.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<Int64>, I>>(object: I): Int64 {
    const message = createBaseInt64()
    message.value = object.value ?? 0
    return message
  }
}

function createBaseBytesRequest(): BytesRequest {
  return { metadata: undefined, value: Buffer.alloc(0) }
}

export const BytesRequest: MessageFns<BytesRequest> = {
  encode(message: BytesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
    }
    if (message.value.length !== 0) {
      writer.uint32(18).bytes(message.value)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BytesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseBytesRequest()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break
          }

          message.metadata = Metadata.decode(reader, reader.uint32())
          continue
        }
        case 2: {
          if (tag !== 18) {
            break
          }

          message.value = Buffer.from(reader.bytes())
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): BytesRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      value: isSet(object.value) ? Buffer.from(bytesFromBase64(object.value)) : Buffer.alloc(0)
    }
  },

  toJSON(message: BytesRequest): unknown {
    const obj: any = {}
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata)
    }
    if (message.value.length !== 0) {
      obj.value = base64FromBytes(message.value)
    }
    return obj
  },

  create<I extends Exact<DeepPartial<BytesRequest>, I>>(base?: I): BytesRequest {
    return BytesRequest.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<BytesRequest>, I>>(object: I): BytesRequest {
    const message = createBaseBytesRequest()
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? Metadata.fromPartial(object.metadata)
        : undefined
    message.value = object.value ?? Buffer.alloc(0)
    return message
  }
}

function createBaseBytes(): Bytes {
  return { value: Buffer.alloc(0) }
}

export const Bytes: MessageFns<Bytes> = {
  encode(message: Bytes, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value.length !== 0) {
      writer.uint32(10).bytes(message.value)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Bytes {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseBytes()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break
          }

          message.value = Buffer.from(reader.bytes())
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): Bytes {
    return {
      value: isSet(object.value) ? Buffer.from(bytesFromBase64(object.value)) : Buffer.alloc(0)
    }
  },

  toJSON(message: Bytes): unknown {
    const obj: any = {}
    if (message.value.length !== 0) {
      obj.value = base64FromBytes(message.value)
    }
    return obj
  },

  create<I extends Exact<DeepPartial<Bytes>, I>>(base?: I): Bytes {
    return Bytes.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<Bytes>, I>>(object: I): Bytes {
    const message = createBaseBytes()
    message.value = object.value ?? Buffer.alloc(0)
    return message
  }
}

function createBaseBooleanRequest(): BooleanRequest {
  return { metadata: undefined, value: false }
}

export const BooleanRequest: MessageFns<BooleanRequest> = {
  encode(message: BooleanRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
    }
    if (message.value !== false) {
      writer.uint32(16).bool(message.value)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BooleanRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseBooleanRequest()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break
          }

          message.metadata = Metadata.decode(reader, reader.uint32())
          continue
        }
        case 2: {
          if (tag !== 16) {
            break
          }

          message.value = reader.bool()
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): BooleanRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      value: isSet(object.value) ? globalThis.Boolean(object.value) : false
    }
  },

  toJSON(message: BooleanRequest): unknown {
    const obj: any = {}
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata)
    }
    if (message.value !== false) {
      obj.value = message.value
    }
    return obj
  },

  create<I extends Exact<DeepPartial<BooleanRequest>, I>>(base?: I): BooleanRequest {
    return BooleanRequest.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<BooleanRequest>, I>>(object: I): BooleanRequest {
    const message = createBaseBooleanRequest()
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? Metadata.fromPartial(object.metadata)
        : undefined
    message.value = object.value ?? false
    return message
  }
}

function createBaseBoolean(): Boolean {
  return { value: false }
}

export const Boolean: MessageFns<Boolean> = {
  encode(message: Boolean, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== false) {
      writer.uint32(8).bool(message.value)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Boolean {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseBoolean()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break
          }

          message.value = reader.bool()
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): Boolean {
    return { value: isSet(object.value) ? globalThis.Boolean(object.value) : false }
  },

  toJSON(message: Boolean): unknown {
    const obj: any = {}
    if (message.value !== false) {
      obj.value = message.value
    }
    return obj
  },

  create<I extends Exact<DeepPartial<Boolean>, I>>(base?: I): Boolean {
    return Boolean.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<Boolean>, I>>(object: I): Boolean {
    const message = createBaseBoolean()
    message.value = object.value ?? false
    return message
  }
}

function createBaseStringArray(): StringArray {
  return { values: [] }
}

export const StringArray: MessageFns<StringArray> = {
  encode(message: StringArray, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.values) {
      writer.uint32(10).string(v!)
    }
    return writer
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StringArray {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
    let end = length === undefined ? reader.len : reader.pos + length
    const message = createBaseStringArray()
    while (reader.pos < end) {
      const tag = reader.uint32()
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break
          }

          message.values.push(reader.string())
          continue
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break
      }
      reader.skip(tag & 7)
    }
    return message
  },

  fromJSON(object: any): StringArray {
    return {
      values: globalThis.Array.isArray(object?.values)
        ? object.values.map((e: any) => globalThis.String(e))
        : []
    }
  },

  toJSON(message: StringArray): unknown {
    const obj: any = {}
    if (message.values?.length) {
      obj.values = message.values
    }
    return obj
  },

  create<I extends Exact<DeepPartial<StringArray>, I>>(base?: I): StringArray {
    return StringArray.fromPartial(base ?? ({} as any))
  },
  fromPartial<I extends Exact<DeepPartial<StringArray>, I>>(object: I): StringArray {
    const message = createBaseStringArray()
    message.values = object.values?.map((e) => e) || []
    return message
  }
}

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'))
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString('base64')
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>

type KeysOfUnion<T> = T extends T ? keyof T : never
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never }

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString())
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error('Value is larger than Number.MAX_SAFE_INTEGER')
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error('Value is smaller than Number.MIN_SAFE_INTEGER')
  }
  return num
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter
  decode(input: BinaryReader | Uint8Array, length?: number): T
  fromJSON(object: any): T
  toJSON(message: T): unknown
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T
}
