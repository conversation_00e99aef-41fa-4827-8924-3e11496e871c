import { ApiConfiguration } from './api'
import { AutoApprovalSettings } from './AutoApprovalSettings'
import { ChatSettings } from './ChatSettings'
export interface ExtensionMessage {
  type:
    | 'action'
    | 'state'
    | 'selectedImages'
    | 'theme'
    | 'workspaceUpdated'
    | 'invoke'
    | 'partialMessage'
    | 'relinquishControl'
    | 'authCallback'
    | 'commitSearchResults'
    | 'openGraphData'
    | 'didUpdateSettings'
    | 'userCreditsBalance'
    | 'userCreditsUsage'
    | 'userCreditsPayments'
    | 'totalTasksSize'
    | 'addToInput'
    | 'browserConnectionResult'
    | 'fileSearchResults'
    | 'grpc_response'
    | 'requestyModels'
    | 'commandGenerationResponse'

  text?: string
  action?:
    | 'mcpButtonClicked'
    | 'settingsButtonClicked'
    | 'historyButtonClicked'
    | 'didBecomeVisible'
    | 'accountLogoutClicked'
    | 'accountButtonClicked'
    | 'focusChatInput'
  invoke?: Invoke
  state?: ExtensionState
  filePaths?: string[]
  partialMessage?: ChatermMessage
  customToken?: string
  error?: string
  openGraphData?: {
    title?: string
    description?: string
    image?: string
    url?: string
    siteName?: string
    type?: string
  }
  url?: string
  totalTasksSize?: number | null
  success?: boolean
  endpoint?: string
  isBundled?: boolean
  isConnected?: boolean
  isRemote?: boolean
  host?: string
  mentionsRequestId?: string
  results?: Array<{
    path: string
    type: 'file' | 'folder'
    label?: string
  }>
  grpc_response?: {
    message?: any
    request_id: string
    error?: string
    is_streaming?: boolean
    sequence_number?: number
  }
  // For command generation response
  command?: string
  tabId?: string
}

export type Invoke = 'sendMessage' | 'primaryButtonClick' | 'secondaryButtonClick'

export type Platform = 'aix' | 'darwin' | 'freebsd' | 'linux' | 'openbsd' | 'sunos' | 'win32' | 'unknown'

export const DEFAULT_PLATFORM = 'unknown'

export interface ExtensionState {
  isNewUser: boolean
  apiConfiguration?: ApiConfiguration
  autoApprovalSettings: AutoApprovalSettings
  remoteBrowserHost?: string
  chatSettings: ChatSettings
  checkpointTrackerErrorMessage?: string
  chatermMessages: ChatermMessage[]
  customInstructions?: string
  mcpMarketplaceEnabled?: boolean
  enableCheckpointsSetting?: boolean
  platform: Platform
  shouldShowAnnouncement: boolean
  shellIntegrationTimeout: number
  uriScheme?: string
  userInfo?: {
    displayName: string | null
    email: string | null
    photoURL: string | null
  }
  version: string
  vscMachineId: string
}

export interface ChatermMessage {
  ts: number
  type: 'ask' | 'say'
  ask?: ChatermAsk
  say?: ChatermSay
  text?: string
  reasoning?: string
  images?: string[]
  partial?: boolean
  lastCheckpointHash?: string
  isCheckpointCheckedOut?: boolean
  isOperationOutsideWorkspace?: boolean
  conversationHistoryIndex?: number
  conversationHistoryDeletedRange?: [number, number]
}

export type ChatermAsk =
  | 'followup'
  | 'command'
  | 'command_output'
  | 'completion_result'
  | 'tool'
  | 'api_req_failed'
  | 'ssh_con_failed'
  | 'resume_task'
  | 'resume_completed_task'
  | 'mistake_limit_reached'
  | 'auto_approval_max_req_reached'
  | 'condense'
  | 'report_bug'

export type ChatermSay =
  | 'task'
  | 'error'
  | 'api_req_started'
  | 'api_req_finished'
  | 'text'
  | 'reasoning'
  | 'completion_result'
  | 'user_feedback'
  | 'user_feedback_diff'
  | 'api_req_retried'
  | 'command'
  | 'command_output'
  | 'tool'
  | 'shell_integration_warning'
  | 'diff_error'
  | 'deleted_api_reqs'
  | 'checkpoint_created'
  | 'sshInfo'
  | 'interactive_command_notification'

export interface ChatermSayTool {
  tool: 'readFile' | 'listFilesTopLevel' | 'listFilesRecursive' | 'searchFiles'
  path?: string
  diff?: string
  content?: string
  regex?: string
  filePattern?: string
  operationIsLocatedInWorkspace?: boolean
}

export interface ChatermAskQuestion {
  question: string
  options?: string[]
  selected?: string
}

export interface ChatermAskNewTask {
  context: string
}

export interface ChatermApiReqInfo {
  request?: string
  tokensIn?: number
  tokensOut?: number
  cacheWrites?: number
  cacheReads?: number
  cost?: number
  cancelReason?: ChatermApiReqCancelReason
  streamingFailedMessage?: string
  retryStatus?: {
    attempt: number
    maxAttempts: number
    delaySec: number
    errorSnippet?: string
  }
}

export type ChatermApiReqCancelReason = 'streaming_failed' | 'user_cancelled' | 'retries_exhausted'

export const COMPLETION_RESULT_CHANGES_FLAG = 'HAS_CHANGES'
