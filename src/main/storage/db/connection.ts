import Database from 'better-sqlite3'
import { join } from 'path'
import * as fs from 'fs'
import { v4 as uuidv4 } from 'uuid'

// 在测试环境中，app可能不可用，使用fallback路径
let USER_DATA_PATH: string
try {
  const { app } = require('electron')
  USER_DATA_PATH = app.getPath('userData')
} catch (error) {
  // 测试环境或非Electron环境的fallback
  USER_DATA_PATH = join(process.cwd(), 'test_data')
}
const INIT_DB_PATH = getInitDbPath()
const INIT_CDB_PATH = getInitChatermDbPath()

let currentUserId: number | null = null

function getUserDatabasePath(userId: number, dbType: 'complete' | 'chaterm'): string {
  const userDir = join(USER_DATA_PATH, 'databases', `${userId}`)
  const dbName = dbType === 'complete' ? 'complete_data.db' : 'chaterm_data.db'
  return join(userDir, dbName)
}

export function getChatermDbPathForUser(userId: number): string {
  return getUserDatabasePath(userId, 'chaterm')
}

function ensureUserDatabaseDir(userId: number): string {
  try {
    if (!userId || userId <= 0) {
      throw new Error(`Invalid user ID: ${userId}`)
    }

    const userDir = join(USER_DATA_PATH, 'users', userId.toString())

    if (!fs.existsSync(userDir)) {
      console.log(`[DB-Directory] Creating user database directory: ${userDir}`)
      fs.mkdirSync(userDir, { recursive: true })

      // 验证目录创建成功
      if (!fs.existsSync(userDir)) {
        throw new Error(`Failed to create user database directory: ${userDir}`)
      }

      // 验证目录权限
      try {
        fs.accessSync(userDir, fs.constants.W_OK | fs.constants.R_OK)
      } catch (accessError) {
        throw new Error(`User database directory is not accessible: ${userDir} - ${accessError}`)
      }

      console.log(`[DB-Directory] User database directory created successfully: ${userDir}`)
    } else {
      // 验证现有目录权限
      try {
        fs.accessSync(userDir, fs.constants.W_OK | fs.constants.R_OK)
      } catch (accessError) {
        throw new Error(`Existing user database directory is not accessible: ${userDir} - ${accessError}`)
      }
    }

    return userDir
  } catch (error) {
    console.error(`[DB-Directory] Failed to ensure user database directory for user ${userId}:`, {
      error: error instanceof Error ? error.message : String(error),
      userId,
      userDataPath: USER_DATA_PATH
    })
    throw error
  }
}

function getLegacyDatabasePath(dbType: 'complete' | 'chaterm'): string {
  const dbName = dbType === 'complete' ? 'complete_data.db' : 'chaterm_data.db'
  return join(USER_DATA_PATH, 'databases', dbName)
}

function migrateLegacyDatabase(userId: number, dbType: 'complete' | 'chaterm'): boolean {
  const legacyPath = getLegacyDatabasePath(dbType)
  const targetPath = getUserDatabasePath(userId, dbType)

  try {
    if (!fs.existsSync(legacyPath)) {
      console.log(`[DB-Migration] Legacy ${dbType} database not found at ${legacyPath}, skipping migration`)
      return false
    }

    // 验证源文件是否可读
    try {
      fs.accessSync(legacyPath, fs.constants.R_OK)
    } catch (accessError) {
      console.error(`[DB-Migration] Cannot read legacy ${dbType} database:`, accessError)
      return false
    }

    // 检查目标文件是否已存在
    if (fs.existsSync(targetPath)) {
      console.log(`[DB-Migration] Target ${dbType} database already exists at ${targetPath}, skipping migration`)
      return true
    }

    // 确保目标目录存在
    const targetDir = require('path').dirname(targetPath)
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true })
      console.log(`[DB-Migration] Created target directory: ${targetDir}`)
    }

    console.log(`[DB-Migration] Migrating legacy ${dbType} database from ${legacyPath} to ${targetPath}`)

    // 验证源数据库文件完整性
    const sourceStats = fs.statSync(legacyPath)
    if (sourceStats.size === 0) {
      console.error(`[DB-Migration] Legacy ${dbType} database file is empty: ${legacyPath}`)
      return false
    }

    // 执行文件复制
    fs.copyFileSync(legacyPath, targetPath)

    // 验证复制结果
    const targetStats = fs.statSync(targetPath)
    if (targetStats.size !== sourceStats.size) {
      console.error(`[DB-Migration] File size mismatch after migration: source=${sourceStats.size}, target=${targetStats.size}`)
      // 清理不完整的文件
      try {
        fs.unlinkSync(targetPath)
      } catch (cleanupError) {
        console.error(`[DB-Migration] Failed to cleanup incomplete file:`, cleanupError)
      }
      return false
    }

    console.log(`[DB-Migration] Legacy ${dbType} database migrated successfully (${targetStats.size} bytes)`)
    return true
  } catch (error) {
    console.error(`[DB-Migration] Failed to migrate legacy ${dbType} database:`, {
      error: error instanceof Error ? error.message : String(error),
      legacyPath,
      targetPath,
      userId,
      dbType
    })

    // 清理可能的不完整文件
    try {
      if (fs.existsSync(targetPath)) {
        fs.unlinkSync(targetPath)
        console.log(`[DB-Migration] Cleaned up incomplete migration file: ${targetPath}`)
      }
    } catch (cleanupError) {
      console.error(`[DB-Migration] Failed to cleanup after migration error:`, cleanupError)
    }

    return false
  }
}

function getInitChatermDbPath(): string {
  try {
    const { app } = require('electron')
    if (app.isPackaged) {
      return join((process as any).resourcesPath, 'db', 'init_chaterm.db')
    } else {
      return join(__dirname, '../../src/renderer/src/assets/db/init_chaterm.db')
    }
  } catch (error) {
    // 测试环境fallback
    return join(process.cwd(), 'test_data', 'init_chaterm.db')
  }
}

function getInitDbPath(): string {
  try {
    const { app } = require('electron')
    if (app.isPackaged) {
      return join((process as any).resourcesPath, 'db', 'init_data.db')
    } else {
      return join(__dirname, '../../src/renderer/src/assets/db/init_data.db')
    }
  } catch (error) {
    // 测试环境fallback
    return join(process.cwd(), 'test_data', 'init_data.db')
  }
}

export function setCurrentUserId(userId: number | null): void {
  currentUserId = userId
}

export function getCurrentUserId(): number | null {
  return currentUserId
}

export function getGuestUserId(): number {
  return 999999999
}

function upgradeUserSnippetTable(db: Database.Database): void {
  try {
    console.log('[DB-Upgrade] Starting user_snippet_v1 table upgrade')

    // 验证表是否存在
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='user_snippet_v1'").get()
    if (!tableExists) {
      console.log('[DB-Upgrade] user_snippet_v1 table does not exist, skipping upgrade')
      return
    }

    // Check if sort_order column exists
    try {
      db.prepare('SELECT sort_order FROM user_snippet_v1 LIMIT 1').get()
      console.log('[DB-Upgrade] sort_order column already exists in user_snippet_v1 table')
    } catch (error) {
      // Column does not exist, need to upgrade table structure
      console.log('[DB-Upgrade] sort_order column not found, adding to user_snippet_v1 table')

      try {
        const transaction = db.transaction(() => {
          // Add sort_order column
          db.exec('ALTER TABLE user_snippet_v1 ADD COLUMN sort_order INTEGER DEFAULT 0')
          console.log('[DB-Upgrade] Added sort_order column to user_snippet_v1')

          // Initialize sort_order for existing records
          let allRecords: any[]
          try {
            allRecords = db.prepare('SELECT id FROM user_snippet_v1 ORDER BY created_at ASC').all()
          } catch (selectError) {
            throw new Error(`Failed to query existing records: ${selectError}`)
          }

          if (allRecords.length > 0) {
            let updateSortStmt: Database.Statement
            try {
              updateSortStmt = db.prepare('UPDATE user_snippet_v1 SET sort_order = ? WHERE id = ?')
            } catch (prepareError) {
              throw new Error(`Failed to prepare update statement: ${prepareError}`)
            }

            allRecords.forEach((record: any, index: number) => {
              try {
                updateSortStmt.run((index + 1) * 10, record.id) // Use multiples of 10 to leave space for insertion
              } catch (updateError) {
                throw new Error(`Failed to update record ${record.id}: ${updateError}`)
              }
            })
            console.log(`[DB-Upgrade] Initialized sort_order for ${allRecords.length} existing records`)
          } else {
            console.log('[DB-Upgrade] No existing records found, sort_order initialization not needed')
          }
        })

        transaction()
        console.log('[DB-Upgrade] user_snippet_v1 table upgrade completed successfully')
      } catch (transactionError) {
        throw new Error(`Transaction failed during user_snippet_v1 upgrade: ${transactionError}`)
      }
    }
  } catch (error) {
    console.error('[DB-Upgrade] Failed to upgrade user_snippet_v1 table:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    })
    // 不抛出错误，允许应用继续运行，但记录详细错误信息
  }
}

function upgradeTAssetsTable(db: Database.Database): void {
  try {
    console.log('[DB-Upgrade] Starting t_assets table upgrade')

    // 验证表是否存在
    const tAssetsExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='t_assets'").get()
    const tAssetChainsExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='t_asset_chains'").get()

    if (!tAssetsExists) {
      console.log('[DB-Upgrade] t_assets table does not exist, skipping related upgrades')
      return
    }

    // 检查 asset_type 列是否存在
    try {
      db.prepare('SELECT asset_type FROM t_assets LIMIT 1').get()
      console.log('[DB-Upgrade] asset_type column already exists in t_assets')
    } catch (error) {
      // 列不存在，需要升级表结构
      console.log('[DB-Upgrade] Adding asset_type column to t_assets')
      try {
        const transaction = db.transaction(() => {
          db.exec("ALTER TABLE t_assets ADD COLUMN asset_type TEXT DEFAULT 'person'")
          console.log('[DB-Upgrade] Added asset_type column to t_assets')
        })
        transaction()
      } catch (transactionError) {
        throw new Error(`Failed to add asset_type column: ${transactionError}`)
      }
    }

    // 追加列升级：t_assets.version
    try {
      db.prepare('SELECT version FROM t_assets LIMIT 1').get()
      console.log('[DB-Upgrade] version column already exists in t_assets')
    } catch (e) {
      console.log('[DB-Upgrade] Adding version column to t_assets')
      try {
        db.exec('ALTER TABLE t_assets ADD COLUMN version INTEGER NOT NULL DEFAULT 1')
        console.log('[DB-Upgrade] Added version column to t_assets')
      } catch (alterError) {
        throw new Error(`Failed to add version column: ${alterError}`)
      }
    }

    // 追加列升级：t_asset_chains.uuid
    if (tAssetChainsExists) {
      try {
        db.prepare('SELECT uuid FROM t_asset_chains LIMIT 1').get()
        console.log('[DB-Upgrade] uuid column already exists in t_asset_chains')
      } catch (e) {
        // uuid 列不存在，需要添加
        console.log('[DB-Upgrade] Adding uuid column to t_asset_chains')
        try {
          const transaction = db.transaction(() => {
            db.exec('ALTER TABLE t_asset_chains ADD COLUMN uuid TEXT')
            console.log('[DB-Upgrade] Added uuid column to t_asset_chains')
          })
          transaction()
        } catch (transactionError) {
          throw new Error(`Failed to add uuid column to t_asset_chains: ${transactionError}`)
        }
      }

      // 检查并填充缺失的 UUID
      try {
        let existingRecords: { key_chain_id: string | number }[]
        try {
          existingRecords = db.prepare("SELECT key_chain_id FROM t_asset_chains WHERE uuid IS NULL OR uuid = ''").all() as {
            key_chain_id: string | number
          }[]
        } catch (selectError) {
          throw new Error(`Failed to query records with missing uuid: ${selectError}`)
        }

        if (existingRecords.length > 0) {
          console.log(`[DB-Upgrade] Filling uuid for ${existingRecords.length} existing t_asset_chains records`)
          try {
            const transaction = db.transaction(() => {
              let updateUuidStmt: Database.Statement
              try {
                updateUuidStmt = db.prepare('UPDATE t_asset_chains SET uuid = ? WHERE key_chain_id = ?')
              } catch (prepareError) {
                throw new Error(`Failed to prepare uuid update statement: ${prepareError}`)
              }

              existingRecords.forEach((record: { key_chain_id: string | number }) => {
                try {
                  updateUuidStmt.run(uuidv4(), record.key_chain_id)
                } catch (updateError) {
                  throw new Error(`Failed to update uuid for record ${record.key_chain_id}: ${updateError}`)
                }
              })
            })
            transaction()
            console.log(`[DB-Upgrade] Auto-filled uuid for ${existingRecords.length} existing t_asset_chains records`)
          } catch (transactionError) {
            throw new Error(`Transaction failed during uuid filling: ${transactionError}`)
          }
        } else {
          console.log('[DB-Upgrade] No records with missing uuid found in t_asset_chains')
        }
      } catch (fillError) {
        console.error('[DB-Upgrade] Error filling uuid for t_asset_chains:', fillError)
        // 继续执行其他升级，不中断整个过程
      }
    } else {
      console.log('[DB-Upgrade] t_asset_chains table does not exist, skipping uuid column upgrade')
    }

    // 追加列：t_assets.need_proxy
    try {
      db.prepare('SELECT need_proxy FROM t_assets LIMIT 1').get()
      console.log('[DB-Upgrade] need_proxy column already exists in t_assets')
    } catch (e) {
      console.log('[DB-Upgrade] Adding need_proxy column to t_assets')
      try {
        db.exec('ALTER TABLE t_assets ADD COLUMN need_proxy INTEGER DEFAULT 0')
        console.log('[DB-Upgrade] Added need_proxy column to t_assets')
      } catch (alterError) {
        throw new Error(`Failed to add need_proxy column: ${alterError}`)
      }
    }

    // 追加列升级：t_assets.proxy_name
    try {
      db.prepare('SELECT proxy_name FROM t_assets LIMIT 1').get()
      console.log('[DB-Upgrade] proxy_name column already exists in t_assets')
    } catch (e) {
      console.log('[DB-Upgrade] Adding proxy_name column to t_assets')
      try {
        db.exec('ALTER TABLE t_assets ADD COLUMN proxy_name TEXT DEFAULT ""')
        console.log('[DB-Upgrade] Added proxy_name column to t_assets')
      } catch (alterError) {
        throw new Error(`Failed to add proxy_name column: ${alterError}`)
      }
    }

    console.log('[DB-Upgrade] t_assets table upgrade completed successfully')
  } catch (error) {
    console.error('[DB-Upgrade] Failed to upgrade t_assets table:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    })
    // 不抛出错误，允许应用继续运行，但记录详细错误信息
  }
}

export async function initDatabase(userId?: number): Promise<Database.Database> {
  const targetUserId = userId || currentUserId

  if (!targetUserId) {
    const errorMsg = 'User ID is required for database initialization'
    console.error(`[DB-Init] ${errorMsg}`, { providedUserId: userId, currentUserId })
    throw new Error(errorMsg)
  }

  const COMPLETE_DB_PATH = getUserDatabasePath(targetUserId, 'complete')

  try {
    console.log(`[DB-Init] Initializing complete database for user ${targetUserId}`)

    // 确保用户数据库目录存在
    ensureUserDatabaseDir(targetUserId)

    if (!fs.existsSync(COMPLETE_DB_PATH)) {
      console.log(`[DB-Init] Complete database not found at ${COMPLETE_DB_PATH}, attempting migration or initialization`)

      // 尝试迁移旧数据库
      const migrated = migrateLegacyDatabase(targetUserId, 'complete')

      if (!migrated) {
        console.log(`[DB-Init] No legacy database to migrate, initializing from template: ${INIT_DB_PATH}`)

        // 验证初始数据库文件存在
        if (!fs.existsSync(INIT_DB_PATH)) {
          throw new Error(`Initial database template not found at: ${INIT_DB_PATH}`)
        }

        // 验证初始数据库文件可读
        try {
          fs.accessSync(INIT_DB_PATH, fs.constants.R_OK)
        } catch (accessError) {
          throw new Error(`Cannot read initial database template: ${INIT_DB_PATH} - ${accessError}`)
        }

        // 验证初始数据库文件完整性
        const initStats = fs.statSync(INIT_DB_PATH)
        if (initStats.size === 0) {
          throw new Error(`Initial database template is empty: ${INIT_DB_PATH}`)
        }

        console.log(`[DB-Init] Copying template database (${initStats.size} bytes) to ${COMPLETE_DB_PATH}`)

        let sourceDb: Database.Database | null = null
        try {
          sourceDb = new Database(INIT_DB_PATH, { readonly: true, fileMustExist: true })

          // 验证源数据库可以正常打开
          try {
            sourceDb.prepare('SELECT 1').get()
          } catch (dbError) {
            throw new Error(`Initial database template is corrupted: ${dbError}`)
          }

          await sourceDb.backup(COMPLETE_DB_PATH)
          console.log(`[DB-Init] Database template copied successfully`)
        } catch (backupError) {
          throw new Error(`Failed to backup initial database: ${backupError}`)
        } finally {
          if (sourceDb) {
            try {
              sourceDb.close()
            } catch (closeError) {
              console.warn(`[DB-Init] Warning: Failed to close source database:`, closeError)
            }
          }
        }

        // 验证复制结果
        if (!fs.existsSync(COMPLETE_DB_PATH)) {
          throw new Error(`Database initialization failed: target file not created at ${COMPLETE_DB_PATH}`)
        }

        const targetStats = fs.statSync(COMPLETE_DB_PATH)
        if (targetStats.size === 0) {
          throw new Error(`Database initialization failed: target file is empty at ${COMPLETE_DB_PATH}`)
        }

        console.log(`[DB-Init] Database initialized successfully (${targetStats.size} bytes)`)
      } else {
        console.log(`[DB-Init] Legacy database migrated successfully`)
      }
    } else {
      console.log(`[DB-Init] Complete database already exists at ${COMPLETE_DB_PATH}`)

      // 验证现有数据库文件完整性
      const existingStats = fs.statSync(COMPLETE_DB_PATH)
      if (existingStats.size === 0) {
        console.warn(`[DB-Init] Existing database file is empty, reinitializing: ${COMPLETE_DB_PATH}`)
        fs.unlinkSync(COMPLETE_DB_PATH)
        return initDatabase(targetUserId) // 递归重新初始化
      }
    }

    // 打开数据库连接
    let db: Database.Database
    try {
      db = new Database(COMPLETE_DB_PATH, { fileMustExist: true })

      // 验证数据库连接
      try {
        db.prepare('SELECT 1').get()
        console.log(`[DB-Init] Complete database connection established successfully at: ${COMPLETE_DB_PATH}`)
      } catch (testError) {
        db.close()
        throw new Error(`Database connection test failed: ${testError}`)
      }
    } catch (dbError) {
      throw new Error(`Failed to open database at ${COMPLETE_DB_PATH}: ${dbError}`)
    }

    return db
  } catch (error) {
    console.error(`[DB-Init] Complete database initialization failed for user ${targetUserId}:`, {
      error: error instanceof Error ? error.message : String(error),
      targetUserId,
      dbPath: COMPLETE_DB_PATH,
      initDbPath: INIT_DB_PATH,
      userDataPath: USER_DATA_PATH
    })

    // 清理可能的不完整文件
    try {
      if (fs.existsSync(COMPLETE_DB_PATH)) {
        const stats = fs.statSync(COMPLETE_DB_PATH)
        if (stats.size === 0) {
          fs.unlinkSync(COMPLETE_DB_PATH)
          console.log(`[DB-Init] Cleaned up empty database file: ${COMPLETE_DB_PATH}`)
        }
      }
    } catch (cleanupError) {
      console.warn(`[DB-Init] Failed to cleanup after initialization error:`, cleanupError)
    }

    throw error
  }
}

export async function initChatermDatabase(userId?: number): Promise<Database.Database> {
  const targetUserId = userId || currentUserId

  if (!targetUserId) {
    const errorMsg = 'User ID is required for Chaterm database initialization'
    console.error(`[DB-Chaterm] ${errorMsg}`, { providedUserId: userId, currentUserId })
    throw new Error(errorMsg)
  }

  const Chaterm_DB_PATH = getUserDatabasePath(targetUserId, 'chaterm')

  try {
    console.log(`[DB-Chaterm] Initializing Chaterm database for user ${targetUserId}`)

    // 确保用户数据库目录存在
    ensureUserDatabaseDir(targetUserId)

    // 验证初始Chaterm数据库模板
    if (!fs.existsSync(INIT_CDB_PATH)) {
      throw new Error(`Initial Chaterm database template not found at: ${INIT_CDB_PATH}`)
    }

    // 验证初始数据库文件可读性和完整性
    try {
      fs.accessSync(INIT_CDB_PATH, fs.constants.R_OK)
      const initStats = fs.statSync(INIT_CDB_PATH)
      if (initStats.size === 0) {
        throw new Error(`Initial Chaterm database template is empty: ${INIT_CDB_PATH}`)
      }
      console.log(`[DB-Chaterm] Template database verified (${initStats.size} bytes)`)
    } catch (accessError) {
      throw new Error(`Cannot access initial Chaterm database template: ${INIT_CDB_PATH} - ${accessError}`)
    }

    const targetDbExists = fs.existsSync(Chaterm_DB_PATH)

    if (!targetDbExists) {
      console.log(`[DB-Chaterm] Target database not found at ${Chaterm_DB_PATH}, attempting migration or initialization`)

      // 尝试迁移旧数据库
      const migrated = migrateLegacyDatabase(targetUserId, 'chaterm')

      if (!migrated) {
        console.log(`[DB-Chaterm] No legacy database to migrate, copying from template`)

        let sourceDb: Database.Database | null = null
        try {
          sourceDb = new Database(INIT_CDB_PATH, { readonly: true, fileMustExist: true })

          // 验证源数据库可以正常打开
          try {
            sourceDb.prepare('SELECT 1').get()
          } catch (dbError) {
            throw new Error(`Initial Chaterm database template is corrupted: ${dbError}`)
          }

          await sourceDb.backup(Chaterm_DB_PATH)
          console.log(`[DB-Chaterm] Database template copied successfully`)
        } catch (backupError) {
          throw new Error(`Failed to backup initial Chaterm database: ${backupError}`)
        } finally {
          if (sourceDb) {
            try {
              sourceDb.close()
            } catch (closeError) {
              console.warn(`[DB-Chaterm] Warning: Failed to close source database:`, closeError)
            }
          }
        }

        // 验证复制结果
        if (!fs.existsSync(Chaterm_DB_PATH)) {
          throw new Error(`Chaterm database initialization failed: target file not created at ${Chaterm_DB_PATH}`)
        }

        const targetStats = fs.statSync(Chaterm_DB_PATH)
        if (targetStats.size === 0) {
          throw new Error(`Chaterm database initialization failed: target file is empty at ${Chaterm_DB_PATH}`)
        }

        console.log(`[DB-Chaterm] Database initialized successfully (${targetStats.size} bytes)`)
      } else {
        console.log(`[DB-Chaterm] Legacy database migrated successfully`)
      }
    } else {
      console.log(`[DB-Chaterm] Target database exists, performing schema synchronization`)

      // 验证现有数据库文件完整性
      const existingStats = fs.statSync(Chaterm_DB_PATH)
      if (existingStats.size === 0) {
        console.warn(`[DB-Chaterm] Existing database file is empty, reinitializing: ${Chaterm_DB_PATH}`)
        fs.unlinkSync(Chaterm_DB_PATH)
        return initChatermDatabase(targetUserId) // 递归重新初始化
      }

      let mainDb: Database.Database | null = null
      let initDb: Database.Database | null = null

      try {
        // 打开主数据库
        try {
          mainDb = new Database(Chaterm_DB_PATH)
          mainDb.prepare('SELECT 1').get() // 测试连接
        } catch (mainDbError) {
          throw new Error(`Failed to open existing Chaterm database: ${mainDbError}`)
        }

        // 打开模板数据库
        try {
          initDb = new Database(INIT_CDB_PATH, { readonly: true, fileMustExist: true })
          initDb.prepare('SELECT 1').get() // 测试连接
        } catch (initDbError) {
          throw new Error(`Failed to open template Chaterm database: ${initDbError}`)
        }

        // 获取模板数据库中的表结构
        let initTables: { name: string; sql: string }[]
        try {
          initTables = initDb.prepare("SELECT name, sql FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").all() as {
            name: string
            sql: string
          }[]
        } catch (queryError) {
          throw new Error(`Failed to query template database schema: ${queryError}`)
        }

        console.log(`[DB-Chaterm] Found ${initTables.length} tables in template database`)

        // 同步表结构
        for (const initTable of initTables) {
          const tableName = initTable.name
          const createTableSql = initTable.sql

          try {
            const tableExists = mainDb.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name = ?").get(tableName)

            if (!tableExists) {
              console.log(`[DB-Chaterm] Creating missing table: ${tableName}`)
              mainDb.exec(createTableSql)
              console.log(`[DB-Chaterm] Table ${tableName} created successfully`)
            }
          } catch (tableError) {
            console.error(`[DB-Chaterm] Failed to process table ${tableName}:`, tableError)
            throw new Error(`Schema synchronization failed for table ${tableName}: ${tableError}`)
          }
        }

        // 进行必要的升级
        try {
          console.log(`[DB-Chaterm] Performing database upgrades`)
          upgradeTAssetsTable(mainDb)
          upgradeUserSnippetTable(mainDb)
          console.log(`[DB-Chaterm] Database upgrades completed successfully`)
        } catch (upgradeError) {
          console.error(`[DB-Chaterm] Database upgrade failed:`, upgradeError)
          throw new Error(`Database upgrade failed: ${upgradeError}`)
        }
      } finally {
        // 安全关闭数据库连接
        if (mainDb) {
          try {
            mainDb.close()
          } catch (closeError) {
            console.warn(`[DB-Chaterm] Warning: Failed to close main database:`, closeError)
          }
        }
        if (initDb) {
          try {
            initDb.close()
          } catch (closeError) {
            console.warn(`[DB-Chaterm] Warning: Failed to close template database:`, closeError)
          }
        }
      }
    }

    // 打开最终的数据库连接
    let db: Database.Database
    try {
      db = new Database(Chaterm_DB_PATH, { fileMustExist: true })

      // 验证数据库连接
      try {
        db.prepare('SELECT 1').get()
        console.log(`[DB-Chaterm] Database connection established successfully at: ${Chaterm_DB_PATH}`)
      } catch (testError) {
        db.close()
        throw new Error(`Database connection test failed: ${testError}`)
      }
    } catch (dbError) {
      throw new Error(`Failed to open Chaterm database at ${Chaterm_DB_PATH}: ${dbError}`)
    }

    return db
  } catch (error) {
    console.error(`[DB-Chaterm] Chaterm database initialization failed for user ${targetUserId}:`, {
      error: error instanceof Error ? error.message : String(error),
      targetUserId,
      dbPath: Chaterm_DB_PATH,
      initDbPath: INIT_CDB_PATH,
      userDataPath: USER_DATA_PATH
    })

    // 清理可能的不完整文件
    try {
      if (fs.existsSync(Chaterm_DB_PATH)) {
        const stats = fs.statSync(Chaterm_DB_PATH)
        if (stats.size === 0) {
          fs.unlinkSync(Chaterm_DB_PATH)
          console.log(`[DB-Chaterm] Cleaned up empty database file: ${Chaterm_DB_PATH}`)
        }
      }
    } catch (cleanupError) {
      console.warn(`[DB-Chaterm] Failed to cleanup after initialization error:`, cleanupError)
    }

    throw error
  }
}
