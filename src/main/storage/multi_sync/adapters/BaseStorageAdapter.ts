/**
 * 多存储后端数据同步系统 - 基础存储适配器
 * 功能：提供存储适配器的基础实现和通用方法
 * 依赖：fs、path、crypto
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import * as fs from 'fs'
import * as path from 'path'
import * as crypto from 'crypto'
import { IStorageAdapter, StorageConfig, FileInfo, FileMetadata, SyncResult, ConflictInfo, StorageType } from '../types/StorageTypes'

/**
 * 基础存储适配器抽象类
 * 提供通用的文件操作和同步逻辑
 */
export abstract class BaseStorageAdapter implements IStorageAdapter {
  protected connected: boolean = false
  protected config: StorageConfig | null = null

  /**
   * 连接到存储服务
   * @param config 存储配置
   * @returns 连接是否成功
   */
  abstract connect(config: StorageConfig): Promise<boolean>

  /**
   * 断开连接
   */
  abstract disconnect(): Promise<void>

  /**
   * 测试连接
   * @returns 连接是否正常
   */
  abstract testConnection(): Promise<boolean>

  /**
   * 上传文件
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  abstract uploadFile(localPath: string, remotePath: string): Promise<void>

  /**
   * 下载文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  abstract downloadFile(remotePath: string, localPath: string): Promise<void>

  /**
   * 删除文件
   * @param remotePath 远程文件路径
   */
  abstract deleteFile(remotePath: string): Promise<void>

  /**
   * 列出文件
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  abstract listFiles(remotePath?: string): Promise<FileInfo[]>

  /**
   * 获取文件元数据
   * @param remotePath 远程文件路径
   * @returns 文件元数据
   */
  abstract getFileMetadata(remotePath: string): Promise<FileMetadata>

  /**
   * 获取存储类型
   * @returns 存储类型
   */
  abstract getType(): StorageType

  /**
   * 同步文件夹
   * @param localPath 本地文件夹路径
   * @param remotePath 远程文件夹路径
   * @returns 同步结果
   */
  async sync(localPath: string, remotePath: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: false,
      message: '',
      filesUploaded: 0,
      filesDownloaded: 0,
      filesDeleted: 0,
      errors: [],
      conflicts: []
    }

    try {
      if (!this.connected) {
        throw new Error('存储适配器未连接')
      }

      // 获取本地文件列表
      const localFiles = await this.getLocalFiles(localPath)

      // 获取远程文件列表
      const remoteFiles = await this.listFiles(remotePath)

      // 检测冲突
      const conflicts = await this.detectConflicts(localFiles, remoteFiles)
      if (conflicts.length > 0) {
        return {
          success: false,
          message: `检测到 ${conflicts.length} 个冲突，需要手动解决`,
          filesUploaded: 0,
          filesDownloaded: 0,
          filesDeleted: 0,
          conflicts,
          errors: []
        }
      }

      // 上传新文件和修改的文件
      for (const localFile of localFiles) {
        const remoteFile = remoteFiles.find((f) => f.name === localFile.name)

        if (!remoteFile || localFile.lastModified > remoteFile.lastModified) {
          try {
            const localFilePath = path.join(localPath, localFile.name)
            const remoteFilePath = this.joinRemotePath(remotePath, localFile.name)

            await this.uploadFile(localFilePath, remoteFilePath)
            result.filesUploaded++
          } catch (error: any) {
            result.errors.push(`上传文件 ${localFile.name} 失败: ${error?.message || error}`)
          }
        }
      }

      // 下载远程新文件
      for (const remoteFile of remoteFiles) {
        const localFile = localFiles.find((f) => f.name === remoteFile.name)

        if (!localFile) {
          try {
            const localFilePath = path.join(localPath, remoteFile.name)
            const remoteFilePath = this.joinRemotePath(remotePath, remoteFile.name)

            await this.downloadFile(remoteFilePath, localFilePath)
            result.filesDownloaded++
          } catch (error: any) {
            result.errors.push(`下载文件 ${remoteFile.name} 失败: ${error?.message || error}`)
          }
        }
      }

      result.success = result.errors.length === 0
      result.message = result.success ? '同步完成' : `同步完成，但有 ${result.errors.length} 个错误`
    } catch (error: any) {
      result.errors.push(`同步失败: ${error?.message || '未知错误'}`)
      result.message = '同步失败'
    }

    return result
  }

  /**
   * 获取本地文件列表
   * @param localPath 本地目录路径
   * @returns 本地文件列表
   */
  protected async getLocalFiles(localPath: string): Promise<FileInfo[]> {
    const files: FileInfo[] = []

    if (!fs.existsSync(localPath)) {
      return files
    }

    const entries = fs.readdirSync(localPath, { withFileTypes: true })

    for (const entry of entries) {
      if (entry.isFile()) {
        const filePath = path.join(localPath, entry.name)
        const stats = fs.statSync(filePath)

        files.push({
          name: entry.name,
          path: filePath,
          size: stats.size,
          lastModified: stats.mtime,
          isDirectory: false,
          checksum: await this.calculateFileChecksum(filePath)
        })
      }
    }

    return files
  }

  /**
   * 计算文件校验和
   * @param filePath 文件路径
   * @returns 文件校验和
   */
  protected async calculateFileChecksum(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('md5')
      const stream = fs.createReadStream(filePath)

      stream.on('data', (data) => hash.update(data))
      stream.on('end', () => resolve(hash.digest('hex')))
      stream.on('error', reject)
    })
  }

  /**
   * 检测文件冲突
   * @param localFiles 本地文件列表
   * @param remoteFiles 远程文件列表
   * @param localFiles 本地文件列表
   * @param remoteFiles 远程文件列表
   * @returns 冲突列表
   */
  protected async detectConflicts(localFiles: FileInfo[], remoteFiles: FileInfo[]): Promise<ConflictInfo[]> {
    const conflicts: ConflictInfo[] = []

    for (const localFile of localFiles) {
      const remoteFile = remoteFiles.find((f) => f.name === localFile.name)

      if (remoteFile) {
        // 检查是否同时修改
        if (localFile.lastModified !== remoteFile.lastModified && localFile.checksum !== remoteFile.checksum) {
          const localMetadata: FileMetadata = {
            name: localFile.name,
            path: localFile.path,
            size: localFile.size,
            lastModified: localFile.lastModified,
            checksum: localFile.checksum
          }

          const remoteMetadata: FileMetadata = {
            name: remoteFile.name,
            path: remoteFile.path,
            size: remoteFile.size,
            lastModified: remoteFile.lastModified,
            checksum: remoteFile.checksum
          }

          conflicts.push({
            filePath: localFile.name,
            localMetadata,
            remoteMetadata,
            conflictType: 'modified'
          })
        }
      }
    }

    return conflicts
  }

  /**
   * 连接远程路径
   * @param basePath 基础路径
   * @param fileName 文件名
   * @returns 完整的远程路径
   */
  protected joinRemotePath(basePath: string, fileName: string): string {
    if (!basePath) return fileName
    return basePath.endsWith('/') ? basePath + fileName : basePath + '/' + fileName
  }

  /**
   * 确保目录存在
   * @param dirPath 目录路径
   */
  protected ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true })
    }
  }

  /**
   * 检查是否已连接
   * @throws 如果未连接则抛出错误
   */
  protected checkConnection(): void {
    if (!this.connected) {
      throw new Error('存储适配器未连接，请先调用 connect() 方法')
    }
  }
}
