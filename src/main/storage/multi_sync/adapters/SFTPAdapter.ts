/**
 * 多存储后端数据同步系统 - SFTP 存储适配器
 * 功能：实现 SFTP 文件传输的文件操作和同步
 * 依赖：ssh2-sftp-client、fs、path
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import * as SftpClient from 'ssh2-sftp-client'
import * as fs from 'fs'
import * as path from 'path'
import { BaseStorageAdapter } from './BaseStorageAdapter'
import { StorageConfig, StorageType, FileInfo, FileMetadata, SFTPConfig } from '../types/StorageTypes'

/**
 * SFTP 存储适配器
 * 实现 SFTP 协议的文件操作和同步功能
 */
export class SFTPAdapter extends BaseStorageAdapter {
  private client: SftpClient | null = null
  private sftpConfig: SFTPConfig | null = null

  /**
   * 连接到 SFTP 服务
   * @param config SFTP 配置
   * @returns 连接是否成功
   */
  async connect(config: StorageConfig): Promise<boolean> {
    try {
      this.sftpConfig = config as SFTPConfig

      if (!this.sftpConfig.host || !this.sftpConfig.username) {
        throw new Error('SFTP 主机地址和用户名必须提供')
      }

      if (!this.sftpConfig.password && !this.sftpConfig.privateKey) {
        throw new Error('SFTP 密码或私钥必须提供其中一个')
      }

      // 初始化 SFTP 客户端
      this.client = new SftpClient()

      // 构建连接配置
      const connectConfig: any = {
        host: this.sftpConfig.host,
        port: this.sftpConfig.port || 22,
        username: this.sftpConfig.username
      }

      // 添加认证信息
      if (this.sftpConfig.password) {
        connectConfig.password = this.sftpConfig.password
      }

      if (this.sftpConfig.privateKey) {
        connectConfig.privateKey = this.sftpConfig.privateKey
        if (this.sftpConfig.passphrase) {
          connectConfig.passphrase = this.sftpConfig.passphrase
        }
      }

      // 连接到 SFTP 服务器
      await this.client.connect(connectConfig)

      // 测试连接 - 尝试列出根目录
      await this.client.list(this.sftpConfig.path || '/')

      this.connected = true
      this.config = config
      return true
    } catch (error) {
      console.error('SFTP 连接失败:', error)
      this.connected = false
      if (this.client) {
        await this.client.end()
        this.client = null
      }
      return false
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      try {
        await this.client.end()
      } catch (error) {
        console.error('SFTP 断开连接失败:', error)
      }
      this.client = null
    }
    this.sftpConfig = null
    this.connected = false
    this.config = null
  }

  /**
   * 测试连接
   * @returns 连接是否正常
   */
  async testConnection(): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client || !this.sftpConfig) {
        return false
      }

      // 尝试列出目录
      await this.client.list(this.sftpConfig.path || '/')
      return true
    } catch (error) {
      console.error('SFTP 连接测试失败:', error)
      return false
    }
  }

  /**
   * 上传文件到 SFTP
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  async uploadFile(localPath: string, remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.sftpConfig) {
        throw new Error('SFTP 客户端未初始化')
      }

      if (!fs.existsSync(localPath)) {
        throw new Error(`本地文件不存在: ${localPath}`)
      }

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 确保远程目录存在
      const remoteDir = path.dirname(fullRemotePath)
      await this.ensureRemoteDirectoryExists(remoteDir)

      // 上传文件
      await this.client.put(localPath, fullRemotePath)
    } catch (error: any) {
      throw new Error(`SFTP 文件上传失败: ${error?.message || error}`)
    }
  }

  /**
   * 从 SFTP 下载文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  async downloadFile(remotePath: string, localPath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.sftpConfig) {
        throw new Error('SFTP 客户端未初始化')
      }

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 确保本地目录存在
      this.ensureDirectoryExists(path.dirname(localPath))

      // 下载文件
      await this.client.get(fullRemotePath, localPath)
    } catch (error: any) {
      throw new Error(`SFTP 文件下载失败: ${error?.message || error}`)
    }
  }

  /**
   * 删除 SFTP 文件
   * @param remotePath 远程文件路径
   */
  async deleteFile(remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.sftpConfig) {
        throw new Error('SFTP 客户端未初始化')
      }

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 删除文件
      await this.client.delete(fullRemotePath)
    } catch (error: any) {
      throw new Error(`SFTP 文件删除失败: ${error?.message || error}`)
    }
  }

  /**
   * 列出 SFTP 文件
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  async listFiles(remotePath?: string): Promise<FileInfo[]> {
    try {
      this.checkConnection()

      if (!this.client || !this.sftpConfig) {
        throw new Error('SFTP 客户端未初始化')
      }

      // 构建远程目录路径
      const fullRemotePath = this.buildRemotePath(remotePath || '')

      // 列出文件
      const fileList = await this.client.list(fullRemotePath)
      const files: FileInfo[] = []

      for (const item of fileList) {
        if (item.type === '-') {
          // 只处理文件，不处理目录
          files.push({
            name: item.name,
            path: remotePath ? `${remotePath}/${item.name}` : item.name,
            size: item.size || 0,
            lastModified: new Date(item.modifyTime || Date.now()),
            isDirectory: false
          })
        }
      }

      return files
    } catch (error: any) {
      throw new Error(`SFTP 文件列表获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取 SFTP 文件元数据
   * @param remotePath 远程文件路径
   * @returns 文件元数据
   */
  async getFileMetadata(remotePath: string): Promise<FileMetadata> {
    try {
      this.checkConnection()

      if (!this.client || !this.sftpConfig) {
        throw new Error('SFTP 客户端未初始化')
      }

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 获取文件统计信息
      const stats = await this.client.stat(fullRemotePath)

      return {
        name: path.basename(remotePath),
        path: remotePath,
        size: stats.size || 0,
        lastModified: new Date(stats.mtime || Date.now())
      }
    } catch (error: any) {
      throw new Error(`SFTP 文件元数据获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取存储类型
   * @returns 存储类型
   */
  getType(): StorageType {
    return StorageType.SFTP
  }

  /**
   * 构建远程路径
   * @param relativePath 相对路径
   * @returns 完整的远程路径
   */
  private buildRemotePath(relativePath: string): string {
    if (!this.sftpConfig?.path) {
      return relativePath || '/'
    }

    if (!relativePath) {
      return this.sftpConfig.path
    }

    // 确保路径正确连接
    const basePath = this.sftpConfig.path.endsWith('/') ? this.sftpConfig.path.slice(0, -1) : this.sftpConfig.path

    const relPath = relativePath.startsWith('/') ? relativePath : '/' + relativePath

    return basePath + relPath
  }

  /**
   * 确保远程目录存在
   * @param remoteDirPath 远程目录路径
   */
  private async ensureRemoteDirectoryExists(remoteDirPath: string): Promise<void> {
    try {
      if (!this.client) {
        throw new Error('SFTP 客户端未初始化')
      }

      // 检查目录是否存在
      try {
        await this.client.stat(remoteDirPath)
        return // 目录已存在
      } catch (error) {
        // 目录不存在，需要创建
      }

      // 递归创建目录
      await this.client.mkdir(remoteDirPath, true)
    } catch (error) {
      console.error('创建远程目录失败:', error)
      // 不抛出错误，因为目录可能已经存在
    }
  }

  /**
   * 检查文件是否存在
   * @param remotePath 远程文件路径
   * @returns 文件是否存在
   */
  async fileExists(remotePath: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      await this.client.stat(fullRemotePath)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 创建远程目录
   * @param remotePath 远程目录路径
   * @param recursive 是否递归创建
   * @returns 是否创建成功
   */
  async createDirectory(remotePath: string, recursive: boolean = true): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      await this.client.mkdir(fullRemotePath, recursive)
      return true
    } catch (error) {
      console.error('创建远程目录失败:', error)
      return false
    }
  }

  /**
   * 删除远程目录
   * @param remotePath 远程目录路径
   * @param recursive 是否递归删除
   * @returns 是否删除成功
   */
  async deleteDirectory(remotePath: string, recursive: boolean = false): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)

      if (recursive) {
        await this.client.rmdir(fullRemotePath, true)
      } else {
        await this.client.rmdir(fullRemotePath)
      }

      return true
    } catch (error) {
      console.error('删除远程目录失败:', error)
      return false
    }
  }

  /**
   * 获取文件权限
   * @param remotePath 远程文件路径
   * @returns 文件权限（八进制字符串）
   */
  async getFilePermissions(remotePath: string): Promise<string> {
    try {
      this.checkConnection()

      if (!this.client) {
        throw new Error('SFTP 客户端未初始化')
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      const stats = await this.client.stat(fullRemotePath)

      // 将权限转换为八进制字符串
      return (stats.mode & parseInt('777', 8)).toString(8)
    } catch (error: any) {
      throw new Error(`获取文件权限失败: ${error?.message || error}`)
    }
  }

  /**
   * 设置文件权限
   * @param remotePath 远程文件路径
   * @param permissions 权限（八进制字符串，如 '755'）
   * @returns 是否设置成功
   */
  async setFilePermissions(remotePath: string, permissions: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      const mode = parseInt(permissions, 8)

      await this.client.chmod(fullRemotePath, mode)
      return true
    } catch (error) {
      console.error('设置文件权限失败:', error)
      return false
    }
  }
}
