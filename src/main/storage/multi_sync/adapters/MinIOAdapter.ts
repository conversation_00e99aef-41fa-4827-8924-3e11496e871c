/**
 * 多存储后端数据同步系统 - MinIO 存储适配器
 * 功能：实现 MinIO 对象存储的文件操作和同步
 * 依赖：minio、fs、path
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import * as Minio from 'minio'
import * as fs from 'fs'
import * as path from 'path'
import { BaseStorageAdapter } from './BaseStorageAdapter'
import { StorageConfig, StorageType, FileInfo, FileMetadata, MinIOConfig } from '../types/StorageTypes'

/**
 * MinIO 存储适配器
 * 实现 MinIO S3 兼容 API 的文件操作和同步功能
 */
export class MinIOAdapter extends BaseStorageAdapter {
  private client: Minio.Client | null = null
  private minioConfig: MinIOConfig | null = null

  /**
   * 连接到 MinIO 服务
   * @param config MinIO 配置
   * @returns 连接是否成功
   */
  async connect(config: StorageConfig): Promise<boolean> {
    try {
      this.minioConfig = config as MinIOConfig

      if (!this.minioConfig.endpoint || !this.minioConfig.accessKey || !this.minioConfig.secretKey) {
        throw new Error('MinIO 端点、访问密钥和秘密密钥必须提供')
      }

      if (!this.minioConfig.bucket) {
        throw new Error('MinIO 存储桶名称必须提供')
      }

      // 初始化 MinIO 客户端
      this.client = new Minio.Client({
        endPoint: this.minioConfig.endpoint,
        port: this.minioConfig.port || (this.minioConfig.useSSL ? 443 : 80),
        useSSL: this.minioConfig.useSSL || false,
        accessKey: this.minioConfig.accessKey,
        secretKey: this.minioConfig.secretKey,
        region: this.minioConfig.region || 'us-east-1'
      })

      // 测试连接 - 检查存储桶是否存在
      const bucketExists = await this.client.bucketExists(this.minioConfig.bucket)
      if (!bucketExists) {
        // 尝试创建存储桶
        await this.client.makeBucket(this.minioConfig.bucket, this.minioConfig.region || 'us-east-1')
      }

      this.connected = true
      this.config = config
      return true
    } catch (error) {
      console.error('MinIO 连接失败:', error)
      this.connected = false
      return false
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    this.client = null
    this.minioConfig = null
    this.connected = false
    this.config = null
  }

  /**
   * 测试连接
   * @returns 连接是否正常
   */
  async testConnection(): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client || !this.minioConfig) {
        return false
      }

      // 尝试列出存储桶
      await this.client.bucketExists(this.minioConfig.bucket)
      return true
    } catch (error) {
      console.error('MinIO 连接测试失败:', error)
      return false
    }
  }

  /**
   * 上传文件到 MinIO
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  async uploadFile(localPath: string, remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.minioConfig) {
        throw new Error('MinIO 客户端未初始化')
      }

      if (!fs.existsSync(localPath)) {
        throw new Error(`本地文件不存在: ${localPath}`)
      }

      // 构建对象名称
      const objectName = this.buildObjectName(remotePath)

      // 获取文件统计信息
      const stats = fs.statSync(localPath)

      // 上传文件
      await this.client.fPutObject(this.minioConfig.bucket, objectName, localPath, {
        'Content-Type': this.getContentType(localPath),
        'Last-Modified': stats.mtime.toISOString()
      })
    } catch (error: any) {
      throw new Error(`MinIO 文件上传失败: ${error?.message || error}`)
    }
  }

  /**
   * 从 MinIO 下载文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  async downloadFile(remotePath: string, localPath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.minioConfig) {
        throw new Error('MinIO 客户端未初始化')
      }

      // 构建对象名称
      const objectName = this.buildObjectName(remotePath)

      // 确保本地目录存在
      this.ensureDirectoryExists(path.dirname(localPath))

      // 下载文件
      await this.client.fGetObject(this.minioConfig.bucket, objectName, localPath)
    } catch (error: any) {
      throw new Error(`MinIO 文件下载失败: ${error?.message || error}`)
    }
  }

  /**
   * 删除 MinIO 文件
   * @param remotePath 远程文件路径
   */
  async deleteFile(remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.minioConfig) {
        throw new Error('MinIO 客户端未初始化')
      }

      // 构建对象名称
      const objectName = this.buildObjectName(remotePath)

      // 删除对象
      await this.client.removeObject(this.minioConfig.bucket, objectName)
    } catch (error: any) {
      throw new Error(`MinIO 文件删除失败: ${error?.message || error}`)
    }
  }

  /**
   * 列出 MinIO 文件
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  async listFiles(remotePath?: string): Promise<FileInfo[]> {
    try {
      this.checkConnection()

      if (!this.client || !this.minioConfig) {
        throw new Error('MinIO 客户端未初始化')
      }

      const files: FileInfo[] = []
      const prefix = remotePath ? this.buildObjectName(remotePath) + '/' : ''

      // 列出对象
      const objectsStream = this.client.listObjects(this.minioConfig.bucket, prefix, false)

      return new Promise((resolve, reject) => {
        objectsStream.on('data', (obj) => {
          if (obj.name && !obj.name.endsWith('/')) {
            // 排除目录
            const fileName = path.basename(obj.name)
            const filePath = remotePath ? `${remotePath}/${fileName}` : fileName

            files.push({
              name: fileName,
              path: filePath,
              size: obj.size || 0,
              lastModified: obj.lastModified || new Date(),
              isDirectory: false,
              etag: obj.etag
            })
          }
        })

        objectsStream.on('end', () => {
          resolve(files)
        })

        objectsStream.on('error', (error) => {
          reject(new Error(`MinIO 文件列表获取失败: ${error.message}`))
        })
      })
    } catch (error: any) {
      throw new Error(`MinIO 文件列表获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取 MinIO 文件元数据
   * @param remotePath 远程文件路径
   * @returns 文件元数据
   */
  async getFileMetadata(remotePath: string): Promise<FileMetadata> {
    try {
      this.checkConnection()

      if (!this.client || !this.minioConfig) {
        throw new Error('MinIO 客户端未初始化')
      }

      // 构建对象名称
      const objectName = this.buildObjectName(remotePath)

      // 获取对象统计信息
      const stat = await this.client.statObject(this.minioConfig.bucket, objectName)

      return {
        name: path.basename(remotePath),
        path: remotePath,
        size: stat.size || 0,
        lastModified: stat.lastModified || new Date(),
        contentType: stat.metaData?.['content-type'],
        etag: stat.etag,
        version: stat.versionId || undefined
      }
    } catch (error: any) {
      throw new Error(`MinIO 文件元数据获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取存储类型
   * @returns 存储类型
   */
  getType(): StorageType {
    return StorageType.MINIO
  }

  /**
   * 构建对象名称
   * @param remotePath 远程路径
   * @returns 对象名称
   */
  private buildObjectName(remotePath: string): string {
    // 移除开头的斜杠，MinIO 对象名称不应该以斜杠开头
    return remotePath.startsWith('/') ? remotePath.substring(1) : remotePath
  }

  /**
   * 根据文件扩展名获取内容类型
   * @param filePath 文件路径
   * @returns 内容类型
   */
  private getContentType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase()
    const contentTypes: { [key: string]: string } = {
      '.txt': 'text/plain',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.pdf': 'application/pdf',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.zip': 'application/zip',
      '.tar': 'application/x-tar',
      '.gz': 'application/gzip'
    }

    return contentTypes[ext] || 'application/octet-stream'
  }

  /**
   * 创建存储桶
   * @param bucketName 存储桶名称
   * @param region 区域
   * @returns 是否创建成功
   */
  async createBucket(bucketName: string, region?: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        throw new Error('MinIO 客户端未初始化')
      }

      const bucketExists = await this.client.bucketExists(bucketName)
      if (bucketExists) {
        return true // 存储桶已存在
      }

      await this.client.makeBucket(bucketName, region || 'us-east-1')
      return true
    } catch (error) {
      console.error('创建存储桶失败:', error)
      return false
    }
  }

  /**
   * 删除存储桶
   * @param bucketName 存储桶名称
   * @returns 是否删除成功
   */
  async deleteBucket(bucketName: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        throw new Error('MinIO 客户端未初始化')
      }

      await this.client.removeBucket(bucketName)
      return true
    } catch (error) {
      console.error('删除存储桶失败:', error)
      return false
    }
  }

  /**
   * 获取预签名 URL
   * @param remotePath 远程文件路径
   * @param expiry 过期时间（秒）
   * @returns 预签名 URL
   */
  async getPresignedUrl(remotePath: string, expiry: number = 3600): Promise<string> {
    try {
      this.checkConnection()

      if (!this.client || !this.minioConfig) {
        throw new Error('MinIO 客户端未初始化')
      }

      const objectName = this.buildObjectName(remotePath)

      return await this.client.presignedGetObject(this.minioConfig.bucket, objectName, expiry)
    } catch (error: any) {
      throw new Error(`获取预签名 URL 失败: ${error?.message || error}`)
    }
  }

  /**
   * 批量删除文件
   */
  async batchDeleteFiles(remotePaths: string[]): Promise<{
    success: string[]
    failed: string[]
  }> {
    const result: {
      success: string[]
      failed: string[]
    } = {
      success: [],
      failed: []
    }

    try {
      const objectNames = remotePaths.map((path) => this.buildObjectName(path))
      await this.client?.removeObjects(this.minioConfig?.bucket || '', objectNames)
      result.success = remotePaths
    } catch (error) {
      result.failed = remotePaths
    }

    return result
  }
}
