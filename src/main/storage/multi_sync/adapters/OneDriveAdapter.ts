/**
 * 多存储后端数据同步系统 - OneDrive 存储适配器
 * 功能：实现 OneDrive 存储服务的文件操作和同步
 * 依赖：@microsoft/microsoft-graph-client、fs、path
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { Client } from '@microsoft/microsoft-graph-client'
import * as fs from 'fs'
import * as path from 'path'
import { BaseStorageAdapter } from './BaseStorageAdapter'
import { StorageConfig, StorageType, FileInfo, FileMetadata, OneDriveConfig } from '../types/StorageTypes'

/**
 * OneDrive 存储适配器
 * 实现 OneDrive API 的文件操作和同步功能
 */
export class OneDriveAdapter extends BaseStorageAdapter {
  private client: Client | null = null
  private oneDriveConfig: OneDriveConfig | null = null

  /**
   * 连接到 OneDrive 服务
   * @param config OneDrive 配置
   * @returns 连接是否成功
   */
  async connect(config: StorageConfig): Promise<boolean> {
    try {
      this.oneDriveConfig = config as OneDriveConfig

      if (!this.oneDriveConfig.accessToken) {
        throw new Error('OneDrive 访问令牌未提供')
      }

      // 初始化 Microsoft Graph 客户端
      this.client = Client.init({
        authProvider: {
          getAccessToken: async () => {
            return this.oneDriveConfig!.accessToken!
          }
        } as any
      })

      // 测试连接
      await this.client.api('/me/drive').get()

      this.connected = true
      this.config = config
      return true
    } catch (error) {
      console.error('OneDrive 连接失败:', error)
      this.connected = false
      return false
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    this.client = null
    this.oneDriveConfig = null
    this.connected = false
    this.config = null
  }

  /**
   * 测试连接
   * @returns 连接是否正常
   */
  async testConnection(): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      // 尝试获取用户信息
      await this.client.api('/me').get()
      return true
    } catch (error) {
      console.error('OneDrive 连接测试失败:', error)
      return false
    }
  }

  /**
   * 上传文件到 OneDrive
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  async uploadFile(localPath: string, remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client) {
        throw new Error('OneDrive 客户端未初始化')
      }

      if (!fs.existsSync(localPath)) {
        throw new Error(`本地文件不存在: ${localPath}`)
      }

      const fileStream = fs.createReadStream(localPath)
      const fileName = path.basename(remotePath)
      const folderPath = this.getFolderPath(remotePath)

      // 构建上传路径
      const uploadPath = folderPath ? `/me/drive/root:/${folderPath}/${fileName}:/content` : `/me/drive/root:/${fileName}:/content`

      await this.client.api(uploadPath).put(fileStream)
    } catch (error: any) {
      throw new Error(`OneDrive 文件上传失败: ${error?.message || error}`)
    }
  }

  /**
   * 从 OneDrive 下载文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  async downloadFile(remotePath: string, localPath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client) {
        throw new Error('OneDrive 客户端未初始化')
      }

      const fileName = path.basename(remotePath)
      const folderPath = this.getFolderPath(remotePath)

      // 构建下载路径
      const downloadPath = folderPath ? `/me/drive/root:/${folderPath}/${fileName}:/content` : `/me/drive/root:/${fileName}:/content`

      const response = await this.client.api(downloadPath).get()

      // 确保本地目录存在
      this.ensureDirectoryExists(path.dirname(localPath))

      // 写入文件
      fs.writeFileSync(localPath, response)
    } catch (error: any) {
      throw new Error(`OneDrive 文件下载失败: ${error?.message || error}`)
    }
  }

  /**
   * 删除 OneDrive 文件
   * @param remotePath 远程文件路径
   */
  async deleteFile(remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client) {
        throw new Error('OneDrive 客户端未初始化')
      }

      const fileName = path.basename(remotePath)
      const folderPath = this.getFolderPath(remotePath)

      // 构建删除路径
      const deletePath = folderPath ? `/me/drive/root:/${folderPath}/${fileName}` : `/me/drive/root:/${fileName}`

      await this.client.api(deletePath).delete()
    } catch (error: any) {
      throw new Error(`OneDrive 文件删除失败: ${error?.message || error}`)
    }
  }

  /**
   * 列出 OneDrive 文件
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  async listFiles(remotePath?: string): Promise<FileInfo[]> {
    try {
      this.checkConnection()

      if (!this.client) {
        throw new Error('OneDrive 客户端未初始化')
      }

      // 构建列表路径
      const listPath = remotePath ? `/me/drive/root:/${remotePath}:/children` : '/me/drive/root/children'

      const response = await this.client.api(listPath).get()
      const files: FileInfo[] = []

      if (response.value) {
        for (const item of response.value) {
          if (item.file) {
            // 只处理文件，不处理文件夹
            files.push({
              name: item.name,
              path: remotePath ? `${remotePath}/${item.name}` : item.name,
              size: item.size || 0,
              lastModified: new Date(item.lastModifiedDateTime),
              isDirectory: false,
              etag: item.eTag
            })
          }
        }
      }

      return files
    } catch (error: any) {
      throw new Error(`OneDrive 文件列表获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取 OneDrive 文件元数据
   * @param remotePath 远程文件路径
   * @returns 文件元数据
   */
  async getFileMetadata(remotePath: string): Promise<FileMetadata> {
    try {
      this.checkConnection()

      if (!this.client) {
        throw new Error('OneDrive 客户端未初始化')
      }

      const fileName = path.basename(remotePath)
      const folderPath = this.getFolderPath(remotePath)

      // 构建元数据路径
      const metadataPath = folderPath ? `/me/drive/root:/${folderPath}/${fileName}` : `/me/drive/root:/${fileName}`

      const response = await this.client.api(metadataPath).get()

      return {
        name: response.name,
        path: remotePath,
        size: response.size || 0,
        lastModified: new Date(response.lastModifiedDateTime),
        contentType: response.file?.mimeType,
        etag: response.eTag,
        version: response.eTag
      }
    } catch (error: any) {
      throw new Error(`OneDrive 文件元数据获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取存储类型
   * @returns 存储类型
   */
  getType(): StorageType {
    return StorageType.ONEDRIVE
  }

  /**
   * 从完整路径中提取文件夹路径
   * @param fullPath 完整路径
   * @returns 文件夹路径
   */
  private getFolderPath(fullPath: string): string {
    const folderPath = path.dirname(fullPath)
    return folderPath === '.' ? '' : folderPath
  }

  /**
   * 刷新访问令牌
   * @returns 是否刷新成功
   */
  async refreshAccessToken(): Promise<boolean> {
    try {
      if (!this.oneDriveConfig?.refreshToken) {
        return false
      }

      // 这里应该实现 OAuth 令牌刷新逻辑
      // 由于需要客户端密钥等敏感信息，建议在主进程中实现
      console.warn('OneDrive 令牌刷新需要在主进程中实现')
      return false
    } catch (error) {
      console.error('OneDrive 令牌刷新失败:', error)
      return false
    }
  }
}
