/**
 * 多存储后端数据同步系统 - SMB 存储适配器
 * 功能：实现 SMB 网络共享的文件操作和同步
 * 依赖：node-smb2、fs、path
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

const SMB2 = require('node-smb2')
import * as fs from 'fs'
import * as path from 'path'
import { BaseStorageAdapter } from './BaseStorageAdapter'
import { StorageConfig, StorageType, FileInfo, FileMetadata, SMBConfig } from '../types/StorageTypes'

/**
 * SMB 存储适配器
 * 实现 SMB 协议的文件操作和同步功能
 */
export class SMBAdapter extends BaseStorageAdapter {
  private client: any = null
  private smbConfig: SMBConfig | null = null

  /**
   * 连接到 SMB 服务
   * @param config SMB 配置
   * @returns 连接是否成功
   */
  async connect(config: StorageConfig): Promise<boolean> {
    try {
      this.smbConfig = config as SMBConfig

      if (!this.smbConfig.host || !this.smbConfig.username || !this.smbConfig.password) {
        throw new Error('SMB 主机地址、用户名和密码必须提供')
      }

      if (!this.smbConfig.share) {
        throw new Error('SMB 共享名称必须提供')
      }

      // 初始化 SMB2 客户端
      this.client = SMB2({
        share: `\\\\${this.smbConfig.host}\\${this.smbConfig.share}`,
        domain: this.smbConfig.domain || 'WORKGROUP',
        username: this.smbConfig.username,
        password: this.smbConfig.password,
        port: this.smbConfig.port || 445,
        packetConcurrency: 20,
        autoCloseTimeout: 0
      })

      // 测试连接 - 尝试列出根目录
      await this.testSMBConnection()

      this.connected = true
      this.config = config
      return true
    } catch (error) {
      console.error('SMB 连接失败:', error)
      this.connected = false
      if (this.client) {
        this.client.disconnect()
        this.client = null
      }
      return false
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      try {
        this.client.disconnect()
      } catch (error) {
        console.error('SMB 断开连接失败:', error)
      }
      this.client = null
    }
    this.smbConfig = null
    this.connected = false
    this.config = null
  }

  /**
   * 测试连接
   * @returns 连接是否正常
   */
  async testConnection(): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      await this.testSMBConnection()
      return true
    } catch (error) {
      console.error('SMB 连接测试失败:', error)
      return false
    }
  }

  /**
   * 测试 SMB 连接
   */
  private async testSMBConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      const testPath = this.smbConfig?.path || '/'

      this.client.readdir(testPath, (err: any, _files: any) => {
        if (err) {
          reject(new Error(`SMB 连接测试失败: ${err.message}`))
        } else {
          resolve()
        }
      })
    })
  }

  /**
   * 上传文件到 SMB
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  async uploadFile(localPath: string, remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      if (!fs.existsSync(localPath)) {
        throw new Error(`本地文件不存在: ${localPath}`)
      }

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 确保远程目录存在
      const remoteDir = path.dirname(fullRemotePath)
      await this.ensureRemoteDirectoryExists(remoteDir)

      // 上传文件
      await this.smbWriteFile(localPath, fullRemotePath)
    } catch (error: any) {
      throw new Error(`SMB 文件上传失败: ${error?.message || error}`)
    }
  }

  /**
   * 从 SMB 下载文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  async downloadFile(remotePath: string, localPath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 确保本地目录存在
      this.ensureDirectoryExists(path.dirname(localPath))

      // 下载文件
      await this.smbReadFile(fullRemotePath, localPath)
    } catch (error: any) {
      throw new Error(`SMB 文件下载失败: ${error?.message || error}`)
    }
  }

  /**
   * 删除 SMB 文件
   * @param remotePath 远程文件路径
   */
  async deleteFile(remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 删除文件
      await this.smbUnlink(fullRemotePath)
    } catch (error: any) {
      throw new Error(`SMB 文件删除失败: ${error?.message || error}`)
    }
  }

  /**
   * 列出 SMB 文件
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  async listFiles(remotePath?: string): Promise<FileInfo[]> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      // 构建远程目录路径
      const fullRemotePath = this.buildRemotePath(remotePath || '')

      // 列出文件
      const fileList = await this.smbReaddir(fullRemotePath)
      const files: FileInfo[] = []

      for (const item of fileList) {
        if (!item.Directory) {
          // 只处理文件，不处理目录
          files.push({
            name: item.Filename,
            path: remotePath ? `${remotePath}/${item.Filename}` : item.Filename,
            size: item.EndOfFile || 0,
            lastModified: new Date(item.LastWriteTime || Date.now()),
            isDirectory: false
          })
        }
      }

      return files
    } catch (error: any) {
      throw new Error(`SMB 文件列表获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取 SMB 文件元数据
   * @param remotePath 远程文件路径
   * @returns 文件元数据
   */
  async getFileMetadata(remotePath: string): Promise<FileMetadata> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 获取文件统计信息
      const stats = await this.smbStat(fullRemotePath)

      return {
        name: path.basename(remotePath),
        path: remotePath,
        size: stats.EndOfFile || 0,
        lastModified: new Date(stats.LastWriteTime || Date.now())
      }
    } catch (error: any) {
      throw new Error(`SMB 文件元数据获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取存储类型
   * @returns 存储类型
   */
  getType(): StorageType {
    return StorageType.SMB
  }

  /**
   * 构建远程路径
   * @param relativePath 相对路径
   * @returns 完整的远程路径
   */
  private buildRemotePath(relativePath: string): string {
    if (!this.smbConfig?.path) {
      return relativePath || '/'
    }

    if (!relativePath) {
      return this.smbConfig.path
    }

    // 确保路径正确连接，SMB 使用反斜杠
    const basePath = this.smbConfig.path.replace(/\//g, '\\')
    const relPath = relativePath.replace(/\//g, '\\')

    return path.join(basePath, relPath).replace(/\//g, '\\')
  }

  /**
   * SMB 读取目录
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  private async smbReaddir(remotePath: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this.client.readdir(remotePath, (err: any, files: any) => {
        if (err) {
          reject(err)
        } else {
          resolve(files || [])
        }
      })
    })
  }

  /**
   * SMB 读取文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  private async smbReadFile(remotePath: string, localPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.readFile(remotePath, (err: any, data: Buffer) => {
        if (err) {
          reject(err)
        } else {
          try {
            fs.writeFileSync(localPath, data)
            resolve()
          } catch (writeErr) {
            reject(writeErr)
          }
        }
      })
    })
  }

  /**
   * SMB 写入文件
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  private async smbWriteFile(localPath: string, remotePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const data = fs.readFileSync(localPath)

      this.client.writeFile(remotePath, data, (err: any) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }

  /**
   * SMB 删除文件
   * @param remotePath 远程文件路径
   */
  private async smbUnlink(remotePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.unlink(remotePath, (err: any) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }

  /**
   * SMB 获取文件统计信息
   * @param remotePath 远程文件路径
   * @returns 文件统计信息
   */
  private async smbStat(remotePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.client.stat(remotePath, (err: any, stats: any) => {
        if (err) {
          reject(err)
        } else {
          resolve(stats)
        }
      })
    })
  }

  /**
   * 确保远程目录存在
   * @param remoteDirPath 远程目录路径
   */
  private async ensureRemoteDirectoryExists(remoteDirPath: string): Promise<void> {
    try {
      if (!this.client) {
        throw new Error('SMB 客户端未初始化')
      }

      // 检查目录是否存在
      try {
        await this.smbStat(remoteDirPath)
        return // 目录已存在
      } catch (error) {
        // 目录不存在，需要创建
      }

      // 创建目录
      await this.smbMkdir(remoteDirPath)
    } catch (error) {
      console.error('创建远程目录失败:', error)
      // 不抛出错误，因为目录可能已经存在
    }
  }

  /**
   * SMB 创建目录
   * @param remotePath 远程目录路径
   */
  private async smbMkdir(remotePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.mkdir(remotePath, (err: any) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }

  /**
   * 检查文件是否存在
   * @param remotePath 远程文件路径
   * @returns 文件是否存在
   */
  async fileExists(remotePath: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      await this.smbStat(fullRemotePath)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 创建远程目录
   * @param remotePath 远程目录路径
   * @returns 是否创建成功
   */
  async createDirectory(remotePath: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      await this.smbMkdir(fullRemotePath)
      return true
    } catch (error) {
      console.error('创建远程目录失败:', error)
      return false
    }
  }

  /**
   * 删除远程目录
   * @param remotePath 远程目录路径
   * @returns 是否删除成功
   */
  async deleteDirectory(remotePath: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      await this.smbRmdir(fullRemotePath)
      return true
    } catch (error) {
      console.error('删除远程目录失败:', error)
      return false
    }
  }

  /**
   * SMB 删除目录
   * @param remotePath 远程目录路径
   */
  private async smbRmdir(remotePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.rmdir(remotePath, (err: any) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }
}
