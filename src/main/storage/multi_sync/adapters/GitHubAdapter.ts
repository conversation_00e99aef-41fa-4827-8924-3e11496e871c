/**
 * 多存储后端数据同步系统 - GitHub 存储适配器
 * 功能：实现 GitHub 仓库的文件操作和同步
 * 依赖：@octokit/rest、fs、path
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { Octokit } from '@octokit/rest'
import * as fs from 'fs'
import * as path from 'path'
import { BaseStorageAdapter } from './BaseStorageAdapter'
import { StorageConfig, StorageType, FileInfo, FileMetadata, GitHubConfig } from '../types/StorageTypes'

/**
 * GitHub 存储适配器
 * 实现 GitHub API 的文件操作和同步功能
 */
export class GitHubAdapter extends BaseStorageAdapter {
  private octokit: Octokit | null = null
  private githubConfig: GitHubConfig | null = null

  /**
   * 连接到 GitHub 服务
   * @param config GitHub 配置
   * @returns 连接是否成功
   */
  async connect(config: StorageConfig): Promise<boolean> {
    try {
      this.githubConfig = config as GitHubConfig

      if (!this.githubConfig.token) {
        throw new Error('GitHub 访问令牌未提供')
      }

      if (!this.githubConfig.owner || !this.githubConfig.repo) {
        throw new Error('GitHub 仓库所有者和仓库名称必须提供')
      }

      // 初始化 Octokit 客户端
      this.octokit = new Octokit({
        auth: this.githubConfig.token
      })

      // 测试连接 - 尝试获取仓库信息
      await this.octokit.rest.repos.get({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo
      })

      this.connected = true
      this.config = config
      return true
    } catch (error: any) {
      console.error('GitHub 连接失败:', error)
      this.connected = false
      return false
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    this.octokit = null
    this.githubConfig = null
    this.connected = false
    this.config = null
  }

  /**
   * 测试连接
   * @returns 连接是否正常
   */
  async testConnection(): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.octokit || !this.githubConfig) {
        return false
      }

      // 尝试获取仓库信息
      await this.octokit.rest.repos.get({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo
      })

      return true
    } catch (error) {
      console.error('GitHub 连接测试失败:', error)
      return false
    }
  }

  /**
   * 上传文件到 GitHub
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  async uploadFile(localPath: string, remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.octokit || !this.githubConfig) {
        throw new Error('GitHub 客户端未初始化')
      }

      if (!fs.existsSync(localPath)) {
        throw new Error(`本地文件不存在: ${localPath}`)
      }

      // 读取文件内容
      const fileContent = fs.readFileSync(localPath)
      const base64Content = fileContent.toString('base64')

      // 构建文件路径
      const filePath = this.buildFilePath(remotePath)
      const branch = this.githubConfig.branch || 'main'

      try {
        // 尝试获取现有文件的 SHA（用于更新）
        const existingFile = await this.octokit.rest.repos.getContent({
          owner: this.githubConfig.owner,
          repo: this.githubConfig.repo,
          path: filePath,
          ref: branch
        })

        // 更新现有文件
        await this.octokit.rest.repos.createOrUpdateFileContents({
          owner: this.githubConfig.owner,
          repo: this.githubConfig.repo,
          path: filePath,
          message: `Update ${path.basename(remotePath)}`,
          content: base64Content,
          branch: branch,
          sha: Array.isArray(existingFile.data) ? undefined : existingFile.data.sha
        })
      } catch (error: any) {
        if (error.status === 404) {
          // 文件不存在，创建新文件
          await this.octokit.rest.repos.createOrUpdateFileContents({
            owner: this.githubConfig.owner,
            repo: this.githubConfig.repo,
            path: filePath,
            message: `Add ${path.basename(remotePath)}`,
            content: base64Content,
            branch: branch
          })
        } else {
          throw error
        }
      }
    } catch (error: any) {
      throw new Error(`GitHub 文件上传失败: ${error?.message || error}`)
    }
  }

  /**
   * 从 GitHub 下载文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  async downloadFile(remotePath: string, localPath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.octokit || !this.githubConfig) {
        throw new Error('GitHub 客户端未初始化')
      }

      // 构建文件路径
      const filePath = this.buildFilePath(remotePath)
      const branch = this.githubConfig.branch || 'main'

      const response = await this.octokit.rest.repos.getContent({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo,
        path: filePath,
        ref: branch
      })

      if (Array.isArray(response.data)) {
        throw new Error('指定路径是目录，不是文件')
      }

      if (response.data.type !== 'file') {
        throw new Error('指定路径不是文件')
      }

      // 解码 Base64 内容
      const content = Buffer.from(response.data.content, 'base64')

      // 确保本地目录存在
      this.ensureDirectoryExists(path.dirname(localPath))

      // 写入文件
      fs.writeFileSync(localPath, content)
    } catch (error: any) {
      throw new Error(`GitHub 文件下载失败: ${error?.message || error}`)
    }
  }

  /**
   * 删除 GitHub 文件
   * @param remotePath 远程文件路径
   */
  async deleteFile(remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.octokit || !this.githubConfig) {
        throw new Error('GitHub 客户端未初始化')
      }

      // 构建文件路径
      const filePath = this.buildFilePath(remotePath)
      const branch = this.githubConfig.branch || 'main'

      // 获取文件 SHA
      const fileResponse = await this.octokit.rest.repos.getContent({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo,
        path: filePath,
        ref: branch
      })

      if (Array.isArray(fileResponse.data) || fileResponse.data.type !== 'file') {
        throw new Error('指定路径不是文件')
      }

      // 删除文件
      await this.octokit.rest.repos.deleteFile({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo,
        path: filePath,
        message: `Delete ${path.basename(remotePath)}`,
        sha: fileResponse.data.sha,
        branch: branch
      })
    } catch (error: any) {
      throw new Error(`GitHub 文件删除失败: ${error?.message || error}`)
    }
  }

  /**
   * 列出 GitHub 文件
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  async listFiles(remotePath?: string): Promise<FileInfo[]> {
    try {
      this.checkConnection()

      if (!this.octokit || !this.githubConfig) {
        throw new Error('GitHub 客户端未初始化')
      }

      // 构建目录路径
      const dirPath = this.buildFilePath(remotePath || '')
      const branch = this.githubConfig.branch || 'main'

      const response = await this.octokit.rest.repos.getContent({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo,
        path: dirPath,
        ref: branch
      })

      const files: FileInfo[] = []

      if (Array.isArray(response.data)) {
        for (const item of response.data) {
          if (item.type === 'file') {
            files.push({
              name: item.name,
              path: remotePath ? `${remotePath}/${item.name}` : item.name,
              size: item.size || 0,
              lastModified: new Date(), // GitHub API 不提供修改时间，使用当前时间
              isDirectory: false,
              etag: item.sha
            })
          }
        }
      }

      return files
    } catch (error: any) {
      throw new Error(`GitHub 文件列表获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取 GitHub 文件元数据
   * @param remotePath 远程文件路径
   * @returns 文件元数据
   */
  async getFileMetadata(remotePath: string): Promise<FileMetadata> {
    try {
      this.checkConnection()

      if (!this.octokit || !this.githubConfig) {
        throw new Error('GitHub 客户端未初始化')
      }

      // 构建文件路径
      const filePath = this.buildFilePath(remotePath)
      const branch = this.githubConfig.branch || 'main'

      const response = await this.octokit.rest.repos.getContent({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo,
        path: filePath,
        ref: branch
      })

      if (Array.isArray(response.data) || response.data.type !== 'file') {
        throw new Error('指定路径不是文件')
      }

      return {
        name: response.data.name,
        path: remotePath,
        size: response.data.size || 0,
        lastModified: new Date(), // GitHub API 不提供修改时间
        etag: response.data.sha,
        version: response.data.sha
      }
    } catch (error: any) {
      throw new Error(`GitHub 文件元数据获取失败: ${error?.message || error}`)
    }
  }

  /**
   * 获取存储类型
   * @returns 存储类型
   */
  getType(): StorageType {
    return StorageType.GITHUB
  }

  /**
   * 构建文件路径
   * @param remotePath 远程路径
   * @returns 构建后的路径
   */
  private buildFilePath(remotePath: string): string {
    if (!this.githubConfig?.path) {
      return remotePath
    }

    if (!remotePath) {
      return this.githubConfig.path
    }

    return `${this.githubConfig.path}/${remotePath}`
  }

  /**
   * 获取仓库的最新提交信息
   * @returns 最新提交的 SHA
   */
  async getLatestCommitSha(): Promise<string> {
    try {
      this.checkConnection()

      if (!this.octokit || !this.githubConfig) {
        throw new Error('GitHub 客户端未初始化')
      }

      const branch = this.githubConfig.branch || 'main'

      const response = await this.octokit.rest.repos.getBranch({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo,
        branch: branch
      })

      return response.data.commit.sha
    } catch (error: any) {
      throw new Error(`获取最新提交失败: ${error?.message || error}`)
    }
  }

  /**
   * 创建分支
   * @param branchName 分支名称
   * @param fromBranch 源分支
   * @returns 是否创建成功
   */
  async createBranch(branchName: string, fromBranch: string = 'main'): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.octokit || !this.githubConfig) {
        throw new Error('GitHub 客户端未初始化')
      }

      // 获取源分支的最新提交
      const fromBranchResponse = await this.octokit.rest.repos.getBranch({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo,
        branch: fromBranch
      })

      // 创建新分支
      await this.octokit.rest.git.createRef({
        owner: this.githubConfig.owner,
        repo: this.githubConfig.repo,
        ref: `refs/heads/${branchName}`,
        sha: fromBranchResponse.data.commit.sha
      })

      return true
    } catch (error) {
      console.error('创建分支失败:', error)
      return false
    }
  }
}
