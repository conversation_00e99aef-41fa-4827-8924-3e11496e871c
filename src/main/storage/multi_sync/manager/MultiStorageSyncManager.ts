/**
 * 多存储后端数据同步系统 - 多存储同步管理器
 * 功能：管理多个存储后端的同步操作和配置
 * 依赖：存储适配器工厂、数据库管理器
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { EventEmitter } from 'events'
import { StorageAdapterFactory } from '../factory/StorageAdapterFactory'
import { IStorageAdapter, StorageType, StorageConfigWrapper, SyncProgress, SyncStatus, SyncResult } from '../types/StorageTypes'

/**
 * 多存储同步管理器
 * 负责管理多个存储后端的配置、连接和同步操作
 */
export class MultiStorageSyncManager extends EventEmitter {
  private static instance: MultiStorageSyncManager | null = null
  private factory: StorageAdapterFactory
  private adapters: Map<string, IStorageAdapter> = new Map()
  private configs: Map<string, StorageConfigWrapper> = new Map()
  private syncProgress: Map<string, SyncProgress> = new Map()
  private activeSyncs: Set<string> = new Set()

  /**
   * 获取管理器单例实例
   * @returns 管理器实例
   */
  static getInstance(): MultiStorageSyncManager {
    if (!MultiStorageSyncManager.instance) {
      MultiStorageSyncManager.instance = new MultiStorageSyncManager()
    }
    return MultiStorageSyncManager.instance
  }

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    super()
    this.factory = StorageAdapterFactory.getInstance()
  }

  /**
   * 添加存储配置
   * @param config 存储配置
   * @returns 配置ID
   */
  async addStorageConfig(config: Omit<StorageConfigWrapper, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const configId = this.generateConfigId()
    const now = new Date()

    const fullConfig: StorageConfigWrapper = {
      ...config,
      id: configId,
      createdAt: now,
      updatedAt: now
    }

    // 验证配置
    const validation = this.factory.validateConfig(config.type, config.config)
    if (!validation.valid) {
      throw new Error(`配置验证失败: ${validation.errors.join(', ')}`)
    }

    // 保存配置
    this.configs.set(configId, fullConfig)

    // 触发配置添加事件
    this.emit('configAdded', fullConfig)

    return configId
  }

  /**
   * 更新存储配置
   * @param configId 配置ID
   * @param updates 更新内容
   * @returns 是否更新成功
   */
  async updateStorageConfig(configId: string, updates: Partial<StorageConfigWrapper>): Promise<boolean> {
    const existingConfig = this.configs.get(configId)
    if (!existingConfig) {
      throw new Error(`配置不存在: ${configId}`)
    }

    // 如果更新了配置内容，需要验证
    if (updates.config) {
      const validation = this.factory.validateConfig(existingConfig.type, updates.config)
      if (!validation.valid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`)
      }
    }

    // 更新配置
    const updatedConfig: StorageConfigWrapper = {
      ...existingConfig,
      ...updates,
      id: configId, // 确保ID不被更改
      createdAt: existingConfig.createdAt, // 确保创建时间不被更改
      updatedAt: new Date()
    }

    this.configs.set(configId, updatedConfig)

    // 如果适配器已连接，需要重新连接
    if (this.adapters.has(configId)) {
      await this.disconnectAdapter(configId)
      if (updatedConfig.enabled) {
        await this.connectAdapter(configId)
      }
    }

    // 触发配置更新事件
    this.emit('configUpdated', updatedConfig)

    return true
  }

  /**
   * 删除存储配置
   * @param configId 配置ID
   * @returns 是否删除成功
   */
  async removeStorageConfig(configId: string): Promise<boolean> {
    const config = this.configs.get(configId)
    if (!config) {
      return false
    }

    // 断开连接
    await this.disconnectAdapter(configId)

    // 删除配置
    this.configs.delete(configId)
    this.syncProgress.delete(configId)

    // 触发配置删除事件
    this.emit('configRemoved', config)

    return true
  }

  /**
   * 获取存储配置
   * @param configId 配置ID
   * @returns 存储配置
   */
  getStorageConfig(configId: string): StorageConfigWrapper | undefined {
    return this.configs.get(configId)
  }

  /**
   * 获取所有存储配置
   * @returns 所有存储配置
   */
  getAllStorageConfigs(): StorageConfigWrapper[] {
    return Array.from(this.configs.values())
  }

  /**
   * 连接存储适配器
   * @param configId 配置ID
   * @returns 是否连接成功
   */
  async connectAdapter(configId: string): Promise<boolean> {
    const config = this.configs.get(configId)
    if (!config) {
      throw new Error(`配置不存在: ${configId}`)
    }

    try {
      // 创建适配器
      const adapter = this.factory.createAdapter(config.type)

      // 连接适配器
      const connected = await adapter.connect(config.config)
      if (connected) {
        this.adapters.set(configId, adapter)

        // 更新同步进度
        this.updateSyncProgress(configId, {
          configId,
          status: SyncStatus.IDLE,
          progress: 0,
          totalFiles: 0,
          processedFiles: 0,
          message: '已连接'
        })

        // 触发连接成功事件
        this.emit('adapterConnected', configId, config)

        return true
      } else {
        throw new Error('连接失败')
      }
    } catch (error: any) {
      // 更新同步进度
      this.updateSyncProgress(configId, {
        configId,
        status: SyncStatus.ERROR,
        progress: 0,
        totalFiles: 0,
        processedFiles: 0,
        error: error?.message || error
      })

      // 触发连接失败事件
      this.emit('adapterConnectionFailed', configId, error)

      return false
    }
  }

  /**
   * 断开存储适配器
   * @param configId 配置ID
   * @returns 是否断开成功
   */
  async disconnectAdapter(configId: string): Promise<boolean> {
    const adapter = this.adapters.get(configId)
    if (!adapter) {
      return true // 已经断开
    }

    try {
      await adapter.disconnect()
      this.adapters.delete(configId)

      // 更新同步进度
      this.updateSyncProgress(configId, {
        configId,
        status: SyncStatus.IDLE,
        progress: 0,
        totalFiles: 0,
        processedFiles: 0,
        message: '已断开'
      })

      // 触发断开连接事件
      this.emit('adapterDisconnected', configId)

      return true
    } catch (error: any) {
      console.error(`断开适配器连接失败 (${configId}):`, error)
      return false
    }
  }

  /**
   * 测试存储连接
   * @param configId 配置ID
   * @returns 连接测试结果
   */
  async testConnection(configId: string): Promise<boolean> {
    const adapter = this.adapters.get(configId)
    if (!adapter) {
      // 尝试临时连接
      const config = this.configs.get(configId)
      if (!config) {
        return false
      }

      const tempAdapter = this.factory.createAdapter(config.type)
      try {
        const connected = await tempAdapter.connect(config.config)
        if (connected) {
          const testResult = await tempAdapter.testConnection()
          await tempAdapter.disconnect()
          return testResult
        }
        return false
      } catch (error: any) {
        return false
      }
    }

    return await adapter.testConnection()
  }

  /**
   * 开始同步
   * @param configId 配置ID
   * @param localPath 本地路径
   * @param remotePath 远程路径
   * @returns 同步结果
   */
  async startSync(configId: string, localPath: string, remotePath: string = ''): Promise<SyncResult> {
    if (this.activeSyncs.has(configId)) {
      throw new Error('该存储后端正在同步中')
    }

    const adapter = this.adapters.get(configId)
    if (!adapter) {
      throw new Error('存储适配器未连接')
    }

    try {
      this.activeSyncs.add(configId)

      // 更新同步进度
      this.updateSyncProgress(configId, {
        configId,
        status: SyncStatus.SYNCING,
        progress: 0,
        totalFiles: 0,
        processedFiles: 0,
        message: '开始同步...'
      })

      // 触发同步开始事件
      this.emit('syncStarted', configId)

      // 执行同步
      const result = await adapter.sync(localPath, remotePath)

      // 更新同步进度
      this.updateSyncProgress(configId, {
        configId,
        status: result.success ? SyncStatus.COMPLETED : SyncStatus.ERROR,
        progress: 100,
        totalFiles: result.filesUploaded + result.filesDownloaded + result.filesDeleted,
        processedFiles: result.filesUploaded + result.filesDownloaded + result.filesDeleted,
        message: result.message,
        error: result.success ? undefined : result.errors.join('; ')
      })

      // 触发同步完成事件
      this.emit('syncCompleted', configId, result)

      return result
    } catch (error: any) {
      // 更新同步进度
      this.updateSyncProgress(configId, {
        configId,
        status: SyncStatus.ERROR,
        progress: 0,
        totalFiles: 0,
        processedFiles: 0,
        error: error?.message || error
      })

      // 触发同步失败事件
      this.emit('syncFailed', configId, error)

      throw error
    } finally {
      this.activeSyncs.delete(configId)
    }
  }

  /**
   * 停止同步
   * @param configId 配置ID
   * @returns 是否停止成功
   */
  async stopSync(configId: string): Promise<boolean> {
    if (!this.activeSyncs.has(configId)) {
      return true // 没有在同步
    }

    // 这里可以实现同步中断逻辑
    // 目前简单地从活动同步集合中移除
    this.activeSyncs.delete(configId)

    // 更新同步进度
    this.updateSyncProgress(configId, {
      configId,
      status: SyncStatus.IDLE,
      progress: 0,
      totalFiles: 0,
      processedFiles: 0,
      message: '同步已停止'
    })

    // 触发同步停止事件
    this.emit('syncStopped', configId)

    return true
  }

  /**
   * 获取同步进度
   * @param configId 配置ID
   * @returns 同步进度
   */
  getSyncProgress(configId: string): SyncProgress | undefined {
    return this.syncProgress.get(configId)
  }

  /**
   * 获取所有同步进度
   * @returns 所有同步进度
   */
  getAllSyncProgress(): SyncProgress[] {
    return Array.from(this.syncProgress.values())
  }

  /**
   * 更新同步进度
   * @param configId 配置ID
   * @param progress 进度信息
   */
  private updateSyncProgress(configId: string, progress: SyncProgress): void {
    this.syncProgress.set(configId, progress)
    this.emit('progressUpdated', progress)
  }

  /**
   * 生成配置ID
   * @returns 配置ID
   */
  private generateConfigId(): string {
    return `config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取支持的存储类型
   * @returns 支持的存储类型列表
   */
  getSupportedStorageTypes(): StorageType[] {
    return this.factory.getSupportedTypes()
  }

  /**
   * 获取存储类型信息
   * @param type 存储类型
   * @returns 存储类型信息
   */
  getStorageTypeInfo(type: StorageType) {
    return {
      type,
      displayName: this.factory.getTypeDisplayName(type),
      description: this.factory.getTypeDescription(type),
      icon: this.factory.getTypeIcon(type),
      configFields: this.factory.getTypeConfigFields(type)
    }
  }

  /**
   * 批量同步所有启用的存储后端
   * @param localPath 本地路径
   * @param remotePath 远程路径
   * @returns 同步结果映射
   */
  async syncAll(localPath: string, remotePath: string = ''): Promise<Map<string, SyncResult>> {
    const results = new Map<string, SyncResult>()
    const enabledConfigs = Array.from(this.configs.values()).filter((config) => config.enabled)

    // 并行执行同步
    const syncPromises = enabledConfigs.map(async (config) => {
      try {
        const result = await this.startSync(config.id, localPath, remotePath)
        results.set(config.id, result)
      } catch (error: any) {
        results.set(config.id, {
          success: false,
          message: error?.message || error,
          filesUploaded: 0,
          filesDownloaded: 0,
          filesDeleted: 0,
          errors: [error?.message || error],
          conflicts: []
        })
      }
    })

    await Promise.all(syncPromises)
    return results
  }

  /**
   * 销毁管理器
   */
  async destroy(): Promise<void> {
    // 停止所有同步
    for (const configId of this.activeSyncs) {
      await this.stopSync(configId)
    }

    // 断开所有连接
    for (const configId of this.adapters.keys()) {
      await this.disconnectAdapter(configId)
    }

    // 清理数据
    this.configs.clear()
    this.adapters.clear()
    this.syncProgress.clear()
    this.activeSyncs.clear()

    // 移除所有监听器
    this.removeAllListeners()
  }
}

// 导出管理器单例实例
export const multiStorageSyncManager = MultiStorageSyncManager.getInstance()
