/**
 * 多存储后端数据同步系统 - 类型定义
 * 功能：定义存储适配器接口和配置类型
 * 依赖：无
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

/**
 * 存储类型枚举
 */
export enum StorageType {
  ONEDRIVE = 'onedrive',
  GITHUB = 'github',
  MINIO = 'minio',
  SMB = 'smb',
  SFTP = 'sftp'
}

/**
 * 文件信息接口
 */
export interface FileInfo {
  name: string
  path: string
  size: number
  lastModified: Date
  isDirectory: boolean
  etag?: string
  checksum?: string
}

/**
 * 文件元数据接口
 */
export interface FileMetadata {
  name: string
  path: string
  size: number
  lastModified: Date
  contentType?: string
  etag?: string
  checksum?: string
  version?: string
}

/**
 * 同步结果接口
 */
export interface SyncResult {
  success: boolean
  message: string
  filesUploaded: number
  filesDownloaded: number
  filesDeleted: number
  errors: string[]
  conflicts: ConflictInfo[]
}

/**
 * 冲突信息接口
 */
export interface ConflictInfo {
  filePath: string
  localMetadata: FileMetadata
  remoteMetadata: FileMetadata
  conflictType: 'modified' | 'deleted' | 'created'
}

/**
 * OneDrive 配置接口
 */
export interface OneDriveConfig {
  clientId: string
  tenantId: string
  redirectUri: string
  accessToken?: string
  refreshToken?: string
  folderPath?: string
}

/**
 * GitHub 配置接口
 */
export interface GitHubConfig {
  owner: string
  repo: string
  token: string
  branch?: string
  path?: string
}

/**
 * MinIO 配置接口
 */
export interface MinIOConfig {
  endpoint: string
  accessKey: string
  secretKey: string
  bucket: string
  region?: string
  useSSL?: boolean
  port?: number
}

/**
 * SMB 配置接口
 */
export interface SMBConfig {
  host: string
  port?: number
  username: string
  password: string
  share: string
  domain?: string
  path?: string
}

/**
 * SFTP 配置接口
 */
export interface SFTPConfig {
  host: string
  port?: number
  username: string
  password?: string
  privateKey?: string
  passphrase?: string
  path?: string
}

/**
 * 存储配置联合类型
 */
export type StorageConfig = OneDriveConfig | GitHubConfig | MinIOConfig | SMBConfig | SFTPConfig

/**
 * 存储配置包装器接口
 */
export interface StorageConfigWrapper {
  id: string
  name: string
  type: StorageType
  config: StorageConfig
  enabled: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * 同步状态枚举
 */
export enum SyncStatus {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  SYNCING = 'syncing',
  COMPLETED = 'completed',
  ERROR = 'error',
  CONFLICT = 'conflict'
}

/**
 * 同步进度信息接口
 */
export interface SyncProgress {
  configId: string
  status: SyncStatus
  progress: number // 0-100
  currentFile?: string
  totalFiles: number
  processedFiles: number
  message?: string
  error?: string
}

/**
 * 存储适配器接口
 */
export interface IStorageAdapter {
  /**
   * 连接到存储服务
   * @param config 存储配置
   * @returns 连接是否成功
   */
  connect(config: StorageConfig): Promise<boolean>

  /**
   * 断开连接
   */
  disconnect(): Promise<void>

  /**
   * 测试连接
   * @returns 连接是否正常
   */
  testConnection(): Promise<boolean>

  /**
   * 上传文件
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  uploadFile(localPath: string, remotePath: string): Promise<void>

  /**
   * 下载文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  downloadFile(remotePath: string, localPath: string): Promise<void>

  /**
   * 删除文件
   * @param remotePath 远程文件路径
   */
  deleteFile(remotePath: string): Promise<void>

  /**
   * 列出文件
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  listFiles(remotePath?: string): Promise<FileInfo[]>

  /**
   * 获取文件元数据
   * @param remotePath 远程文件路径
   * @returns 文件元数据
   */
  getFileMetadata(remotePath: string): Promise<FileMetadata>

  /**
   * 同步文件夹
   * @param localPath 本地文件夹路径
   * @param remotePath 远程文件夹路径
   * @returns 同步结果
   */
  sync(localPath: string, remotePath: string): Promise<SyncResult>

  /**
   * 获取存储类型
   * @returns 存储类型
   */
  getType(): StorageType
}

/**
 * 存储适配器工厂接口
 */
export interface IStorageAdapterFactory {
  /**
   * 创建存储适配器
   * @param type 存储类型
   * @returns 存储适配器实例
   */
  createAdapter(type: StorageType): IStorageAdapter

  /**
   * 获取支持的存储类型
   * @returns 支持的存储类型列表
   */
  getSupportedTypes(): StorageType[]
}
