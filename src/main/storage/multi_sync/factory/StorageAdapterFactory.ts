/**
 * 多存储后端数据同步系统 - 存储适配器工厂
 * 功能：根据存储类型创建相应的存储适配器实例
 * 依赖：各种存储适配器
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { IStorageAdapter, IStorageAdapterFactory, StorageType } from '../types/StorageTypes'
import { OneDriveAdapter } from '../adapters/OneDriveAdapter'
import { GitHubAdapter } from '../adapters/GitHubAdapter'
import { MinIOAdapter } from '../adapters/MinIOAdapter'
import { SMBAdapter } from '../adapters/SMBAdapter'
import { SFTPAdapter } from '../adapters/SFTPAdapter'

/**
 * 存储适配器工厂类
 * 实现工厂模式，根据存储类型创建相应的适配器实例
 */
export class StorageAdapterFactory implements IStorageAdapterFactory {
  private static instance: StorageAdapterFactory | null = null

  /**
   * 获取工厂单例实例
   * @returns 工厂实例
   */
  static getInstance(): StorageAdapterFactory {
    if (!StorageAdapterFactory.instance) {
      StorageAdapterFactory.instance = new StorageAdapterFactory()
    }
    return StorageAdapterFactory.instance
  }

  /**
   * 私有构造函数，实现单例模式
   */
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {}

  /**
   * 创建存储适配器
   * @param type 存储类型
   * @returns 存储适配器实例
   * @throws 如果存储类型不支持则抛出错误
   */
  createAdapter(type: StorageType): IStorageAdapter {
    switch (type) {
      case StorageType.ONEDRIVE:
        return new OneDriveAdapter()

      case StorageType.GITHUB:
        return new GitHubAdapter()

      case StorageType.MINIO:
        return new MinIOAdapter()

      case StorageType.SMB:
        return new SMBAdapter()

      case StorageType.SFTP:
        return new SFTPAdapter()

      default:
        throw new Error(`不支持的存储类型: ${type}`)
    }
  }

  /**
   * 获取支持的存储类型
   * @returns 支持的存储类型列表
   */
  getSupportedTypes(): StorageType[] {
    return [StorageType.ONEDRIVE, StorageType.GITHUB, StorageType.MINIO, StorageType.SMB, StorageType.SFTP]
  }

  /**
   * 检查是否支持指定的存储类型
   * @param type 存储类型
   * @returns 是否支持
   */
  isTypeSupported(type: StorageType): boolean {
    return this.getSupportedTypes().includes(type)
  }

  /**
   * 获取存储类型的显示名称
   * @param type 存储类型
   * @returns 显示名称
   */
  getTypeDisplayName(type: StorageType): string {
    const displayNames: { [key in StorageType]: string } = {
      [StorageType.ONEDRIVE]: 'OneDrive',
      [StorageType.GITHUB]: 'GitHub',
      [StorageType.MINIO]: 'MinIO',
      [StorageType.SMB]: 'SMB 网络共享',
      [StorageType.SFTP]: 'SFTP'
    }

    return displayNames[type] || type
  }

  /**
   * 获取存储类型的描述
   * @param type 存储类型
   * @returns 描述信息
   */
  getTypeDescription(type: StorageType): string {
    const descriptions: { [key in StorageType]: string } = {
      [StorageType.ONEDRIVE]: 'Microsoft OneDrive 云存储服务，支持 OAuth 认证',
      [StorageType.GITHUB]: 'GitHub 代码仓库，支持 API Token 认证',
      [StorageType.MINIO]: 'MinIO 对象存储，兼容 Amazon S3 API',
      [StorageType.SMB]: 'SMB/CIFS 网络文件共享协议',
      [StorageType.SFTP]: 'SSH 文件传输协议，支持密码和密钥认证'
    }

    return descriptions[type] || '未知存储类型'
  }

  /**
   * 获取存储类型的图标
   * @param type 存储类型
   * @returns 图标名称或 URL
   */
  getTypeIcon(type: StorageType): string {
    const icons: { [key in StorageType]: string } = {
      [StorageType.ONEDRIVE]: 'cloud',
      [StorageType.GITHUB]: 'github',
      [StorageType.MINIO]: 'database',
      [StorageType.SMB]: 'share-alt',
      [StorageType.SFTP]: 'server'
    }

    return icons[type] || 'folder'
  }

  /**
   * 获取存储类型的配置字段
   * @param type 存储类型
   * @returns 配置字段定义
   */
  getTypeConfigFields(type: StorageType): ConfigField[] {
    switch (type) {
      case StorageType.ONEDRIVE:
        return [
          { name: 'clientId', label: '客户端 ID', type: 'text', required: true },
          { name: 'tenantId', label: '租户 ID', type: 'text', required: true, defaultValue: 'common' },
          { name: 'redirectUri', label: '重定向 URI', type: 'text', required: true },
          { name: 'folderPath', label: '文件夹路径', type: 'text', required: false }
        ]

      case StorageType.GITHUB:
        return [
          { name: 'owner', label: '仓库所有者', type: 'text', required: true },
          { name: 'repo', label: '仓库名称', type: 'text', required: true },
          { name: 'token', label: 'API Token', type: 'password', required: true },
          { name: 'branch', label: '分支名称', type: 'text', required: false, defaultValue: 'main' },
          { name: 'path', label: '路径前缀', type: 'text', required: false }
        ]

      case StorageType.MINIO:
        return [
          { name: 'endpoint', label: '端点地址', type: 'text', required: true },
          { name: 'accessKey', label: '访问密钥', type: 'text', required: true },
          { name: 'secretKey', label: '秘密密钥', type: 'password', required: true },
          { name: 'bucket', label: '存储桶', type: 'text', required: true },
          { name: 'region', label: '区域', type: 'text', required: false, defaultValue: 'us-east-1' },
          { name: 'useSSL', label: '使用 SSL', type: 'boolean', required: false, defaultValue: false },
          { name: 'port', label: '端口', type: 'number', required: false }
        ]

      case StorageType.SMB:
        return [
          { name: 'host', label: '主机地址', type: 'text', required: true },
          { name: 'username', label: '用户名', type: 'text', required: true },
          { name: 'password', label: '密码', type: 'password', required: true },
          { name: 'share', label: '共享名称', type: 'text', required: true },
          { name: 'domain', label: '域', type: 'text', required: false, defaultValue: 'WORKGROUP' },
          { name: 'port', label: '端口', type: 'number', required: false, defaultValue: 445 },
          { name: 'path', label: '路径', type: 'text', required: false }
        ]

      case StorageType.SFTP:
        return [
          { name: 'host', label: '主机地址', type: 'text', required: true },
          { name: 'username', label: '用户名', type: 'text', required: true },
          { name: 'password', label: '密码', type: 'password', required: false },
          { name: 'privateKey', label: '私钥', type: 'textarea', required: false },
          { name: 'passphrase', label: '私钥密码', type: 'password', required: false },
          { name: 'port', label: '端口', type: 'number', required: false, defaultValue: 22 },
          { name: 'path', label: '路径', type: 'text', required: false }
        ]

      default:
        return []
    }
  }

  /**
   * 验证配置是否有效
   * @param type 存储类型
   * @param config 配置对象
   * @returns 验证结果
   */
  validateConfig(type: StorageType, config: any): ValidationResult {
    const fields = this.getTypeConfigFields(type)
    const errors: string[] = []

    for (const field of fields) {
      if (field.required && (!config[field.name] || config[field.name].toString().trim() === '')) {
        errors.push(`${field.label} 是必填项`)
      }

      if (field.type === 'number' && config[field.name] && isNaN(Number(config[field.name]))) {
        errors.push(`${field.label} 必须是有效的数字`)
      }
    }

    // 特殊验证
    if (type === StorageType.SFTP) {
      if (!config.password && !config.privateKey) {
        errors.push('密码或私钥必须提供其中一个')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 获取存储类型的默认配置
   * @param type 存储类型
   * @returns 默认配置对象
   */
  getDefaultConfig(type: StorageType): any {
    const fields = this.getTypeConfigFields(type)
    const defaultConfig: any = {}

    for (const field of fields) {
      if (field.defaultValue !== undefined) {
        defaultConfig[field.name] = field.defaultValue
      }
    }

    return defaultConfig
  }
}

/**
 * 配置字段定义接口
 */
export interface ConfigField {
  name: string
  label: string
  type: 'text' | 'password' | 'number' | 'boolean' | 'textarea'
  required: boolean
  defaultValue?: any
  placeholder?: string
  description?: string
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean
  errors: string[]
}

// 导出工厂单例实例
export const storageAdapterFactory = StorageAdapterFactory.getInstance()
