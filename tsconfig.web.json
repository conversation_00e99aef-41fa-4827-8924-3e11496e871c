{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/**/*", "src/renderer/src/env.d.ts", "src/renderer/src/**/*", "src/renderer/src/**/*.vue", "src/preload/*.d.ts"], "exclude": ["node_modules", "dist"], "compilerOptions": {"composite": true, "allowJs": true, "baseUrl": ".", "outDir": "dist/renderer", "noEmit": false, "paths": {"@renderer/*": ["src/renderer/src/*"], "@views/*": ["src/renderer/src/views/*"], "@router/*": ["src/renderer/src/router/*"], "@store/*": ["src/renderer/src/store/*"], "@utils/*": ["src/renderer/src/utils/*"], "@api/*": ["src/renderer/src/api/*"], "@config/*": ["src/renderer/src/config/*"], "@/*": ["src/renderer/src/*"]}}}