<!doctype html>
<html class="theme-dark">
  <head>
    <meta charset="UTF-8" />
    <title>Chaterm</title>
    <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
    <!-- <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:"
    /> -->
    <script>
      ;(function () {
        const theme = localStorage.getItem('theme') || 'auto'

        // Helper function to get actual theme based on system preference for auto mode
        const getActualTheme = (theme) => {
          if (theme === 'auto') {
            // Check system preference first
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
              return 'dark'
            } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) {
              return 'light'
            }
            // Default to light theme if no system preference is detected
            return 'light'
          }
          return theme
        }

        const actualTheme = getActualTheme(theme)
        document.documentElement.className = `theme-${actualTheme}`
      })()
    </script>
  </head>

  <body>
    <div id="app"></div>
    <script
      type="module"
      src="/src/main.ts"
    ></script>
  </body>
</html>
