export const shellCommands = [
  ',',
  '!',
  '[',
  '[[',
  '^',
  '$',
  '2to3',
  '7z',
  '7za',
  '7zr',
  'a2ping',
  'aapt',
  'ab',
  'abduco',
  'ac',
  'accelerate',
  'ack',
  'acme.sh-dns',
  'acme.sh',
  'act',
  'acyclic',
  'adb-devices',
  'adb-install',
  'adb-logcat',
  'adb-reboot',
  'adb-reverse',
  'adb-shell',
  'adb',
  'adguardhome',
  'adscript',
  'afconvert',
  'ag',
  'agate',
  'age-keygen',
  'age',
  'aircrack-ng',
  'airdecap-ng',
  'aireplay-ng',
  'airmon-ng',
  'airodump-ng',
  'airpaste',
  'airshare',
  'ajson',
  'alacritty',
  'alex',
  'alias',
  'amass',
  'androguard',
  'ani-cli',
  'anki',
  'ansible-galaxy',
  'ansible-playbook',
  'ansible',
  'ansiweather',
  'ant',
  'apg',
  'apktool',
  'apm',
  'apropos',
  'ar',
  'arch',
  'aria2',
  'aria2c',
  'arp-scan',
  'arp',
  'arthas-trace',
  'arthas-watch',
  'arthas',
  'asar',
  'asciinema',
  'asdf',
  'aspell',
  'at',
  'atom',
  'atuin',
  'autoflake',
  'autojump',
  'awk',
  'axel',
  'babel',
  'banner',
  'base32',
  'base64',
  'basename',
  'bash',
  'bashmarks',
  'bat',
  'bc',
  'bcomps',
  'behat',
  'bg',
  'binwalk',
  'bmaptool',
  'bower',
  'bpytop',
  'brew',
  'browser-sync',
  'btm',
  'btop',
  'buku',
  'bun',
  'carbon-now',
  'cargo-add',
  'cargo-bench',
  'cargo-build',
  'cargo-check',
  'cargo-clean',
  'cargo-clippy',
  'cargo-doc',
  'cargo-fetch',
  'cargo-fix',
  'cargo-fmt',
  'cargo-generate-lockfile',
  'cargo-help',
  'cargo-init',
  'cargo-install',
  'cargo-locate-project',
  'cargo-login',
  'cargo-logout',
  'cargo-metadata',
  'cargo-new',
  'cargo-owner',
  'cargo-package',
  'cargo-pkgid',
  'cargo-publish',
  'cargo-remove',
  'cargo-report',
  'cargo-run',
  'cargo-rustc',
  'cargo-rustdoc',
  'cargo-search',
  'cargo-test',
  'cargo-tree',
  'cargo-uninstall',
  'cargo-update',
  'cargo-vendor',
  'cargo-verify-project',
  'cargo-version',
  'cargo-yank',
  'cargo',
  'case',
  'cat',
  'ccomps',
  'cd',
  'chmod',
  'chown',
  'chroot',
  'clang-cpp',
  'clang',
  'clang++',
  'clear',
  'clojure',
  'code',
  'cola',
  'command',
  'compgen',
  'cp',
  'curl',
  'cut',
  'deno',
  'df',
  'diff',
  'direnv',
  'docker-build',
  'docker-run',
  'docker',
  'du',
  'duf',
  'echo',
  'edgepaint',
  'espanso',
  'etcd',
  'eval',
  'exit',
  'export',
  'fastapi',
  'fastboot',
  'fc-cache',
  'fc-list',
  'fc',
  'fd',
  'feh',
  'ffmpeg',
  'ffsend',
  'file',
  'find',
  'firefox',
  'fish',
  'fossil-ci',
  'fossil-delete',
  'fossil-forget',
  'fossil-new',
  'fzf',
  'g++',
  'gcc',
  'gendesk',
  'gh-cs',
  'gh-gist',
  'gh-repo',
  'git-add',
  'git-alias',
  'git-am',
  'git-annex',
  'git-annotate',
  'git-apply',
  'git-archive-file',
  'git-archive',
  'git-authors',
  'git-bisect',
  'git-blame-someone-else',
  'git-blame',
  'git-branch',
  'git-browse-ci',
  'git-browse',
  'git-brv',
  'git-bug',
  'git-bugreport',
  'git-bulk',
  'git-bundle',
  'git-cat-file',
  'git-changelog',
  'git-check-attr',
  'git-check-ignore',
  'git-check-mailmap',
  'git-check-ref-format',
  'git-checkout-index',
  'git-checkout',
  'git-cherry-pick',
  'git-cherry',
  'git-clean',
  'git-clear-soft',
  'git-clear',
  'git-cliff',
  'git-clone',
  'git-coauthor',
  'git-cola',
  'git-column',
  'git-commit-graph',
  'git-commit-tree',
  'git-commit',
  'git-commits-since',
  'git-config',
  'git-contrib',
  'git-count-objects',
  'git-count',
  'git-cp',
  'git-create-branch',
  'git-credential-cache',
  'git-credential-store',
  'git-credential',
  'git-diff',
  'git-help',
  'git-init',
  'git-log',
  'git-merge',
  'git-pull',
  'git-push',
  'git-rebase',
  'git-remote',
  'git-rename-branch',
  'git-status',
  'git-switch',
  'git',
  'glances',
  'glow',
  'gnmic-sub',
  'go-bug',
  'go-build',
  'go-clean',
  'go-doc',
  'go-env',
  'go',
  'google-chrome',
  'gpg-zip',
  'gpg',
  'gpgv',
  'grep',
  'gunicorn',
  'gvcolor',
  'gvpack',
  'gzip',
  'hashcat',
  'helix',
  'heroku',
  'hexo',
  'hostname',
  'htop',
  'hx',
  'i3',
  'ifconfig',
  'imgcat',
  'install',
  'jadx',
  'jar',
  'jarsigner',
  'java',
  'javac',
  'javadoc',
  'javap',
  'jbang',
  'jc',
  'jcal',
  'jdeps',
  'jdupes',
  'jekyll',
  'jello',
  'jenv',
  'jest',
  'jetifier',
  'jf',
  'jfrog',
  'jhat',
  'jhipster',
  'jhsdb',
  'jigsaw',
  'jmap',
  'jmeter',
  'jmtpfs',
  'jobs',
  'joe',
  'john',
  'join',
  'josm',
  'jp2a',
  'jpegoptim',
  'jpegtopnm',
  'jps',
  'jq',
  'jrnl',
  'json5',
  'jstack',
  'jtbl',
  'julia',
  'jupyter-lab',
  'jupyter',
  'jupyterlab',
  'jupytext',
  'just.1',
  'just',
  'jwt',
  'kafkacat',
  'kill',
  'killall',
  'kitex',
  'less',
  'llvm-ar',
  'llvm-g++',
  'llvm-gcc',
  'llvm-nm',
  'llvm-objdump',
  'llvm-strings',
  'ln',
  'logger',
  'ls',
  'lsof',
  'lzcat',
  'lzma',
  'make',
  'man',
  'matlab',
  'md5sum',
  'mingle',
  'mkdir',
  'mkfile',
  'mongod',
  'more',
  'mpv',
  'mscore',
  'mv',
  'mvn',
  'mysql',
  'n',
  'nano',
  'neofetch',
  'netstat',
  'ninja',
  'nload',
  'nm-classic',
  'nmap',
  'node',
  'nop',
  'npm',
  'ntl',
  'nvim',
  'oathtool',
  'paste',
  'pdfgrep',
  'picocom',
  'ping',
  'pio-init',
  'piodebuggdb',
  'pip-install',
  'pip',
  'platformio',
  'pm2',
  'pnpm',
  'popd',
  'powershell',
  'print',
  'print.zsh',
  'printenv',
  'printf',
  'protoc',
  'ps',
  'ptpython3',
  'pushd',
  'pwd',
  'pwgen',
  'pyenv',
  'python',
  'python3',
  'q',
  'qalc',
  'qc',
  'qcp',
  'qdbus',
  'qemu-img',
  'qemu',
  'qmmp',
  'qmv',
  'qoitopam',
  'qownnotes',
  'qpdf',
  'qr',
  'qrencode',
  'qrttoppm',
  'qtcreator',
  'quarkus',
  'quarto',
  'quilt',
  'quota',
  'qutebrowser',
  'r2',
  'rcat',
  'read',
  'rm',
  'rmdir',
  'rsync',
  'rubocop',
  'runit',
  'runsv',
  'runsvchdir',
  'runsvdir',
  'rustfmt',
  'sccmap',
  'scp',
  'scrapy',
  'screen',
  'seq',
  'shasum',
  'sleep',
  'sort',
  'spf',
  'ssh-add',
  'ssh-copy-id',
  'ssh',
  'sshuttle',
  'steam',
  'stty',
  'sv',
  'sudo',
  'su',
  'ss',
  'tail',
  'tar',
  'thunderbird',
  'tldr',
  'tldrl',
  'tlmgr-arch',
  'touch',
  'trap',
  'tred',
  'tree',
  'u3d',
  'ufraw-batch',
  'ugrep',
  'ulimit',
  'umask',
  'umount',
  'unalias',
  'uname',
  'unar',
  'unclutter',
  'uncrustify',
  'unexpand',
  'unflatten',
  'unimatrix',
  'uniq',
  'unison',
  'units',
  'unlink',
  'unlzma',
  'unp',
  'unrar',
  'unset',
  'unxz',
  'unzip',
  'unzstd',
  'updog',
  'upt',
  'uptime',
  'upx',
  'users',
  'usql',
  'uudecode',
  'uuencode',
  'uv-python',
  'uv-tool',
  'uv',
  'uvicorn',
  'vi',
  'view',
  'vim',
  'virtualenv',
  'vlc',
  'vue',
  'w',
  'wc',
  'wget',
  'where',
  'which',
  'who',
  'whoami',
  'whois',
  'write',
  'xkill',
  'xz',
  'xzcat',
  'ya',
  'yacas',
  'yacc',
  'yadm-alt',
  'yadm-bootstrap',
  'yadm-clone',
  'yadm-config',
  'yadm-decrypt',
  'yadm-encrypt',
  'yadm-enter',
  'yadm-git-crypt',
  'yadm-gitconfig',
  'yadm-init',
  'yadm-introspect',
  'yadm-list',
  'yadm-perms',
  'yadm-transcrypt',
  'yadm-upgrade',
  'yadm',
  'yank',
  'yapf',
  'yard',
  'yarn-why',
  'yarn',
  'yazi',
  'ybacklight',
  'ybmtopbm',
  'yes',
  'yesod',
  'ykinfo',
  'ykman-config',
  'ykman-fido',
  'ykman-oath',
  'ykman-openpgp',
  'ykman',
  'yolo',
  'you-get',
  'youtube-dl',
  'youtube-viewer',
  'yq',
  'yt-dlp',
  'yuvsplittoppm',
  'yuvtoppm',
  'yuy2topam',
  'z',
  'zapier-analytics',
  'zapier-build',
  'zapier-convert',
  'zapier-init',
  'zapier-push',
  'zapier-scaffold',
  'zapier',
  'zbarimg',
  'zcat',
  'zcmp',
  'zdb',
  'zdiff',
  'zeek',
  'zegrep',
  'zeisstopnm',
  'zek',
  'zellij',
  'zfgrep',
  'zfs',
  'zgrep',
  'zig',
  'zint',
  'zip',
  'zip2john',
  'zipalign',
  'zipcloak',
  'zipgrep',
  'zipinfo',
  'zipnote',
  'zless',
  'zlib-flate',
  'zm',
  'zmore',
  'zmv',
  'znew',
  'zola',
  'zopflipng',
  'zotero',
  'zoxide',
  'zpool',
  'zrun',
  'zsh',
  'zstd',
  'zstdcat',
  'zstdless',
  'zstdmt',
  'zsteg',
  'a2disconf',
  'a2dismod',
  'a2dissite',
  'a2enconf',
  'a2enmod',
  'a2ensite',
  'a2query',
  'abbr',
  'ac',
  'acpi',
  'add-apt-repository',
  'addpart',
  'addr2line',
  'adduser',
  'alpine',
  'alternatives',
  'amixer',
  'anbox',
  'apache2ctl',
  'apk',
  'aplay',
  'apport-bug',
  'apt-add-repository',
  'apt-cache',
  'apt-file',
  'apt-get',
  'apt-key',
  'apt-mark',
  'apt',
  'aptitude',
  'arch-chroot',
  'archey',
  'archinstall',
  'archlinux-java',
  'arecord',
  'arithmetic',
  'ark',
  'as',
  'ascii',
  'asciiart',
  'asterisk',
  'aura',
  'auracle',
  'aurman',
  'aurvote',
  'authconfig',
  'autorandr',
  'avahi-browse',
  'balooctl',
  'batcat',
  'beep',
  'betterlockscreen',
  'bitwise',
  'blkdiscard',
  'blkid',
  'bluetoothctl',
  'bluetoothd',
  'bmon',
  'boltctl',
  'bootctl',
  'bpftrace',
  'brctl',
  'brightnessctl',
  'btrfs-device',
  'btrfs-filesystem',
  'btrfs-scrub',
  'btrfs-subvolume',
  'btrfs',
  'cal',
  'cat',
  'cc',
  'chage',
  'cpuid',
  'cryptsetup-luksformat',
  'cryptsetup',
  'debootstrap',
  'debuild',
  'df',
  'diff3',
  'dmenu',
  'dmesg',
  'dmidecode',
  'dnf',
  'dos2unix',
  'flameshot',
  'flatpak',
  'grub-install',
  'grub-mkconfig',
  'head',
  'hexdump',
  'iostat',
  'ip-route-list',
  'ip',
  'iptables',
  'iwctl',
  'konsole',
  'line',
  'logsave',
  'lsattr',
  'lsb_release',
  'lsblk',
  'lscpu',
  'lspci',
  'ls',
  'lvs',
  'mac2unix',
  'makepkg',
  'mbw',
  'megadl',
  'minicom',
  'mkfs.btrfs',
  'mkfs.cramfs',
  'mkfs.exfat',
  'mkfs.ext4',
  'mkfs.f2fs',
  'mkfs.fat',
  'mkfs',
  'mkfs.minix',
  'mkfs.ntfs',
  'mknod',
  'more',
  'ncal',
  'openvpn3',
  'pacman',
  'paru',
  'poweroff',
  'readelf',
  'reboot',
  'sacctmgr',
  'sed',
  'sleep',
  'systemctl',
  'timedatectl',
  'tmt-run',
  'tmt-try',
  'tmt',
  'ubuntu-bug',
  'ul',
  'unix2dos',
  'unix2mac',
  'wg',
  'xcowsay',
  'yaourt',
  'yay',
  'zypper'
]
