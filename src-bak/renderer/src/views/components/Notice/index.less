.notification {
  background: var(--bg-color-quinary) !important;
  border-radius: 4px !important;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.5) !important;
  width: 420px !important;
  overflow: hidden;
}

.notice-title {
  display: none;
  color: var(--text-color);
  font-weight: 600;
}

.notice-desc {
  color: var(--text-color);
  font-size: 14px;
  line-height: 1;
}

.notice-description {
  width: 320px;
}

.notice-btn-wrap {
  margin-top: 18px;
  text-align: right;
}

.notice-btn {
  background: #0e639c;
  color: #ffffff;
  border: none;
  border-radius: 3px;
  padding: 4px 12px;
  cursor: pointer;
  font-size: 13px;
  height: 30px;
  margin-left: 10px;
}

.notice-btn:hover {
  background: #1177bb;
}

.notice-btn-withe {
  background: #5e5e5e;
  color: #ffffff;
  border: none;
  border-radius: 3px;
  padding: 4px 12px;
  cursor: pointer;
  font-size: 13px;
  height: 30px;
  margin-left: 10px;
}
.notice-btn-withe:hover {
  background: #686868;
}

.notice-progress {
  margin-top: 8px;
}
.ant-notification-notice {
  padding: 15px !important;
}

.ant-notification-notice-icon {
  font-size: 16px !important;
}
.ant-notification-notice-content {
  padding-bottom: 6px;
}

.ant-notification-notice-description {
  margin-inline-start: 30px !important;
}

.ant-notification-notice-close {
  color: var(--text-color);
  :hover {
    color: var(--text-color);
  }
}
.ant-notification-close-x {
  color: var(--text-color);
  :hover {
    color: var(--text-color);
  }
}
.ant-progress-inner {
  background-color: var(--bg-color-tertiary) !important;
}
.no-closeIcon .ant-notification-notice-close {
  display: none !important;
}
