<?xml version="1.0" encoding="UTF-8"?>
<svg width="84px" height="83px" viewBox="0 0 84 83" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Chaterm logo</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#D4EAF2" offset="0%"></stop>
            <stop stop-color="#777775" offset="100%"></stop>
        </linearGradient>
        <path d="M39.9392213,78.2222222 C27.4188034,78.2222222 20.6115859,76.8641975 13.3181387,73.0370371 C7.60493828,69.8271605 4.44444444,60.6913581 4.44444444,48.0987654 C4.44444444,32.5432099 7.72649572,24.1481482 15.3846153,20.3209876 C20.4900285,17.7283951 30.2146249,16 39.9392213,16 C49.6638177,16.1234568 59.3884141,17.8518518 64.6153847,20.5679012 C72.2735044,24.5185185 75.5555556,32.7901234 75.5555556,48.3456791 C75.5555556,60.9382716 72.2735043,70.0740741 66.5603039,73.2839506 C59.1452992,76.9876544 52.4596391,78.2222222 39.9392213,78.2222222 Z" id="path-2"></path>
        <filter x="-7.0%" y="-6.4%" width="114.1%" height="116.1%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#D4EAF2" offset="0%"></stop>
            <stop stop-color="#777775" offset="100%"></stop>
        </linearGradient>
        <rect id="path-5" x="1.77777778" y="37.3333333" width="76.4444444" height="19.5555556" rx="5.33333333"></rect>
        <filter x="-6.5%" y="-20.5%" width="113.1%" height="151.1%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#777775" offset="0%"></stop>
            <stop stop-color="#D4EAF2" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="33.3453971%" cy="50%" fx="33.3453971%" fy="50%" r="53.5681967%" id="radialGradient-8">
            <stop stop-color="#1677FF" offset="0%"></stop>
            <stop stop-color="#020000" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="57.1428571%" gradientTransform="translate(0.5, 0.5), scale(0.875, 1), translate(-0.5, -0.5)" id="radialGradient-9">
            <stop stop-color="#0044A4" offset="0%"></stop>
            <stop stop-color="#000000" offset="100%"></stop>
        </radialGradient>
        <path d="M39.9392213,78.2222222 C27.4188034,78.2222222 20.6115859,76.8641975 13.3181387,73.0370371 C7.60493828,69.8271605 4.44444444,60.6913581 4.44444444,48.0987654 C4.44444444,32.5432099 7.72649572,24.1481482 15.3846153,20.3209876 C20.4900285,17.7283951 30.2146249,16 39.9392213,16 C49.6638177,16.1234568 59.3884141,17.8518518 64.6153847,20.5679012 C72.2735044,24.5185185 75.5555556,32.7901234 75.5555556,48.3456791 C75.5555556,60.9382716 72.2735043,70.0740741 66.5603039,73.2839506 C59.1452992,76.9876544 52.4596391,78.2222222 39.9392213,78.2222222 Z" id="path-10"></path>
        <linearGradient x1="50%" y1="1.23259516e-30%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#D4EAF2" offset="0%"></stop>
            <stop stop-color="#777775" offset="100%"></stop>
        </linearGradient>
        <path d="M26.6666667,24 L26.6666667,24.8888889 C26.6666667,25.3798087 26.2686976,25.7777778 25.7777778,25.7777778 L16.8888889,25.7777778 C16.3979691,25.7777778 16,25.3798087 16,24.8888889 L16,24 C16,23.5090802 16.3979691,23.1111111 16.8888889,23.1111111 L25.7777778,23.1111111 C26.2686976,23.1111111 26.6666667,23.5090802 26.6666667,24 Z M6.08641882,4.16939966 L11.8359538,10.04485 C12.1741062,10.390408 12.1740521,10.9428724 11.835832,11.2883642 L6.08390671,17.1639553 C5.74876348,17.5063041 5.20259914,17.5214585 4.84898793,17.1982205 L4.22130915,16.6244559 C3.85896369,16.293234 3.83373311,15.7309865 4.16495509,15.368641 C4.16787969,15.3654416 4.17082765,15.3622636 4.17379875,15.3591073 L8.01747338,11.2758483 C8.33963903,10.9336014 8.3396751,10.399698 8.0175557,10.0574075 L4.17520723,5.97445206 C3.83876826,5.61694536 3.85584703,5.05439123 4.21335374,4.71795226 C4.21655724,4.71493754 4.21978307,4.71194665 4.22303097,4.70897983 L4.85160932,4.13479898 C5.20529454,3.81172186 5.75138031,3.82702377 6.08641882,4.16939966 Z" id="path-12"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-2-1进入页" transform="translate(-584, -312)">
            <g id="Chaterm-logo" transform="translate(586, 312)">
                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="80" height="80"></rect>
                <g id="路径">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <path stroke="#020000" stroke-width="8" d="M39.9147988,20.0000162 C48.9017752,20.1167819 57.9290299,21.6013073 62.7710297,24.1173173 C65.7687067,25.6637577 67.8603195,28.0790219 69.2408118,31.584178 C70.8722514,35.7265054 71.5555556,41.2115672 71.5555556,48.3456791 C71.5555556,54.1069614 70.8407529,59.0862486 69.4343201,62.9854171 C68.3066175,66.1118393 66.7877763,68.5346687 64.6678412,69.7578335 C57.7922436,73.1712688 51.5405763,74.2222222 39.9392213,74.2222222 C28.3378578,74.2222222 21.9866046,73.0597214 15.2189271,69.5164846 C13.1144362,68.3055933 11.6310218,65.9035557 10.5299306,62.8090934 C9.13789591,58.8969754 8.44444444,53.8927726 8.44444444,48.0987654 C8.44444444,40.9479348 9.13102097,35.4249868 10.7697703,31.2640999 C12.141149,27.7820835 14.2089749,25.3802087 17.1727598,23.8990513 C21.9202419,21.4883133 30.9365615,20.0027219 39.9147988,20.0000162 Z" stroke-linejoin="square" fill="#262626" fill-rule="evenodd"></path>
                    <path stroke="url(#linearGradient-1)" stroke-width="2.66666667" d="M39.9309763,17.3333365 C49.4097151,17.4546036 58.9019325,19.1016598 64.0041003,21.7528523 C67.5550694,23.5847015 70.0767222,26.429568 71.7219797,30.6069796 C73.4485326,34.9908054 74.2222222,40.7876108 74.2222222,48.3456791 C74.2222222,54.4666915 73.4361498,59.7500686 71.9427926,63.8902249 C70.5429065,67.7712437 68.5371585,70.6362736 65.923368,72.1116632 C58.6906773,75.7163989 52.1498277,76.8888889 39.9392213,76.8888889 C27.7173501,76.8888889 21.0613871,75.5944731 13.9388087,71.8562992 C11.3481063,70.3863802 9.38486774,67.5456466 8.01757291,63.7030565 C6.54094272,59.5531947 5.77777778,54.2485595 5.77777778,48.0987654 C5.77777778,40.5248362 6.55472888,34.6893145 8.28860233,30.2869015 C9.93100287,26.1167438 12.4425561,23.2818517 15.9883188,21.509819 C20.9666897,18.98174 30.4549864,17.3343391 39.9309763,17.3333365 Z" stroke-linejoin="square"></path>
                </g>
                <g id="矩形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use fill="url(#linearGradient-4)" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
                <rect id="矩形" fill="url(#linearGradient-7)" transform="translate(48.8889, 11.1111) rotate(15) translate(-48.8889, -11.1111)" x="47.1111111" y="1.77777778" width="3.55555556" height="18.6666667" rx="1.77777778"></rect>
                <circle id="椭圆形" stroke="#D2E7EF" stroke-width="2.22222222" fill="url(#radialGradient-8)" cx="49.7777778" cy="7.11111111" r="5.11111111"></circle>
                <g id="路径">
                    <use fill="#262626" fill-rule="evenodd" xlink:href="#path-10"></use>
                    <path stroke="#020000" stroke-width="8" d="M39.9147988,20.0000162 C48.9017752,20.1167819 57.9290299,21.6013073 62.7710297,24.1173173 C65.7687067,25.6637577 67.8603195,28.0790219 69.2408118,31.584178 C70.8722514,35.7265054 71.5555556,41.2115672 71.5555556,48.3456791 C71.5555556,54.1069614 70.8407529,59.0862486 69.4343201,62.9854171 C68.3066175,66.1118393 66.7877763,68.5346687 64.6678412,69.7578335 C57.7922436,73.1712688 51.5405763,74.2222222 39.9392213,74.2222222 C28.3378578,74.2222222 21.9866046,73.0597214 15.2189271,69.5164846 C13.1144362,68.3055933 11.6310218,65.9035557 10.5299306,62.8090934 C9.13789591,58.8969754 8.44444444,53.8927726 8.44444444,48.0987654 C8.44444444,40.9479348 9.13102097,35.4249868 10.7697703,31.2640999 C12.141149,27.7820835 14.2089749,25.3802087 17.1727598,23.8990513 C21.9202419,21.4883133 30.9365615,20.0027219 39.9147988,20.0000162 Z" stroke-linejoin="square" fill="url(#radialGradient-9)" fill-rule="evenodd"></path>
                    <path stroke="url(#linearGradient-1)" stroke-width="2.66666667" d="M39.9309763,17.3333365 C49.4097151,17.4546036 58.9019325,19.1016598 64.0041003,21.7528523 C67.5550694,23.5847015 70.0767222,26.429568 71.7219797,30.6069796 C73.4485326,34.9908054 74.2222222,40.7876108 74.2222222,48.3456791 C74.2222222,54.4666915 73.4361498,59.7500686 71.9427926,63.8902249 C70.5429065,67.7712437 68.5371585,70.6362736 65.923368,72.1116632 C58.6906773,75.7163989 52.1498277,76.8888889 39.9392213,76.8888889 C27.7173501,76.8888889 21.0613871,75.5944731 13.9388087,71.8562992 C11.3481063,70.3863802 9.38486774,67.5456466 8.01757291,63.7030565 C6.54094272,59.5531947 5.77777778,54.2485595 5.77777778,48.0987654 C5.77777778,40.5248362 6.55472888,34.6893145 8.28860233,30.2869015 C9.93100287,26.1167438 12.4425561,23.2818517 15.9883188,21.509819 C20.9666897,18.98174 30.4549864,17.3343391 39.9309763,17.3333365 Z" stroke-linejoin="square"></path>
                </g>
                <g id="code-logo备份" transform="translate(18.6667, 34.6667)">
                    <rect id="矩形" fill="#FFFFFF" opacity="0" x="0" y="0" width="28.4444444" height="28.4444444"></rect>
                    <g id="形状结合" fill-rule="nonzero">
                        <use fill="#FFFFFF" xlink:href="#path-12"></use>
                        <use fill-opacity="0.5" fill="url(#linearGradient-11)" xlink:href="#path-12"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>