<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>plus</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon" transform="translate(-392, -252)" fill-rule="nonzero">
            <g id="plus" transform="translate(392, 252)">
                <rect id="矩形备份-2" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>
                <path d="M8.58222222,1 C8.68592593,1 8.73777778,1.05185185 8.73777778,1.15555556 L8.737,7.261 L14.5322222,7.26111111 C14.6359259,7.26111111 14.6877778,7.31296296 14.6877778,7.41666667 L14.6877778,8.58333333 C14.6877778,8.68703704 14.6359259,8.73888889 14.5322222,8.73888889 L8.737,8.738 L8.73777778,14.8444444 C8.73777778,14.9481481 8.68592593,15 8.58222222,15 L7.41555556,15 C7.31185185,15 7.26,14.9481481 7.26,14.8444444 L7.26,8.738 L1.46555556,8.73888889 C1.36185185,8.73888889 1.31,8.68703704 1.31,8.58333333 L1.31,7.41666667 C1.31,7.31296296 1.36185185,7.26111111 1.46555556,7.26111111 L7.26,7.261 L7.26,1.15555556 C7.26,1.05185185 7.31185185,1 7.41555556,1 L8.58222222,1 Z" id="形状结合" fill="#FFFFFF"></path>
            </g>
        </g>
    </g>
</svg>